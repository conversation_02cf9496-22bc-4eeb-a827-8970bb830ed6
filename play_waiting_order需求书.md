# play_waiting_order 需求书

## 1. 需求概述

当玩家免费次数用完，再次发起游戏请求，但没有可用订单时，在webdisplay前端显示"player_waiting_order"提示内容。该提示与其他游戏状态提示（如玩家在玩、抓到/没抓到）独立显示，有自己的显示位置和触发时机。

## 2. 功能详细说明

### 2.1 触发条件
- 玩家免费次数已用完（`free_games_used_this_session >= max_free_games_per_session`）
- 玩家再次发起游戏请求（评论内容为数字）
- 没有可用订单（`temp_his_orderID_toUse`、`pending_game_entry_details`、`temp_order_toVerify`等都为None或无效）

### 2.2 显示内容
- 显示内容由配置文件中的`web_display.display_settings.player_waiting_order`部分定义
- 默认显示"{player}免费已用完！"
- 显示持续2秒后自动清除，或被其他消息替换

### 2.3 显示位置
- 显示位置由配置文件中的`center`参数控制，默认为["80%", "42%"]
- 显示宽度由`width`参数控制，默认为"85%"

### 2.4 独立性要求
- 该提示与游戏状态提示（玩家在玩、抓到/没抓到）完全独立
- 可以与其他状态提示同时显示
- 有自己的显示位置、样式和持续时间
- 触发时机基于玩家请求游戏但没有有效订单的事件，而不是跟随游戏轮次的节奏

## 3. 配置参数

`config_play.yaml`中的配置参数：

```yaml
# 玩家等待订单
player_waiting_order: # [动态加载]
  enabled: true # [动态加载]
  template: "{player}免费已用完！" # [动态加载]
  regular_font_file: "DouyinSansBold.otf" # 
  emoji_font_file: "segoe-ui-emoji.ttf" # 
  font_size: "90px" # [动态加载]
  font_color: "#00FF00" # [动态加载]
  stroke_color: "#000000" # [动态加载]
  stroke_width: "10px" # [动态加载]
  shadow_blur: "15px" # 阴影模糊半径，用于OBS兼容性 [动态加载]
  duration: 2  # 持续显示2秒后自动清除 [动态加载]
  z_index: 20 # [动态加载]
  animated: false  # 游戏中提示不使用动画 [动态加载]
  center: ["80%", "42%"] # [动态加载]
  width: "85%" #提示词容器的宽度，可以是百分比或像素值 
```

## 4. 实现方案

### 4.1 后端实现

#### 4.1.1 在 play_processing.py 中添加事件触发

在`process_batch_game_requests`函数中，当检测到玩家免费次数用尽且没有有效订单时，发送`waiting_order`事件：

```python
# 在 process_batch_game_requests 函数中，免费次数用尽时：
elif free_games_used < max_free_games:
    # 免费名额，正常加入队列
    # ...现有代码...
else:
    # 免费次数用尽，存为待处理
    player_info[player_id]['pending_game_entry_details'] = entry
    pending_json = json.dumps(entry, ensure_ascii=False)
    logger.info(f"[游戏请求] 玩家 {name}({player_id}) 免费次数用尽，存储待验证游戏请求")
    if db_sync_manager:
        db_sync_manager.add_sync_task("update_player_order_state", {
            'player_id': player_id,
            'pending_game_entry_details_json': pending_json
        })
    
    # 新增：通知webdisplay显示等待订单提示
    if status_update_event is not None:
        status_update_event.put({
            'type': 'waiting_order',
            'player_id': player_id,
            'player_name': name
        })
    
    processed_player_ids.append(player_id)
    continue
```

#### 4.1.2 在 handle_status_updates_worker 函数中处理事件

```python
# 在 handle_status_updates_worker 函数中添加对新事件类型的处理：
elif update_type == "waiting_order":
    player_name = update.get('player_name', '')
    if web_display and web_display.is_initialized:
        web_display.show_status_message('player_waiting_order', player_name)
    if gui_update_queue:
        gui_update_queue.put({
            'type': 'waiting_order',
            'player_name': player_name
        })
```

### 4.2 前端实现

#### 4.2.1 在 webdisplay_template.html 中添加独立显示区域

```html
<!-- 新增：等待订单提示专用显示区域 -->
<div id="waitingOrderDisplayWrapper">
    <div id="waitingOrderDisplay" class="waiting-order-display"></div>
</div>
```

#### 4.2.2 添加相应的CSS样式

```html
<style>
    /* 等待订单提示样式 - 位置由JS动态设置 */
    #waitingOrderDisplayWrapper {
        position: absolute;
        /* 位置由JS根据配置动态设置 */
        transform: translate(-50%, -50%);
        z-index: {{TEXT_WRAPPER_Z_INDEX}};
        text-align: center;
    }
    
    .waiting-order-display {
        display: inline-block;
        padding: 10px 20px;
        border-radius: 8px;
        visibility: hidden; /* 默认隐藏 */
    }
</style>
```

#### 4.2.3 修改JavaScript处理函数

```javascript
// 特殊处理 player_waiting_order 类型
if (statusType === 'player_waiting_order') {
    const waitingOrderWrapper = document.getElementById('waitingOrderDisplayWrapper');
    const waitingOrderDisplay = document.getElementById('waitingOrderDisplay');
    clearTimeout(activeTimers.waitingOrder); // 清除之前的定时器
    
    // 获取配置
    const config = displaySettings[statusType];
    if (!config || !config.enabled) return;
    
    // 应用位置配置 - 使用config中的center和width参数
    if (config.center && Array.isArray(config.center) && config.center.length === 2) {
        waitingOrderWrapper.style.left = config.center[0];
        waitingOrderWrapper.style.top = config.center[1];
    }
    
    if (config.width) {
        waitingOrderWrapper.style.width = config.width;
    }
    
    // 应用样式
    applyElementStyles(waitingOrderDisplay, config, 'WaitingOrder');
    
    // 设置内容
    let text = config.template || '';
    text = text.replace('{player}', playerName || '玩家');
    waitingOrderDisplay.innerHTML = text.replace(/\n/g, '<br>');
    
    // 显示
    waitingOrderDisplay.style.visibility = 'visible';
    
    // 设置定时器自动隐藏
    if (config.duration > 0) {
        activeTimers.waitingOrder = setTimeout(() => {
            waitingOrderDisplay.style.visibility = 'hidden';
        }, config.duration * 1000);
    }
    
    return; // 提前返回，不处理其他类型
}
```

#### 4.2.4 初始化函数中添加对新显示区域的位置设置

```javascript
// 在页面加载完成后初始化等待订单显示区域的位置
document.addEventListener('DOMContentLoaded', function() {
    // 初始化等待订单显示区域的位置
    const waitingOrderConfig = displaySettings.player_waiting_order;
    if (waitingOrderConfig && waitingOrderConfig.center && 
        Array.isArray(waitingOrderConfig.center) && waitingOrderConfig.center.length === 2) {
        const waitingOrderWrapper = document.getElementById('waitingOrderDisplayWrapper');
        waitingOrderWrapper.style.left = waitingOrderConfig.center[0];
        waitingOrderWrapper.style.top = waitingOrderConfig.center[1];
        
        if (waitingOrderConfig.width) {
            waitingOrderWrapper.style.width = waitingOrderConfig.width;
        }
    }
    
    // 初始化定时器对象
    activeTimers.waitingOrder = null;
});

// 初始化时添加waitingOrder定时器
let activeTimers = { main: null, sub1: null, sub2: null, general: null, waitingOrder: null };
```

#### 4.2.5 在applyDynamicConfigs函数中添加对等待订单显示区域的更新

```javascript
function applyDynamicConfigs(newConfig) {
    // 现有代码...
    
    // 更新等待订单显示区域的配置
    if (newConfig.display_settings && newConfig.display_settings.player_waiting_order) {
        const waitingOrderConfig = newConfig.display_settings.player_waiting_order;
        const waitingOrderWrapper = document.getElementById('waitingOrderDisplayWrapper');
        
        if (waitingOrderConfig.center && 
            Array.isArray(waitingOrderConfig.center) && 
            waitingOrderConfig.center.length === 2) {
            waitingOrderWrapper.style.left = waitingOrderConfig.center[0];
            waitingOrderWrapper.style.top = waitingOrderConfig.center[1];
        }
        
        if (waitingOrderConfig.width) {
            waitingOrderWrapper.style.width = waitingOrderConfig.width;
        }
    }
}
```

## 5. 流程总结

1. **玩家免费次数用尽再次请求时**，`process_batch_game_requests`检测到，向`status_update_event`发送`waiting_order`事件。
2. **handle_status_updates_worker**线程收到事件，调用`web_display.show_status_message('player_waiting_order', player_name)`。
3. **webdisplay子进程**通过socket转发命令到前端页面。
4. **前端JS**根据`status_type`显示`player_waiting_order`配置的内容，在独立的显示区域显示，2秒后自动清除或被新消息替换。

## 6. 注意事项

- 此功能只针对没有可用订单、或订单还未验证的玩家显示提示。
- 有有效订单的玩家不会显示该提示，而是直接排队。
- 提示显示持续2秒，这个时间可在配置文件中调整。
- 提示的位置和样式完全由配置文件控制，可以根据需要调整。
- 该提示与其他游戏状态提示完全独立，可以同时显示。