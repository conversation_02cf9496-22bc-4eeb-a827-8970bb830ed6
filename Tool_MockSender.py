import socket
import json
import time
import ast
import threading
import sys
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from collections import deque

# 配置参数
HOST = '127.0.0.1'  # 服务器地址（修复了错误的IP地址格式，原先是'127.0..0.1'）
PORT = 9999        # 服务器端口
MOCK_FILE = 'MockLiveMSG.txt'  # 模拟消息文件
AUTO_INTERVAL = 7.0  # 自动发送的间隔时间(秒)
ROUTE = '/game-da302d82'  # URL路由，与poll_request.py一致
AUTO_EXIT_DELAY = 2  # 所有消息发送完毕后自动退出的延迟时间（秒）

# 全局变量
messages = []  # 存储从文件读取的消息
current_index = 0  # 当前发送到的消息索引
auto_mode = True  # 默认为自动发送模式
server_running = True  # 服务器运行标志
httpd = None  # HTTP服务器实例
message_ready = False  # 是否有消息准备好发送
last_send_time = 0  # 上次消息发送时间
ready_messages_queue = deque()  # 存储已准备好的消息索引队列

class MockHTTPRequestHandler(BaseHTTPRequestHandler):
    """处理HTTP请求的类"""
    
    def do_GET(self):
        """处理GET请求"""
        global current_index, messages, server_running, httpd, message_ready, ready_messages_queue
        
        # 检查请求路径是否匹配
        if self.path == ROUTE:
            # 如果还有消息可以发送且有消息已准备好
            if current_index < len(messages) and (message_ready or len(ready_messages_queue) > 0):
                # 准备响应数据
                response_messages = []
                
                # 处理自动模式下准备好的单条消息
                if message_ready and current_index < len(messages):
                    message = messages[current_index]
                    try:
                        time_str, msg_str = message.split(": ", 1)
                        msg_dict = ast.literal_eval(msg_str)
                        response_messages.append(msg_dict)
                        print(f"已发送消息 {current_index+1}/{len(messages)}: {msg_dict.get('content', '无内容')}")
                        current_index += 1
                        message_ready = False  # 重置状态
                    except (SyntaxError, ValueError) as e:
                        print(f"解析消息失败: {e}")
                
                # 处理手动模式下队列中的多条消息
                while ready_messages_queue and current_index < len(messages):
                    queue_index = ready_messages_queue.popleft()
                    if queue_index < len(messages):
                        message = messages[queue_index]
                        try:
                            time_str, msg_str = message.split(": ", 1)
                            msg_dict = ast.literal_eval(msg_str)
                            response_messages.append(msg_dict)
                            print(f"已发送队列消息 {queue_index+1}/{len(messages)}: {msg_dict.get('content', '无内容')}")
                            # 更新当前索引为最大发送索引+1
                            current_index = max(current_index, queue_index + 1)
                        except (SyntaxError, ValueError) as e:
                            print(f"解析队列消息失败: {e}")
                
                # 构造响应数据
                response_data = {
                    "code": 0,
                    "message": response_messages
                }
                
                # 发送响应
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
                
                # 如果所有消息都已发送完毕，启动自动退出倒计时
                if current_index >= len(messages) and len(ready_messages_queue) == 0:
                    print(f"\n所有消息已发送完毕！{AUTO_EXIT_DELAY}秒后自动退出...")
                    # 启动退出线程
                    exit_thread = threading.Thread(target=shutdown_server)
                    exit_thread.daemon = True
                    exit_thread.start()
            else:
                # 所有消息都已发送或消息未准备好，返回空消息列表
                response_data = {
                    "code": 0,
                    "message": []
                }
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(response_data).encode('utf-8'))
        else:
            # 路径不匹配
            self.send_error(404, "找不到请求的资源")
    
    def log_message(self, format, *args):
        """重写日志方法，减少控制台输出"""
        return

def load_messages():
    """从文件中加载消息"""
    global messages
    try:
        with open(MOCK_FILE, 'r', encoding='utf-8') as file:
            messages = [line.strip() for line in file if line.strip()]
        print(f"成功加载 {len(messages)} 条消息")
    except Exception as e:
        print(f"加载消息失败: {e}")
        sys.exit(1)

def shutdown_server():
    """关闭服务器并退出程序"""
    global server_running, httpd
    
    # 等待一段时间后关闭服务器
    time.sleep(AUTO_EXIT_DELAY)
    print("正在关闭服务器...")
    server_running = False
    
    if httpd:
        # 关闭HTTP服务器
        httpd.shutdown()
    
    print("程序已完成所有任务，正在退出...")
    # 强制退出程序
    os._exit(0)

def run_server():
    """运行HTTP服务器"""
    global httpd

    server_address = (HOST, PORT)
    httpd = HTTPServer(server_address, MockHTTPRequestHandler)
    print(f"服务器已启动 http://{HOST}:{PORT}{ROUTE}")

    try:
        # 使用 serve_forever，更好响应 Ctrl-C
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n程序被用户终止")
    finally:
        httpd.server_close()
        print("服务器已关闭，程序退出")
        sys.exit(0)

def auto_sender():
    """自动发送消息的线程函数"""
    global current_index, messages, auto_mode, server_running, message_ready, last_send_time
    
    while server_running:
        current_time = time.time()
        # 如果当前索引小于消息总数，且间隔时间已到
        if current_index < len(messages) and (current_time - last_send_time) >= AUTO_INTERVAL:
            # 只有在队列为空时才设置自动消息准备就绪
            if len(ready_messages_queue) == 0:
                message_ready = True  # 标记消息准备好可以发送
                last_send_time = current_time
                print(f"自动模式: 消息准备就绪 ({current_index+1}/{len(messages)})")
        
        # 短暂休眠避免CPU占用过高
        time.sleep(0.1)

def manual_input_handler():
    """处理手动输入的线程函数"""
    global auto_mode, current_index, messages, server_running, message_ready, last_send_time, ready_messages_queue
    
    print("输入模式: 按Enter键立即发送下一条消息，连续按Enter键可准备多条消息，输入'quit'退出")
    print("系统将每 {} 秒自动准备一条消息，无需手动切换模式".format(AUTO_INTERVAL))
    print("连续按Enter键可累积多条消息，下次客户端查询时将一次性发送")
    
    while server_running:
        try:
            cmd = input()
        except EOFError:
            # 捕获到 EOF，退出输入线程
            print("输入流已关闭，停止手动输入处理线程")
            break

        if cmd.lower() == 'quit':
            # 退出程序
            print("用户请求退出程序...")
            server_running = False
            shutdown_server()
        elif current_index < len(messages):
            # 手动触发，将下一条消息索引加入队列
            next_msg_index = current_index + len(ready_messages_queue)
            if next_msg_index < len(messages):
                ready_messages_queue.append(next_msg_index)
                last_send_time = time.time()  # 更新时间戳以不影响自动模式的计时
                print(f"手动触发: 已将消息 {next_msg_index+1}/{len(messages)} 加入发送队列，当前队列长度: {len(ready_messages_queue)}")
            else:
                print("所有消息已加入队列！")
        else:
            print("所有消息已发送完毕！输入'quit'退出程序")

if __name__ == "__main__":
    # 确保os模块被导入，用于强制退出程序

    
    print("启动模拟消息发送器...")
    
    # 加载消息
    load_messages()
    
    print(f"程序将自动每 {AUTO_INTERVAL} 秒准备一条新消息")
    print("按Enter键可以立即准备一条新消息")
    print("连续按Enter键可准备多条消息，将在客户端下次查询时一次性发送")
    print(f"注意: 所有消息发送完毕后，程序将在 {AUTO_EXIT_DELAY} 秒后自动退出")
    
    # 初始化上次发送时间
    last_send_time = time.time() - AUTO_INTERVAL  # 让第一条消息立即准备好
    
    # 启动输入处理线程
    input_thread = threading.Thread(target=manual_input_handler)
    input_thread.daemon = True
    input_thread.start()
    
    # 启动自动发送线程
    auto_thread = threading.Thread(target=auto_sender)
    auto_thread.daemon = True
    auto_thread.start()
    
    # 启动HTTP服务器
    run_server()
