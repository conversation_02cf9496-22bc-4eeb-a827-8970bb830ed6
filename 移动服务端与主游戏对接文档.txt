以下为**机械臂服务端与客户端通信事件对接文档**，结合你当前代码和最新讨论，详细说明所有一级事件的结构、含义及客户端处理建议。

---

# 机械臂服务端-客户端通信事件对接文档

## 1. 通信基础

- **所有事件均为 JSON 字符串，结尾带换行符 `\n`，通过 TCP 发送。**
- 每个事件都包含 `type: "event"` 字段。
- **流程归属请以 `request_id` 字段为准**，客户端需用此字段关联请求与事件。
- 事件结构中如有 `object_id`，仅表示相关物体编号，不用于流程归属。

---

## 2. 一级事件类型及结构

### 2.1 object_picked（抓取结果事件）

- **时机**：每次抓取动作后立即发送，无论抓取成功与否。
- **含义**：汇报本次抓取是否抓到物体。
- **结构**：

```json
{
  "type": "event",
  "request_id": "xxx",         // 客户端请求ID，流程归属标识
  "event_name": "object_picked",
  "data": {
    "success": true,           // true=抓到物体，false=未抓到
    "object_id": 3,            // 被尝试抓取的物体编号
    "message": "螃蟹"          // 成功时为物体名，失败时为失败提示
  }
}
```

- **客户端建议**：用于界面提示、动画、音效等。抓取失败不是异常，仅为游戏结果。

---

### 2.2 hardware_error（硬件故障事件）

- **时机**：运动前或运动中检测到硬件/通信故障（如无法读取电机状态、脉冲、运动超时、硬件报警等）。
- **事件发送时机**：只要运动前或运动中检测到硬件/通信故障，服务端会立即发送 hardware_error 事件，并终止本轮流程，不会再发送 cycle_completed。
- **含义**：流程立即终止，需人工干预或重启。
- **结构**：

```json
{
  "type": "event",
  "request_id": "xxx",
  "event_name": "hardware_error",
  "data": {
    "error_message": "检测到电机硬件故障，已终止本轮操作",
    "faults": [
      {
        "axis": "motor_x",       // 故障轴名
        "fault_type": "hardware_fault", // 故障类型（如 hardware_fault、timeout_fault、comm_error 等）
        "fault_msg": "运动前无法读取初始位置"
      }
      // ...可有多个轴
    ]
  }
}
```

- **多轴同时故障示例**：

```json
{
  "type": "event",
  "request_id": "xxx",
  "event_name": "hardware_error",
  "data": {
    "error_message": "检测到电机硬件故障，已终止本轮操作",
    "faults": [
      {
        "axis": "motor_x",
        "fault_type": "hardware_fault",
        "fault_msg": "运动前无法读取初始位置"
      },
      {
        "axis": "motor_z",
        "fault_type": "timeout_fault",
        "fault_msg": "运动超时未到位"
      }
    ]
  }
}
```

- **故障详情补充说明**：
    - `faults` 数组会包含所有检测到的硬件故障（支持多轴同时故障），每项含 `axis`、`fault_type`、`fault_msg` 字段。
    - `fault_type` 字段用于区分故障类型，如 `hardware_fault`（硬件故障）、`timeout_fault`（超时）、`comm_error`（通信异常）等，便于客户端做不同处理。
    - 服务端会**一次性完整发送所有故障详情**，不会遗漏。
    - 客户端应遍历 `faults` 数组，逐条展示或记录每个轴的详细故障信息，建议在UI或日志中完整展示 `axis`、`fault_type`、`fault_msg`。
    - 若有多轴同时故障，建议分行或分条显示。
    - faults 数组中的每项可能会在未来增加新字段，客户端应容忍并忽略未知字段。

- **客户端建议**：
    - 弹窗提示、锁定操作、提示用户联系维护人员。
    - **收到此事件后，本轮流程已终止，不会再收到 cycle_completed。**
    - 建议客户端在日志中，将 faults 数组中的每个故障分行或分条展示，便于用户快速定位问题。

---

### 2.3 operation_error（流程性错误事件）

- **时机**：流程中出现非硬件类错误（如参数错误、找不到目标、流程异常等）。
- **含义**：流程异常终止，通常会紧跟一个 cycle_completed（status: failed）。
- **结构**：

```json
{
  "type": "event",
  "request_id": "xxx",
  "event_name": "operation_error",
  "data": {
    "error_message": "移动到展示位置失败（抓取失败后）",
    "stage": "moving_to_display_after_pickup_failed", // 错误发生阶段
    "object_id": 3,                                   // 相关物体编号（如有）
    "details": "traceback..."                         // 详细错误信息（可选）
  }
}
```

- **客户端建议**：弹窗提示、流程终止、允许用户重试或退出。**收到此事件后，通常会收到 cycle_completed（status: failed）。**

---

### 2.4 cycle_completed（流程结束事件）

- **时机**：一轮流程（抓取、展示、投放、归位）正常或异常结束时发送。
- **含义**：本轮流程已结束，流程归属以 request_id 区分。
- **结构**：

```json
{
  "type": "event",
  "request_id": "xxx",
  "event_name": "cycle_completed",
  "data": {
    "status": "success", // 仅有 "success" 或 "failed"
    "message": "抓取流程已结束，机械臂已返回起点",
    "final_positions": {
      "X": 123.4,
      "Y": 234.5,
      "Z": 0.0,
      "CAM": 0.0
    }
  }
}
```

- **status 说明**：
    - `"success"`：流程完整走完（无论抓到没抓到物体），都算正常结束。
    - `"failed"`：流程中有流程性异常（如参数错误、流程错误等），提前终止。
- **客户端建议**：用于流程归档、界面状态切换、允许用户发起下一轮操作。**收到 hardware_error 时不会再收到 cycle_completed。**

---

### 2.5 其它辅助事件（可选监听）

| 事件名                  | 说明                                 | 结构示例（data字段）                   |
|------------------------|--------------------------------------|----------------------------------------|
| arm_at_hover_position  | 机械臂到达抓取后悬停判断位置         | { "message": "...", "current_positions": {...} } |
| arm_at_display_position| 机械臂到达展示位置                   | { "has_object": true/false, "message": "..." }  |
| object_selection_changed| 自动切换目标物体时通知               | { "requested_object_id": 1, "actual_object_id": 2, ... } |

- **客户端建议**：用于动画、状态提示、日志等，非主流程控制事件。

---

## 3. 客户端处理建议

- **流程归属**：所有事件均以 `request_id` 作为流程归属标识，客户端需用此字段关联请求与事件。
- **主流程事件**：客户端必须监听并处理 `object_picked`、`hardware_error`、`operation_error`、`cycle_completed`。
- **辅助事件**：如需更丰富的交互体验，可监听辅助事件（如 arm_at_hover_position）。
- **流程终止**：
    - 收到 `hardware_error` 或 `operation_error` 时，流程应终止，等待用户干预或新指令。
    - `cycle_completed` 表示本轮流程已彻底结束，可安全发起下一轮。
    - **收到 hardware_error 后，服务端不会再发送 cycle_completed 事件**，客户端应以此为流程终止信号。如需归档可自行记录。
- **抓取失败不是异常**：抓取失败（object_picked.success=false）属于正常流程，`cycle_completed.status` 仍为 `"success"`。
- **兼容性建议**：客户端应容忍 `faults` 数组中出现新的字段（如 future_version、error_code 等），只需关注已知字段即可，建议日志或UI完整展示所有字段，便于后续扩展和维护。

---

## 4. 事件流举例

### 4.1 正常抓取成功

1. `object_picked`（success: true）
2. `cycle_completed`（status: "success"）

### 4.2 抓取失败但流程正常

1. `object_picked`（success: false）
2. `cycle_completed`（status: "success"）

### 4.3 流程性错误

1. `operation_error`
2. `cycle_completed`（status: "failed"）

### 4.4 硬件故障

1. `hardware_error`（流程立即终止，不再发 cycle_completed）

---

## 5. 字段说明

| 字段名         | 类型     | 说明                                   |
|---------------|----------|----------------------------------------|
| request_id    | string   | 客户端请求ID，**流程归属唯一标识**      |
| object_id     | int      | 相关物体编号，仅作参考                  |
| status        | string   | 流程结束状态："success" 或 "failed"     |
| faults        | array    | 硬件故障详情，数组，每项含 axis、fault_type、fault_msg |
| fault_type    | string   | 故障类型（如 hardware_fault、timeout_fault、comm_error）|
| fault_msg     | string   | 故障详细描述                            |
| stage         | string   | 错误发生阶段，便于定位                  |
| final_positions | object | 流程结束时各轴物理坐标                |

> faults 数组中的每项可能会在未来增加新字段，客户端应容忍并忽略未知字段。

**fault_type 字段常见枚举值：**
- hardware_fault
- timeout_fault
- comm_error

---

## 6. 版本兼容与扩展

- 如需扩展事件类型或字段，请与服务端开发人员协商，保持事件结构一致性。
- 客户端应容忍未知字段，按需解析。

---

如需更详细字段说明或特殊场景说明，请联系服务端开发人员。