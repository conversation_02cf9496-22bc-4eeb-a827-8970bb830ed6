import sqlite3
import time
import yaml
import logging
import threading
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

# 配置日志
logger = logging.getLogger('play_db')

# 全局配置变量
_config = None

def set_config(config):
    """设置配置，由外部调用"""
    global _config, DB_FILE, SESSION_EXPIRE_HOURS
    _config = config
    
    # 更新全局配置变量
    db_config = config.get('database', {})
    DB_FILE = db_config.get('db_file', 'Play_db.db')
    SESSION_EXPIRE_HOURS = db_config.get('session_expire_hours', 24)

def get_config():
    """获取配置，内部使用"""
    global _config
    if (_config is None):
        # 兜底方案：如果没有外部配置，使用默认配置
        return {'database': {'db_file': 'Play_db.db', 'session_expire_hours': 24}}
    return _config

# 读取配置
def load_config() -> Dict[str, Any]:
    """从配置文件加载数据库配置（兜底函数）"""
    try:
        with open('config_play.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        db_config = config.get('database', {})
        return {
            'db_file': db_config.get('db_file', 'Play_db.db'),
            'session_expire_hours': db_config.get('session_expire_hours', 24)
        }
    except Exception as e:
        logger.error(f"加载配置文件时出错: {e}，将使用默认配置")
        return {'db_file': 'Play_db.db', 'session_expire_hours': 24}

# 全局变量初始化
if _config is None:
    config = load_config()
    DB_FILE = config['db_file']
    SESSION_EXPIRE_HOURS = config['session_expire_hours']
else:
    db_config = _config.get('database', {})
    DB_FILE = db_config.get('db_file', 'Play_db.db')
    SESSION_EXPIRE_HOURS = db_config.get('session_expire_hours', 24)

db_lock = threading.Lock()
current_session_id = None

# 通用数据库操作函数
def _db_execute(query_func: Callable, operation_name: str = "数据库操作", 
               commit: bool = True, return_last_row_id: bool = False) -> Any:
    """执行数据库操作的通用函数"""
    with db_lock:
        conn = None
        try:
            conn = sqlite3.connect(DB_FILE, timeout=10)
            result = query_func(conn)
            
            if commit:
                conn.commit()
                
            # 修正：移除错误的 conn.lastrowid 访问
            # lastrowid 应该由 query_func 内部的 cursor 获取并返回
            return result
            
        except sqlite3.Error as e:
            logger.error(f"{operation_name}时出错: {e}")
            if conn and commit:
                conn.rollback()
            return None if return_last_row_id else False
        finally:
            if conn:
                conn.close()

def _db_query(query_func: Callable, operation_name: str = "数据库查询") -> Any:
    """执行数据库查询的通用函数（只读操作）"""
    return _db_execute(query_func, operation_name, commit=False)

def init_db() -> None:
    """初始化数据库，创建必要的表"""
    def create_tables(conn):
        cursor = conn.cursor()
        
        # 表结构定义 - 采纳您的字典化方案
        tables = {
            'sessions': '''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    start_time TEXT NOT NULL,
                    platform TEXT,
                    status TEXT DEFAULT 'active',
                    end_time TEXT
                )
            ''',
            'players': '''
                CREATE TABLE IF NOT EXISTS players (
                    player_id TEXT PRIMARY KEY,
                    session_id INTEGER,
                    plat TEXT,
                    name TEXT,
                    head_img TEXT,
                    come_time TEXT,
                    comments_after_game INTEGER DEFAULT 0,
                    temp_order_toVerify TEXT,
                    temp_his_orderID_toUse TEXT,
                    pending_game_entry_details_json TEXT,
                    free_games_used_this_session INTEGER DEFAULT 0,
                    FOREIGN KEY (session_id) REFERENCES sessions(session_id)
                )
            ''',
            'comments': '''
                CREATE TABLE IF NOT EXISTS comments (
                    comment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER,
                    player_id TEXT,
                    comment_time TEXT,
                    content TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions(session_id),
                    FOREIGN KEY (player_id) REFERENCES players(player_id)
                )
            ''',
            'games': '''
                CREATE TABLE IF NOT EXISTS games (
                    game_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER,
                    player_id TEXT,
                    game_time TEXT,
                    result TEXT,
                    order_ID TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions(session_id),
                    FOREIGN KEY (player_id) REFERENCES players(player_id)
                )
            ''',
            'queue': '''
                CREATE TABLE IF NOT EXISTS queue (
                    queue_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER,
                    player_id TEXT,
                    plat TEXT,
                    dt TEXT,
                    name TEXT,
                    content TEXT,
                    head_img TEXT,
                    order_ID TEXT,
                    possibility REAL,
                    priority REAL,
                    queue_position INTEGER,
                    FOREIGN KEY (session_id) REFERENCES sessions(session_id),
                    FOREIGN KEY (player_id) REFERENCES players(player_id)
                )
            ''',
            'pending_orders': '''
                CREATE TABLE IF NOT EXISTS pending_orders (
                    order_id TEXT PRIMARY KEY,
                    creation_time TEXT,
                    buyer_account TEXT,
                    product_name TEXT,
                    product_quantity INTEGER,
                    order_details_json TEXT,
                    status TEXT DEFAULT 'available'
                )
            '''
        }
        
        # 创建所有表
        for name, sql in tables.items():
            cursor.execute(sql)
            
        return True
    
    result = _db_execute(create_tables, "初始化数据库")
    if result:
        logger.info("数据库初始化成功")

# 会话管理函数
def create_new_session(platform: str = "抖音") -> int:
    """创建新的会话并返回会话ID"""
    global current_session_id
    
    def insert_session(conn):
        cursor = conn.cursor()
        start_time = datetime.now().isoformat()
        cursor.execute(
            "INSERT INTO sessions (start_time, platform, status) VALUES (?, ?, ?)",
            (start_time, platform, 'active')
        )
        # 修正：在 query_func 内部返回 lastrowid
        return cursor.lastrowid
    
    session_id = _db_execute(insert_session, "创建新会话", return_last_row_id=True)
    if session_id:
        current_session_id = session_id
        logger.info(f"已创建新会话，ID: {session_id}")
    return session_id or -1

def _update_session_status(session_id: int, status: str, op_name: str) -> bool:
    """通用会话状态更新函数 - 采纳您的抽象方案"""
    def update_session(conn):
        cursor = conn.cursor()
        if status == 'closed':
            end_time = datetime.now().isoformat()
            cursor.execute(
                "UPDATE sessions SET status = ?, end_time = ? WHERE session_id = ?",
                (status, end_time, session_id)
            )
        else:
            cursor.execute(
                "UPDATE sessions SET status = ?, end_time = NULL WHERE session_id = ?",
                (status, session_id)
            )
        return True
    
    result = _db_execute(update_session, f"{op_name} {session_id}")
    if result:
        logger.info(f"已{op_name}，ID: {session_id}")
    return bool(result)

def close_session(session_id: int) -> bool:
    """关闭指定的会话"""
    return _update_session_status(session_id, 'closed', "关闭会话")

def reactivate_session(session_id: int) -> bool:
    """重新激活已关闭的会话"""
    return _update_session_status(session_id, 'active', "重新激活会话")

def _get_session_by_query(query: str, params: tuple, operation_name: str) -> Optional[Dict[str, Any]]:
    """通用会话查询函数"""
    def query_session(conn):
        cursor = conn.cursor()
        cursor.execute(query, params)
        row = cursor.fetchone()
        if not row:
            return None
        
        return {
            'session_id': row[0],
            'start_time': row[1] if 'start_time_iso' not in operation_name else row[1],
            'start_time_iso': row[1] if 'start_time_iso' in operation_name else row[1],
            'platform': row[2],
            'status': row[3],
            'end_time': row[4]
        }
    
    return _db_query(query_session, operation_name)

def get_last_session() -> Optional[Dict[str, Any]]:
    """获取最近的会话信息"""
    return _get_session_by_query(
        "SELECT session_id, start_time, platform, status, end_time FROM sessions ORDER BY session_id DESC LIMIT 1",
        (),
        "获取最近会话"
    )

def get_session_details(session_id: int) -> Optional[Dict[str, Any]]:
    """获取会话详细信息，包括开始时间ISO格式"""
    return _get_session_by_query(
        "SELECT session_id, start_time, platform, status, end_time FROM sessions WHERE session_id = ?",
        (session_id,),
        "获取会话详细信息_start_time_iso"
    )

def is_session_valid(session_id: int) -> bool:
    """检查会话是否有效（未过期）"""
    def check_validity(conn):
        cursor = conn.cursor()
        cursor.execute("SELECT start_time FROM sessions WHERE session_id = ?", (session_id,))
        row = cursor.fetchone()
        if not row:
            return False
        
        try:
            start_time = datetime.fromisoformat(row[0])
            expire_time = start_time + timedelta(hours=SESSION_EXPIRE_HOURS)
            return datetime.now() <= expire_time
        except ValueError:
            logger.error(f"解析会话开始时间出错: {row[0]}")
            return False
    
    return bool(_db_query(check_validity, f"检查会话 {session_id} 有效性"))

def is_newly_created_session(session_id: int) -> bool:
    """判断指定会话是否为新创建的（无任何数据）"""
    def check_data_exists(conn):
        cursor = conn.cursor()
        for table in ['players', 'comments', 'games']:
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE session_id = ?", (session_id,))
            if cursor.fetchone()[0] > 0:
                return False
        return True
    
    return bool(_db_query(check_data_exists, f"检查会话 {session_id} 是否新创建"))

# 数据操作函数
def add_player(session_id: int, player_id: str, plat: str, name: str, head_img: str, come_time: str, 
              comments_after_game: int = 0, temp_order_toVerify: Optional[str] = None,
              temp_his_orderID_toUse: Optional[str] = None, pending_game_entry_details_json: Optional[str] = None,
              free_games_used_this_session: int = 0) -> bool:
    """添加或更新玩家信息"""
    def upsert_player(conn):
        cursor = conn.cursor()
        cursor.execute("SELECT player_id FROM players WHERE player_id = ?", (player_id,))
        exists = cursor.fetchone()
        
        # 确保字段类型正确
        safe_comments_after_game = comments_after_game if comments_after_game is not None else 0
        safe_free_games_used = free_games_used_this_session if free_games_used_this_session is not None else 0
        
        # 修正：调整参数顺序，确保字段对应正确
        params = (session_id, plat, name, head_img, come_time, safe_comments_after_game, temp_order_toVerify,
                 temp_his_orderID_toUse, pending_game_entry_details_json, safe_free_games_used)
        
        if exists:
            cursor.execute("""
                UPDATE players 
                SET session_id = ?, plat = ?, name = ?, head_img = ?, come_time = ?,
                    comments_after_game = ?, temp_order_toVerify = ?,
                    temp_his_orderID_toUse = ?, pending_game_entry_details_json = ?,
                    free_games_used_this_session = ?
                WHERE player_id = ?
            """, params + (player_id,))
        else:
            cursor.execute("""
                INSERT INTO players (
                    player_id, session_id, plat, name, head_img, come_time, 
                    comments_after_game, temp_order_toVerify, temp_his_orderID_toUse, 
                    pending_game_entry_details_json, free_games_used_this_session
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (player_id,) + params)
        return True
    
    return bool(_db_execute(upsert_player, f"添加/更新玩家 {player_id}"))

def _insert_simple_record(table: str, fields: tuple, values: tuple, operation_name: str) -> bool:
    """通用简单记录插入函数"""
    def insert_record(conn):
        cursor = conn.cursor()
        placeholders = ','.join(['?'] * len(fields))
        cursor.execute(f"INSERT INTO {table} ({','.join(fields)}) VALUES ({placeholders})", values)
        return True
    
    return bool(_db_execute(insert_record, operation_name))

def add_comment(session_id: int, player_id: str, comment_time: str, content: str) -> bool:
    """添加评论记录"""
    return _insert_simple_record(
        'comments',
        ('session_id', 'player_id', 'comment_time', 'content'),
        (session_id, player_id, comment_time, content),
        f"添加评论 {player_id}"
    )

def add_game(session_id: int, player_id: str, game_time: str, result: str, order_ID: Optional[str] = None) -> bool:
    """添加游戏记录"""
    start_time = time.time()
    # 统一处理：如果 order_ID 是 None、空字符串或 "N/A"，都存为 NULL
    order_val = order_ID if (order_ID and order_ID.upper() != "N/A") else None
    success = _insert_simple_record(
        'games',
        ('session_id', 'player_id', 'game_time', 'result', 'order_ID'),
        (session_id, player_id, game_time, result, order_val),
        f"添加游戏记录 {player_id}"
    )
    
    if success:
        elapsed = time.time() - start_time
        logger.info(f"数据库操作: 添加游戏记录完成，耗时 {elapsed:.3f}秒 - 玩家: {player_id}, 结果: {result}, 订单: {order_val or 'NULL'}")
    
    return success

def update_queue(session_id: int, queue_data: List[Tuple]) -> bool:
    """更新队列，先清空后重新添加"""
    start_time = time.time()
    
    def replace_queue(conn):
        cursor = conn.cursor()
        cursor.execute("DELETE FROM queue WHERE session_id = ?", (session_id,))
        
        for position, queue_item in enumerate(queue_data):
            if len(queue_item) == 3:
                entry, possibility, priority = queue_item
                order_ID = None
            else:
                entry, possibility, priority, order_ID = queue_item
            
            # 修正：只解包前6个元素，因为数据库中的entry相关字段只有6个
            # 第7个元素 target_object_name 是在内存中管理的，不存储到数据库
            if len(entry) >= 6:
                plat, dt, name, player_id, content, head_img = entry[:6]
            else:
                # 如果entry元素少于6个，记录错误并跳过此项
                logger.error(f"队列条目 entry 数据不足6个元素，跳过该条目: {entry}")
                continue
            
            cursor.execute("""
                INSERT INTO queue (
                    session_id, player_id, plat, dt, name, content, head_img, 
                    order_ID, possibility, priority, queue_position
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (session_id, player_id, plat, dt, name, content, head_img,
                  order_ID, possibility, priority, position))
        return True
    
    result = _db_execute(replace_queue, f"更新队列")
    if result:
        elapsed = time.time() - start_time
        logger.info(f"数据库操作: 更新队列完成，耗时 {elapsed:.3f}秒")
    
    return bool(result)

# 数据获取函数
def get_player_info(session_id: int) -> Dict[str, Dict[str, Any]]:
    """获取指定会话的所有玩家信息"""
    def query_players(conn):
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT player_id, plat, name, head_img, come_time, 
                   comments_after_game, temp_order_toVerify, 
                   temp_his_orderID_toUse, pending_game_entry_details_json,
                   free_games_used_this_session
            FROM players WHERE session_id = ?
        """, (session_id,))
        
        players = {}
        for row in cursor.fetchall():
            player_data = {
                'plat': row['plat'],
                'name': row['name'],
                'player': row['player_id'],
                'head_img': row['head_img'],
                'come_time': row['come_time'],
                'comments_after_game': row['comments_after_game'] or 0,
                'temp_order_toVerify': row['temp_order_toVerify'],
                'temp_his_orderID_toUse': row['temp_his_orderID_toUse'],
                'pending_game_entry_details': None,
                'free_games_used_this_session': row['free_games_used_this_session'] or 0
            }
            
            # 修正：正确解析JSON字段
            pending_json = row['pending_game_entry_details_json']
            if pending_json:
                try:
                    entry_details = json.loads(pending_json)
                    if isinstance(entry_details, list) and len(entry_details) > 0:
                        player_data['pending_game_entry_details'] = tuple(entry_details)
                except Exception as e:
                    logger.error(f"解析玩家 {row['player_id']} 的待处理游戏请求JSON数据出错: {e}")
            
            players[row['player_id']] = player_data
        
        return players
    
    return _db_query(query_players, f"获取会话 {session_id} 玩家信息") or {}

def _get_session_data(session_id: int, table: str, fields: str, order_by: str = "") -> List[Tuple[Any, ...]]:
    """通用会话数据获取函数"""
    def query_data(conn) -> List[Tuple[Any, ...]]:
        cursor = conn.cursor()
        query = f"SELECT {fields} FROM {table} WHERE session_id = ?"
        if order_by:
            query += f" ORDER BY {order_by}"
        cursor.execute(query, (session_id,))
        return cursor.fetchall()
    
    return _db_query(query_data, f"获取会话 {session_id} {table}数据") or []

def get_player_comments(session_id: int) -> Dict[str, List]:
    """获取指定会话的所有玩家评论"""
    rows = _get_session_data(session_id, 'comments', 'player_id, comment_time, content', 'comment_time')
    
    comments = {}
    for player_id, comment_time, content in rows:
        if player_id not in comments:
            comments[player_id] = []
        comments[player_id].append([comment_time, content])
    
    return comments

def get_player_games(session_id: int) -> Dict[str, List[Tuple[str, str, Optional[str]]]]:
    """获取指定会话的所有玩家游戏记录，包含订单ID"""
    rows = _get_session_data(session_id, 'games', 'player_id, game_time, result, order_ID', 'game_time')
    
    games = {}
    for player_id, game_time, result, order_ID in rows:
        if player_id not in games:
            games[player_id] = []
        games[player_id].append((game_time, result, order_ID))
    
    return games

def reconstruct_queue(session_id: int) -> List:
    """从数据库重建队列"""
    def query_queue(conn):
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT player_id, plat, dt, name, content, head_img, order_ID, possibility, priority
            FROM queue WHERE session_id = ? ORDER BY queue_position
        """, (session_id,))
        
        queue = []
        for row in cursor.fetchall():
            entry = [row['plat'], row['dt'], row['name'], row['player_id'],
                    row['content'], row['head_img']]
            queue.append((entry, row['possibility'], row['priority'], row['order_ID']))
        
        return queue
    
    return _db_query(query_queue, f"重建会话 {session_id} 队列") or []

# 批量查询函数 - 采纳您的循环优化方案
def get_session_info_batch(session_id: Optional[int] = None) -> Dict[str, Any]:
    """批量获取会话信息，减少数据库查询次数"""
    def batch_query(conn):
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        result = {}
        
        # 获取会话信息
        if session_id is None:
            cursor.execute("SELECT session_id, start_time, platform, status, end_time FROM sessions ORDER BY session_id DESC LIMIT 1")
            last_session_row = cursor.fetchone()
            result['last_session'] = dict(last_session_row) if last_session_row else None
            target_session_id = last_session_row['session_id'] if last_session_row else None
        else:
            cursor.execute("SELECT session_id, start_time, platform, status, end_time FROM sessions WHERE session_id = ?", (session_id,))
            session_row = cursor.fetchone()
            result['session_details'] = dict(session_row) if session_row else None
            target_session_id = session_id
        
        if target_session_id:
            # 获取统计信息 - 采纳您的循环优化
            stats = {}
            for table, key in [('players', 'player_count'), ('comments', 'comment_count'), ('games', 'game_count')]:
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE session_id = ?", (target_session_id,))
                stats[key] = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT result, COUNT(*) FROM games 
                WHERE session_id = ? AND result != 'nothing'
                GROUP BY result
            """, (target_session_id,))
            stats['prize_stats'] = {result: count for result, count in cursor.fetchall()}
            result['stats'] = stats
            
            # 检查是否为新会话
            result['is_new_session'] = all(count == 0 for count in [
                stats['player_count'], stats['comment_count'], stats['game_count']
            ])
            
            # 获取最近评论和队列项目
            queries = [
                ("""SELECT c.comment_id, c.player_id, p.name, c.comment_time, c.content
                    FROM comments c JOIN players p ON c.player_id = p.player_id
                    WHERE c.session_id = ? ORDER BY c.comment_time DESC LIMIT 10""", 'recent_comments'),
                ("""SELECT * FROM queue WHERE session_id = ? ORDER BY queue_position""", 'queue_items')
            ]
            
            for query, key in queries:
                cursor.execute(query, (target_session_id,))
                result[key] = [dict(row) for row in cursor.fetchall()]
        
        return result
    
    return _db_query(batch_query, "批量获取会话信息") or {}

# 订单相关函数 - 采纳您的_process_order帮助函数
def _process_order(cursor, order_id: str, creation_time: str, buyer_account: str, 
                  product_name: str, product_quantity: int, order_details_json: str,
                  exists: bool) -> None:
    """处理单个订单的更新或插入"""
    if exists:
        cursor.execute("""
            UPDATE pending_orders 
            SET creation_time = ?, buyer_account = ?, product_name = ?, 
                product_quantity = ?, order_details_json = ?
            WHERE order_id = ?
        """, (creation_time, buyer_account, product_name, product_quantity, order_details_json, order_id))
    else:
        cursor.execute("""
            INSERT INTO pending_orders 
            (order_id, creation_time, buyer_account, product_name, product_quantity, order_details_json)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (order_id, creation_time, buyer_account, product_name, product_quantity, order_details_json))

def add_pending_order(order_id: str, creation_time: str, buyer_account: str, 
                     product_name: str, product_quantity: int, order_details_json: str) -> bool:
    """添加待处理订单到数据库"""
    def upsert_order(conn):
        cursor = conn.cursor()
        cursor.execute("SELECT order_id FROM pending_orders WHERE order_id = ?", (order_id,))
        exists = cursor.fetchone() is not None
        _process_order(cursor, order_id, creation_time, buyer_account, product_name, 
                      product_quantity, order_details_json, exists)
        return True
    
    return bool(_db_execute(upsert_order, f"添加待处理订单 {order_id}"))

def add_pending_orders_batch(orders_data: List[Dict[str, Any]]) -> bool:
    """批量添加待处理订单到数据库"""
    if not orders_data:
        logger.debug("没有订单数据需要添加")
        return True
        
    def batch_insert_orders(conn):
        cursor = conn.cursor()
        
        # 1. 提取所有有效的订单ID
        order_ids = [order.get('order_id') for order in orders_data if order.get('order_id')]
        if not order_ids:
            logger.warning("没有有效的订单ID，跳过批量添加")
            return True
            
        # 2. 一次性查询所有已存在的订单
        placeholders = ','.join(['?'] * len(order_ids))
        cursor.execute(f"SELECT order_id FROM pending_orders WHERE order_id IN ({placeholders})", order_ids)
        existing_orders = {row[0] for row in cursor.fetchall()}
        
        # 3. 准备批量插入和更新的参数
        to_insert = []
        to_update = []
        
        for order_data in orders_data:
            order_id = order_data.get('order_id')
            if not order_id:
                continue
                
            # 准备共同的数据
            data = (
                order_data.get('creation_time'),
                order_data.get('buyer_account'),
                order_data.get('product_name'),
                order_data.get('product_quantity'),
                order_data.get('order_details_json')
            )
                
            if order_id in existing_orders:
                to_update.append(data + (order_id,))  # 更新参数
            else:
                to_insert.append((order_id,) + data)  # 插入参数
        
        # 4. 批量执行SQL操作
        if to_update:
            cursor.executemany("""
                UPDATE pending_orders 
                SET creation_time = ?, buyer_account = ?, product_name = ?, 
                    product_quantity = ?, order_details_json = ?
                WHERE order_id = ?
            """, to_update)
            logger.info(f"已批量更新 {len(to_update)} 个已存在订单")
            
        if to_insert:
            cursor.executemany("""
                INSERT INTO pending_orders 
                (order_id, creation_time, buyer_account, product_name, 
                 product_quantity, order_details_json)
                VALUES (?, ?, ?, ?, ?, ?)
            """, to_insert)
            logger.info(f"已批量插入 {len(to_insert)} 个新订单")
            
        return True
    
    return bool(_db_execute(batch_insert_orders, f"批量添加 {len(orders_data)} 个订单"))

def update_pending_order_status(order_id: str, status: str) -> bool:
    """更新待处理订单的状态
    
    Args:
        order_id: 订单ID
        status: 新状态
    
    Returns:
        bool: 操作是否成功
    """
    def update_status(conn):
        cursor = conn.cursor()
        cursor.execute("SELECT order_id FROM pending_orders WHERE order_id = ?", (order_id,))
        if cursor.fetchone() is None:
            logger.warning(f"订单 {order_id} 不存在，无法更新状态")
            return False
        cursor.execute("UPDATE pending_orders SET status = ? WHERE order_id = ?", (status, order_id))
        return True
    
    result = bool(_db_execute(update_status, f"更新订单 {order_id} 状态"))
    return result

def delete_pending_order(order_id: str) -> bool:
    """删除待处理订单"""
    def delete_order(conn):
        cursor = conn.cursor()
        cursor.execute("DELETE FROM pending_orders WHERE order_id = ?", (order_id,))
        return True
    
    result = bool(_db_execute(delete_order, f"删除订单 {order_id}"))
    return result

def get_all_available_pending_orders() -> List[Dict[str, Any]]:
    """获取所有可用的待处理订单"""
    def query_orders(conn):
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT order_id, creation_time, buyer_account, product_name, 
                   product_quantity, order_details_json
            FROM pending_orders WHERE status = 'available' ORDER BY creation_time
        """)
        
        orders = []
        for row in cursor.fetchall():
            order_dict = dict(row)
            try:
                order_dict['details'] = json.loads(order_dict['order_details_json'] or '{}')
            except Exception as e:
                logger.error(f"解析订单 {order_dict['order_id']} 详情JSON出错: {e}")
                order_dict['details'] = {}
            orders.append(order_dict)
        
        return orders
    
    return _db_query(query_orders, "获取所有可用待处理订单") or []

def get_all_pending_orders_with_status() -> List[Dict[str, Any]]:
    """获取所有待处理订单（包含状态信息）"""
    def query_orders(conn):
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT order_id, creation_time, buyer_account, product_name, 
                   product_quantity, order_details_json, status
            FROM pending_orders ORDER BY creation_time
        """)
        
        orders = []
        for row in cursor.fetchall():
            order_dict = dict(row)
            try:
                order_dict['details'] = json.loads(order_dict['order_details_json'] or '{}')
            except Exception as e:
                logger.error(f"解析订单 {order_dict['order_id']} 详情JSON出错: {e}")
                order_dict['details'] = {}
            orders.append(order_dict)
        
        return orders
    
    return _db_query(query_orders, "获取所有订单（含状态）") or []

def update_player_order_state(player_id: str, updates_dict: Dict[str, Any], session_id: Optional[int] = None) -> bool:
    """更新玩家的订单相关状态
    
    Args:
        player_id: 玩家ID
        updates_dict: 一个字典，键是字段名，值是新的字段值
        session_id: 会话ID，如果为None则使用当前会话ID
    
    Returns:
        bool: 操作是否成功
    """
    if not session_id:
        session_id = get_current_session_id()
        if not session_id:
            logger.error(f"更新玩家 {player_id} 订单状态失败：无法获取当前会话ID")
            return False
    
    def update_player(conn):
        cursor = conn.cursor()
        
        if not updates_dict:
            logger.debug(f"没有为玩家 {player_id} 提供更新字段")
            return True

        updates = []
        params = []
        
        # 从 updates_dict 构建更新语句
        allowed_fields = {"temp_order_toVerify", "temp_his_orderID_toUse", 
                          "pending_game_entry_details_json", "free_games_used_this_session"}
        
        for field_name, value in updates_dict.items():
            if field_name in allowed_fields:
                updates.append(f"{field_name} = ?")
                params.append(value)
            else:
                logger.warning(f"尝试更新不允许的字段 {field_name} for player {player_id}")

        if not updates:
            logger.debug(f"没有有效字段为玩家 {player_id} 进行更新")
            return True 

        params.extend([player_id, session_id])
        sql = f"UPDATE players SET {', '.join(updates)} WHERE player_id = ? AND session_id = ?"
        
        try:
            cursor.execute(sql, params)
            if cursor.rowcount == 0:
                # 检查玩家是否存在
                cursor.execute("SELECT 1 FROM players WHERE player_id = ? AND session_id = ?", (player_id, session_id))
                if cursor.fetchone():
                    logger.debug(f"更新玩家 {player_id} 订单状态，数据未改变")
                else:
                    logger.warning(f"尝试更新玩家 {player_id} 订单状态，但该玩家在会话 {session_id} 中不存在")
            else:
                logger.debug(f"已更新玩家 {player_id} 的订单相关状态: {updates_dict}")
        except Exception as e:
            logger.error(f"更新玩家 {player_id} 订单状态时数据库错误: {e}")
            return False
        return True
    
    return bool(_db_execute(update_player, f"更新玩家 {player_id} 订单状态"))

def update_comments_after_game(session_id: int, player_id: str, comments_after_game: int) -> bool:
    """更新玩家的游戏后评论计数"""
    def update_count(conn):
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE players SET comments_after_game = ?
            WHERE player_id = ? AND session_id = ?
        """, (comments_after_game, player_id, session_id))
        return True
    
    return bool(_db_execute(update_count, f"更新玩家 {player_id} 游戏后评论计数"))

# 便捷函数
def set_current_session_id(session_id: int):
    """设置当前会话ID"""
    global current_session_id
    current_session_id = session_id
    logger.debug(f"当前会话ID已设置为: {session_id}")

def get_current_session_id() -> Optional[int]:
    """获取当前会话ID"""
    return current_session_id

# 统计相关函数
def get_session_stats(session_id: int) -> Dict[str, Any]:
    """获取会话统计信息"""
    def query_stats(conn):
        cursor = conn.cursor()
        stats = {}
        
        # 获取各种计数 - 采纳您的循环优化
        for table, key in [('players', 'player_count'), ('comments', 'comment_count'), ('games', 'game_count')]:
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE session_id = ?", (session_id,))
            stats[key] = cursor.fetchone()[0]
        
        # 获取奖品统计
        cursor.execute("""
            SELECT result, COUNT(*) FROM games 
            WHERE session_id = ? AND result != 'nothing'
            GROUP BY result
        """, (session_id,))
        stats['prize_stats'] = {result: count for result, count in cursor.fetchall()}
        
        return stats
    
    return _db_query(query_stats, f"获取会话 {session_id} 统计信息") or {}

def get_recent_comments(session_id: int, limit: int = 10) -> List[Dict[str, Any]]:
    """获取指定会话的最近评论"""
    def query_comments(conn):
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.comment_id, c.player_id, p.name, c.comment_time, c.content
            FROM comments c JOIN players p ON c.player_id = p.player_id
            WHERE c.session_id = ? ORDER BY c.comment_time DESC LIMIT ?
        """, (session_id, limit))
        
        return [dict(row) for row in cursor.fetchall()]
    
    return _db_query(query_comments, f"获取会话 {session_id} 最近评论") or []

def get_queue_items(session_id: int) -> List[Dict[str, Any]]:
    """获取指定会话的队列项目"""
    def query_queue(conn):
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM queue WHERE session_id = ? ORDER BY queue_position
        """, (session_id,))
        
        return [dict(row) for row in cursor.fetchall()]
    
    return _db_query(query_queue, f"获取会话 {session_id} 队列项目") or []

# 初始化模块时设置配置
def init_db_with_config(config=None):
    """使用配置初始化数据库模块"""
    if config:
        set_config(config)
    init_db()

# 初始化模块
init_db()
logger.info(f"数据库模块初始化完成，使用数据库文件: {DB_FILE}")
