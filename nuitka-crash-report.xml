<?xml version='1.0' encoding='utf8'?>
<nuitka-compilation-report nuitka_version="2.7.12" nuitka_commercial_version="not installed" mode="accelerated" completion="error exit message (1)" exit_message="Failed unexpectedly in Scons C backend compilation.">
  <module name="__main__" kind="PythonMainModule" usage="root_module" reason="Root module" source_path="${cwd}\Tool_BatchPlayerSender.py">
    <optimization-time pass="1" time="0.28" micro_passes="8" max_branch_merge="88" merged_total="5179" />
    <optimization-time pass="2" time="0.03" micro_passes="1" max_branch_merge="53" merged_total="581" />
    <module_usages>
      <module_usage name="site" finding="excluded" line="1" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="time" finding="absolute" line="7" />
      <module_usage name="logging" finding="excluded" line="8" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="random" finding="excluded" line="9" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="threading" finding="excluded" line="10" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="json" finding="excluded" line="11" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="typing" finding="excluded" line="12" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="http.server" finding="absolute" line="13" />
      <module_usage name="collections" finding="excluded" line="14" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="collections.deque" finding="not-found" line="14" />
    </module_usages>
  </module>
  <performance>
    <memory_usage name="after_launch" value="40902656" />
    <memory_usage name="before_c_code_generation" value="43548672" />
    <memory_usage name="before_running_scons" value="45891584" />
  </performance>
  <data_composer blob_size="10084" total="425">
    <module_data filename="__bytecode.const" blob_name=".bytecode" blob_size="3" input_size="0" />
    <module_data filename="__constants.const" blob_name="" blob_size="872" input_size="2174" />
    <module_data filename="module.__main__.const" blob_name="__main__" blob_size="9169" input_size="13378" />
  </data_composer>
  <command_line>
    <option value="--show-scons" />
    <option value="Tool_BatchPlayerSender.py" />
  </command_line>
  <plugins>
    <plugin name="anti-bloat" user_enabled="no" />
    <plugin name="eventlet" user_enabled="no" />
    <plugin name="gi" user_enabled="no" />
    <plugin name="implicit-imports" user_enabled="no" />
    <plugin name="multiprocessing" user_enabled="no" />
    <plugin name="options-nanny" user_enabled="no" />
    <plugin name="pkg-resources" user_enabled="no" />
    <plugin name="transformers" user_enabled="no" />
  </plugins>
  <distributions />
  <python python_exe="${sys.prefix}\python.exe" python_flavor="CPython Official" python_version="3.9.0" os_name="Windows" os_release="10" arch_name="x86_64" filesystem_encoding="utf-8">
    <search_path>
      <path value="${cwd}" />
      <path value="${sys.prefix}\DLLs" />
      <path value="${sys.prefix}\lib" />
      <path value="${sys.prefix}" />
      <path value="${sys.prefix}\lib\site-packages" />
    </search_path>
  </python>
  <output run_filename="${cwd}\Tool_BatchPlayerSender.cmd" />
</nuitka-compilation-report>
