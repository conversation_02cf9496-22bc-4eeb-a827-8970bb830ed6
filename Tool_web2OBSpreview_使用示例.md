# Tool_web2OBSpreview.py 使用示例

## 新增功能：player_waiting_order 等待订单提示测试

### 概述
`Tool_web2OBSpreview.py` 工具现已支持测试 `player_waiting_order` 功能，可以在不启动主程序的情况下预览等待订单提示的显示效果。

### 新增命令

#### 1. waitorder 命令
```bash
waitorder <玩家名>
```
- **功能**: 显示等待订单提示区域
- **示例**: 
  - `waitorder` - 使用默认玩家名"等待玩家"
  - `waitorder 张三` - 显示"张三免费已用完！"

#### 2. wo 命令（简写）
```bash
wo <玩家名>
```
- **功能**: 与 waitorder 相同，简写形式
- **示例**:
  - `wo` - 使用默认玩家名
  - `wo 李四` - 显示"李四免费已用完！"

### 演示序列更新

运行 `demo` 命令时，演示序列现在包含等待订单提示步骤：

1. 欢迎消息
2. 更新队列
3. **等待订单提示** ← 新增
4. 玩家开始游戏
5. 显示抓取区域
6. 玩家抓中奖品
7. 结束消息

### 使用方法

#### 启动工具
```bash
python Tool_web2OBSpreview.py
```

#### 测试等待订单提示
```bash
🎮 请输入命令 > waitorder 测试玩家
```
或
```bash
🎮 请输入命令 > wo 测试玩家
```

#### 查看完整演示
```bash
🎮 请输入命令 > demo
```

### 配置说明

等待订单提示的显示效果由 `config_play.yaml` 中的 `player_waiting_order` 配置控制：

```yaml
web_display:
  display_settings:
    player_waiting_order:
      enabled: true
      template: "{player}免费已用完！"
      font_size: "90px"
      font_color: "#00FF00"
      duration: 2
      center: ["80%", "42%"]
      width: "85%"
```

### 显示特点

- **独立显示区域**: 不会与其他游戏状态消息冲突
- **自动定时隐藏**: 显示2秒后自动消失
- **可配置位置**: 默认显示在屏幕右侧 (80%, 42%) 位置
- **动态样式**: 支持实时配置更新

### 测试场景

1. **基本功能测试**:
   ```bash
   waitorder 玩家A
   ```

2. **多玩家测试**:
   ```bash
   waitorder 玩家A
   # 等待2秒后
   waitorder 玩家B
   ```

3. **与其他状态混合测试**:
   ```bash
   waitorder 等待玩家
   # 等待1秒后
   1 正在游戏的玩家
   ```

4. **完整流程演示**:
   ```bash
   demo
   ```

### 注意事项

- 等待订单提示会在配置的持续时间后自动隐藏
- 新的状态消息会覆盖当前显示的等待订单提示
- 可以通过 `reload` 命令重新加载配置文件来测试不同的显示效果
- 使用 `clear` 命令可以立即清除所有显示内容

### 故障排除

如果等待订单提示没有显示：

1. 检查 `config_play.yaml` 中 `player_waiting_order.enabled` 是否为 `true`
2. 确认 `webdisplay_template.html` 包含 `waitingOrderDisplayWrapper` 元素
3. 使用 `reload` 命令重新加载配置
4. 检查浏览器控制台是否有JavaScript错误

### 相关文件

- `Tool_web2OBSpreview.py` - 预览工具主程序
- `config_play.yaml` - 配置文件
- `webdisplay_template.html` - HTML模板
- `play_processing.py` - 后端事件处理
- `Play_webdisplay.py` - WebDisplay核心模块
