import math
import time
import threading
import logging
import traceback
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import queue
import copy
import re
import json
import play_logic
import Play_db
import play_db_sync
import Play_obs
import sys

logger = logging.getLogger(__name__)
TIME_FORMAT_FOR_COMPARISON = "%a %b %d %H:%M:%S %Y"

# 全量内存订单簿：order_id -> 状态('available','claimed')
orderbook_status: Dict[str, str] = {}
orderbook_status_lock = threading.RLock()

# 全局物体映射缓存：track_number -> class_name
latest_object_map: Dict[str, str] = {}
object_map_lock = threading.Lock()

def update_object_map_from_detection(new_object_map: Dict[str, str], 
                                   in_que: List[Tuple], queue_lock: threading.Lock) -> bool:
    """从检测服务更新物体映射缓存，并同步更新队列中的物体名称
    
    Returns:
        bool: True表示映射发生了变化并且已同步到队列，False表示无变化
    """
    map_changed = False
    
    # 比较并更新全局缓存
    with object_map_lock:
        if latest_object_map != new_object_map:
            latest_object_map.clear()
            latest_object_map.update(new_object_map)
            map_changed = True
            logger.debug(f"物体映射缓存已更新，共 {len(latest_object_map)} 个物体")
    
    # 如果映射发生变化，同步更新队列
    if map_changed:
        sync_in_que_with_object_map(in_que, queue_lock)
        return True
    
    return False

def sync_in_que_with_object_map(in_que: List[Tuple], queue_lock: threading.Lock) -> None:
    """同步队列中所有条目的物体名称"""
    updated_count = 0
    
    # 获取当前映射快照
    current_obj_map = {}
    with object_map_lock:
        current_obj_map = latest_object_map.copy()
    
    # 更新队列中的物体名称
    with queue_lock:
        for queue_item in in_que:
            entry = queue_item[0]  # entry是第一个元素
            if len(entry) >= 7:  # 确保有足够的字段
                target_id_str = str(entry[4])  # content (target_id)
                current_target_name = entry[6] if len(entry) > 6 else "未知"
                
                new_target_name = current_obj_map.get(target_id_str, "未知")
                
                if current_target_name != new_target_name:
                    if len(entry) == 6:
                        entry.append(new_target_name)  # 添加新字段
                    else:
                        entry[6] = new_target_name  # 更新现有字段
                    updated_count += 1
    
    if updated_count > 0:
        logger.debug(f"已同步 {updated_count} 个队列条目的物体名称")

def get_object_name_by_id(target_id: str) -> str:
    """根据目标ID获取物体名称"""
    with object_map_lock:
        return latest_object_map.get(str(target_id), "未知")

def _recalculate_and_update_comments_after_game(
    player_id: str, p_info: Dict[str, Any], p_comments: Dict[str, List[Tuple[str, str]]],
    p_games: Dict[str, List[List[Any]]], current_logger: logging.Logger,
    pi_lock: threading.Lock, pc_lock: threading.Lock, pg_lock: threading.Lock
):
    """重新计算并更新玩家游戏后评论数，排除上次游戏前已出现的评论内容"""
    if not player_id:
        current_logger.error("player_id 为空，无法计算有效评论数")
        return

    # 获取历史游戏时间和评论
    last_game_time_str = None
    parsed_last_game_time = None
    comments_for_player = []
    
    with pc_lock, pg_lock:
        history = p_games.get(player_id, [])
        if history:
            last_game_time_str = history[-1][0]
            try:
                parsed_last_game_time = datetime.strptime(last_game_time_str, TIME_FORMAT_FOR_COMPARISON)
            except ValueError:
                current_logger.error(f"无法解析玩家 {player_id} 的上次游戏时间 '{last_game_time_str}'")
                parsed_last_game_time = None
        comments_for_player = list(p_comments.get(player_id, []))

    # 构建历史评论内容集合
    historical_contents = set()
    if parsed_last_game_time:
        for dt_str, content in comments_for_player:
            try:
                dt_p = datetime.strptime(dt_str, TIME_FORMAT_FOR_COMPARISON)
                if dt_p <= parsed_last_game_time:
                    historical_contents.add(content)
            except ValueError:
                continue

    # 计算新评论数
    unique_new_contents = set()
    for dt_str, content in comments_for_player:
        try:
            dt_p = datetime.strptime(dt_str, TIME_FORMAT_FOR_COMPARISON)
            if parsed_last_game_time is None:
                unique_new_contents.add(content)
            elif dt_p > parsed_last_game_time and content not in historical_contents:
                unique_new_contents.add(content)
        except ValueError:
            current_logger.warning(f"无法解析评论时间 '{dt_str}'，跳过")

    new_count = len(unique_new_contents)

    # 更新 player_info
    with pi_lock:
        if player_id in p_info:
            p_info[player_id]['comments_after_game'] = new_count
        else:
            current_logger.warning(f"玩家 {player_id} 在更新时已不在 player_info")

def calculate_priority(player: str, entry: List, player_info: Dict, player_comments: Dict, 
                      player_games: Dict, config: Dict, current_dt: str = None, order_id: Optional[str] = None) -> float:
    """计算玩家优先级系数"""
    priority = 1.0
    # 修正：只解包前6个元素，忽略第7个元素（target_object_name）
    plat, dt, name, player_id, content, head_img = entry[:6]
    priority_config = config.get('game', {}).get('priority', {})

    # 付费乘数
    if order_id is not None:
        priority *= priority_config.get('paid_multiplier', 5.0)

    if player_id not in player_info:
        logger.warning(f"计算优先级时未找到玩家 {player_id} 的信息")
        return priority

    player_data = player_info[player_id]

    # 评论系数
    comments_after_game = player_data.get('comments_after_game', 0)
    pca = priority_config.get('comment', {}).get('pca', 1.0)
    pcb = priority_config.get('comment', {}).get('pcb', 1.0)
    priority *= math.sqrt(pca * (pcb + comments_after_game))

    # 游戏次数系数
    played_times = len(player_games.get(player_id, []))
    ppa = priority_config.get('played_times', {}).get('ppa', 5.0)
    ppb = priority_config.get('played_times', {}).get('ppb', 1.5)
    priority *= ppa * (ppb ** -played_times)

    # 停留时间系数
    come_time_str = player_data.get('come_time', dt)
    try:
        come_time = datetime.strptime(come_time_str, "%a %b %d %H:%M:%S %Y")
        if current_dt:
            current_time_dt = datetime.strptime(current_dt, "%a %b %d %H:%M:%S %Y")
        else:
            current_time_dt = datetime.strptime(dt, "%a %b %d %H:%M:%S %Y")

        time_diff = (current_time_dt - come_time).total_seconds() / 60
        stayed_minutes = max(1, time_diff)
        pta = priority_config.get('stay_time', {}).get('pta', 1.0)
        ptb = priority_config.get('stay_time', {}).get('ptb', 1.0)
        priority *= pta + ptb * math.log(stayed_minutes)
    except Exception:
        pass

    # 奖品状态系数
    prizes = player_games.get(player_id, [])
    has_prize = any(p[1].lower() != 'nothing' for p in prizes)
    prize_status_config = priority_config.get('prize_status', {})
    if has_prize:
        priority *= prize_status_config.get('has_prize', 1.0)
    else:
        priority *= prize_status_config.get('no_prize', 3.0)

    return priority

def calculate_game_possibility(player_id: str, player_info: Dict, player_games: Dict, config: Dict, 
                              player_games_lock: threading.Lock, order_id: Optional[str] = None) -> float:
    """计算玩家游戏请求的可能性系数"""
    possibilities = config.get('game', {}).get('possibilities', {})
    
    with player_games_lock:
        prizes = player_games.get(player_id, [])
        
    if not prizes:
        base_possibility = possibilities.get('first_time', 1.0)
    elif all(p[1].lower()=='nothing' for p in prizes):
        base_possibility = possibilities.get('after_no_prize', 0.6)
    else:
        base_possibility = possibilities.get('after_prize', 0.0)
        
    if order_id is not None:
        base_possibility = min(1.0, base_possibility + possibilities.get('paid_bonus', 0.2))
        
    return base_possibility

def update_queue_priorities_inplace(in_que: List[Tuple], player_info: Dict, player_comments: Dict, 
                                   player_games: Dict, config: Dict) -> None:
    """原地更新队列中所有请求的优先级并重新排序"""
    current_dt = time.ctime()
    
    for i in range(len(in_que)):
        queue_item = in_que[i]
        if len(queue_item) == 4:
            entry, possibility, _, order_id = queue_item
        else:
            entry, possibility, _ = queue_item
            order_id = None
            
        player_id = entry[3]
        new_priority = calculate_priority(player_id, entry, player_info, player_comments, player_games, config, current_dt, order_id)
        
        if order_id is not None:
            in_que[i] = (entry, possibility, new_priority, order_id)
        else:
            in_que[i] = (entry, possibility, new_priority)
    
    in_que.sort(key=lambda x: (-x[2], x[0][1]))

def _handle_game_completion(
    player_id: str, name: str, captured_item: str, order_id: Optional[str],
    config: Dict, current_player: Dict,
    player_info: Dict, player_games: Dict,
    db_sync_manager: Optional[play_db_sync.DBSyncManager],
    status_update_queue: Optional[queue.Queue],
    pi_lock: threading.Lock, pg_lock: threading.Lock,
    current_game_start_time: float
):
    """
    处理游戏完成后的通用逻辑
    更新玩家统计、数据库、current_player状态，并发送game_end事件
    """
    logger.info(f"[游戏完成] 玩家 {name}({player_id}), 结果: {captured_item}, 订单: {order_id}")

    game_end_time_str = time.ctime()

    with pg_lock:
        if player_id not in player_games:
            player_games[player_id] = []
        player_games[player_id].append([game_end_time_str, captured_item, "N/A", order_id])
    
    if db_sync_manager:
        session_id_val = Play_db.get_current_session_id()
        if session_id_val:
            game_data_for_db = {
                'session_id': session_id_val,
                'player_id': player_id,
                'game_time': game_end_time_str,
                'result': captured_item,
                'order_ID': order_id
            }
            db_sync_manager.add_sync_task("game_update", game_data_for_db)

    with pi_lock:
        if player_id in player_info:
            player_info[player_id]['comments_after_game'] = 0 # 重置评论计数
            if db_sync_manager:
                db_sync_manager.add_sync_task("player_info_update", {
                    'player_id': player_id,
                    'comments_after_game': 0
                })
    
    # 清除current_player状态
    if current_player.get('player_id') == player_id:
        current_player.clear()
    else:
        logger.warning(f"Game completion for {player_id}, but current_player is {current_player.get('player_id')}")

    # 通知游戏结束
    if status_update_queue:
        player_info_copy = {}
        with pi_lock:
            player_info_copy = copy.deepcopy(player_info)
        
        status_update_queue.put({
            'type': 'game_end', 
            'player_id': player_id, 
            'name': name,
            'result': captured_item, 
            'player_info': player_info_copy,
            'duration': time.time() - current_game_start_time
        })

        # 发送'caught'事件给OBS/GUI
        if captured_item and captured_item.lower() != 'nothing':
            status_update_queue.put({
                'type': 'caught',
                'player': name,
                'item': captured_item,
                'success': True
            })
        elif captured_item is not None:
             status_update_queue.put({
                'type': 'caught',
                'player': name,
                'item': "未抓中",
                'success': False
            })

def process_game_queue(in_que: List[Tuple], player_info: Dict, player_comments: Dict, player_games: Dict, 
                      queue_lock: threading.Lock, stop_flag: Dict[str, bool], current_player: Dict, 
                      config: Dict, status_update_queue: Optional[queue.Queue] = None,
                      pi_lock: threading.Lock = None, pc_lock: threading.Lock = None, pg_lock: threading.Lock = None,
                      move_service_client = None): # 新增参数
    """游戏处理线程：从队列中提取优先级最高的玩家进行游戏处理"""
    thread_config = config.get('game_thread', {})
    empty_queue_sleep = thread_config.get('empty_queue_sleep', 0.5)
    post_game_sleep = thread_config.get('post_game_sleep', 0.5)
    error_sleep = thread_config.get('error_sleep', 1.0)
    game_timeout = thread_config.get('game_timeout', 30)

    import play_db_sync
    db_sync_manager = play_db_sync.db_sync_manager

    game_mode = config.get('game', {}).get('game_mode', 'simulate')

    while stop_flag.get('running', True):
        entry_tuple = None
        lock_acquired = False
        
        try:
            lock_acquired = queue_lock.acquire(timeout=1.0)

            if lock_acquired:
                if in_que:
                    # 在真实模式下，检查是否有游戏正在进行
                    if game_mode == 'real' and current_player.get('status') == 'waiting_move_service':
                        logger.debug("[游戏线程] 当前有真实游戏正在等待移动服务响应，暂不处理新请求。")
                        queue_lock.release()
                        lock_acquired = False
                        time.sleep(empty_queue_sleep)
                        continue
                    
                    entry_tuple = in_que.pop(0)
                else:
                    logger.debug("[游戏线程] 当前队列为空")
            else:
                logger.warning("[游戏线程] 获取队列锁超时")

        finally:
            if lock_acquired:
                if entry_tuple:
                    latest_queue_copy = list(in_que)
                queue_lock.release()
                
                if entry_tuple and status_update_queue:
                    player_info_copy = None
                    if pi_lock:
                        with pi_lock:
                            player_info_copy = copy.deepcopy(player_info)
                    else:
                        player_info_copy = copy.deepcopy(player_info)
                            
                    status_update_queue.put({
                        'type': 'queue_update',
                        'in_que': latest_queue_copy,
                        'player_info': player_info_copy
                    })

        if not entry_tuple:
            time.sleep(empty_queue_sleep)
            continue

        # 解包队列项
        entry_list = entry_tuple[0]
        possibility = entry_tuple[1]
        order_id = entry_tuple[3] if len(entry_tuple) == 4 else None

        # 游戏处理逻辑
        current_game_start_time = time.time()
        try:
            entry_data = entry_list 
            name = entry_data[2]
            player_id = entry_data[3]
            target_id_from_comment = entry_data[4]
            
            # 确保锁已初始化
            if not pi_lock or not pg_lock:
                logger.error(f"锁未初始化，无法为玩家 {name} 处理游戏。")
                time.sleep(error_sleep)
                continue

            # 在游戏真正开始前扣除免费次数（如果没有订单）
            with pi_lock:
                if order_id is None:
                    free_games_used = player_info.get(player_id, {}).get('free_games_used_this_session', 0)
                    if player_id in player_info:
                        player_info[player_id]['free_games_used_this_session'] = free_games_used + 1
                        logger.info(f"[游戏线程] 玩家 {name}({player_id}) 开始免费游戏，扣除免费次数 ({free_games_used + 1})")
                        
                        if db_sync_manager:
                            db_sync_manager.add_sync_task("update_player_order_state", {
                                'player_id': player_id,
                                'free_games_used_this_session': player_info[player_id]['free_games_used_this_session']
                            })
                else:
                    logger.info(f"[游戏线程] 玩家 {name}({player_id}) 使用订单 {order_id}，不消耗免费次数")

            # 更新当前玩家状态
            current_player.update({
                'player_id': player_id, 'name': name, 'start_time': current_game_start_time,
                'target_id': target_id_from_comment, 'target_object': "未知", 'order_id': order_id
            })
            
            # 获取目标物体名称
            if game_mode == 'real':
                current_player['target_object'] = get_object_name_by_id(target_id_from_comment)
            else:
                try:
                    (x, y), target_object_from_logic = play_logic.pos_update(target_id_from_comment)
                    current_player['target_object'] = target_object_from_logic
                except Exception as e:
                    logger.error(f"获取目标物体名称时出错: {e}")
                    current_player['target_object'] = f"未知({target_id_from_comment})"
            
            # 通知游戏开始
            if status_update_queue is not None:
                status_update_queue.put({
                    'type': 'game_start', 'player_id': player_id, 'name': name,
                    'start_time': current_player['start_time'], 'target_id': target_id_from_comment,
                    'target_object': current_player['target_object']
                })

            # 根据游戏模式执行不同的游戏逻辑
            if game_mode == 'real':
                # 真实模式：使用移动服务
                if not move_service_client:
                    logger.error(f"[游戏线程] 真实模式下移动服务客户端未初始化，玩家 {name}({player_id}) 游戏失败")
                    _handle_game_completion(
                        player_id, name, "nothing", order_id, config, current_player,
                        player_info, player_games, db_sync_manager, status_update_queue,
                        pi_lock, pg_lock, current_game_start_time
                    )
                    time.sleep(error_sleep)
                    continue
                
                # 设置等待移动服务状态
                current_player['status'] = 'waiting_move_service'
                
                # 发送抓取指令到移动服务
                command_id = move_service_client.send_pick_command(
                    target_object_id=target_id_from_comment,
                    player_id=player_id,
                    player_name=name
                )
                
                if command_id:
                    current_player['move_command_id'] = command_id
                    logger.info(f"[游戏线程-真实模式] 玩家 {name}({player_id}) 抓取指令已发送到移动服务，命令ID: {command_id}")
                    # 真实模式下，游戏结果将通过移动服务事件返回，这里不需要继续处理
                else:
                    logger.error(f"[游戏线程-真实模式] 玩家 {name}({player_id}) 发送抓取指令失败")
                    current_player['status'] = 'error'
                    _handle_game_completion(
                        player_id, name, "nothing", order_id, config, current_player,
                        player_info, player_games, db_sync_manager, status_update_queue,
                        pi_lock, pg_lock, current_game_start_time
                    )
            else:
                # 模拟模式：使用现有的 play_logic
                logger.info(f"[游戏线程-模拟模式] 开始执行游戏逻辑 for {name}({player_id})")
                captured = "nothing"

                game_timer = threading.Timer(game_timeout, lambda: logger.error(f"玩家 {name}({player_id}) 游戏超时"))
                game_timer.start()

                try:
                    captured = play_logic.go_play(entry_list, possibility, event_queue=status_update_queue)
                except Exception as e:
                    logger.error(f"玩家 {name}({player_id}) 游戏逻辑执行出错: {e}")
                finally:
                    game_timer.cancel()

                # 模拟模式游戏完成处理
                _handle_game_completion(
                    player_id, name, captured, order_id, config, current_player,
                    player_info, player_games, db_sync_manager, status_update_queue,
                    pi_lock, pg_lock, current_game_start_time
                )

            time.sleep(post_game_sleep)

        except Exception as e:
            logger.error(f"处理游戏时发生未预期的错误: {e}")
            logger.error(traceback.format_exc())
            current_player.clear()
            if status_update_queue is not None:
                status_update_queue.put({'type': 'game_error', 'error': str(e)})
            time.sleep(error_sleep)

    logger.info("[游戏线程] 线程停止")

def process_batch_game_requests(new_game_requests: List[Tuple[str, List]], player_info: Dict, player_comments: Dict, 
                              player_games: Dict, in_que: List, queue_lock: threading.Lock, config: Dict, 
                              current_player: Dict, db_sync_manager=None, player_games_lock: Optional[threading.Lock] = None,
                              status_update_event: Optional[queue.Queue] = None) -> Tuple[List, List]:
    """批量处理游戏请求"""
    if not new_game_requests:
        return [], []

    processed_player_ids = []
    prepared_requests_to_queue = []
    current_player_id = current_player.get('player_id')
    queue_updated = False  # 添加标志来跟踪队列是否有更新

    # 批次内去重
    unique_requests = {}
    for player_id, entry in new_game_requests:
        if player_id not in unique_requests:
            unique_requests[player_id] = entry
        else:
            existing_entry = unique_requests[player_id]
            try:
                existing_time = time.mktime(time.strptime(existing_entry[1]))
                new_time = time.mktime(time.strptime(entry[1]))
                if new_time < existing_time:
                    unique_requests[player_id] = entry
            except Exception:
                pass

    # 获取队列快照
    queued_players = []
    lock_acquired = False
    try:
        lock_acquired = queue_lock.acquire(timeout=3)
        if lock_acquired:
            queued_players = [item[0][3] for item in in_que]
    finally:
        if lock_acquired:
            queue_lock.release()

    # 处理请求
    min_interval = config.get('game', {}).get('min_interval_seconds', 120)
    max_free_games = config.get('game', {}).get('max_free_games_per_session', 1)
    
    for player_id, entry in unique_requests.items():
        # 统一使用 [:6] 截取，确保兼容6或7元素的entry
        plat, msg_dt, name, _, content, head_img = entry[:6]
        
        # 检查规则
        skip_reason = None
        if player_id in processed_player_ids:
            skip_reason = "同批次已处理"
        elif player_id in queued_players:
            # 如果已在队列中，但本次请求的目标编号与原来不同，则更新该队列项
            with queue_lock:
                for idx, queue_item in enumerate(in_que):
                    entry_q = queue_item[0]   # [plat, dt, name, player_id, content, head_img, target_object_name]
                    if entry_q[3] == player_id:
                        old_content = entry_q[4]
                        if old_content != content:
                            logger.info(f"玩家 {name}({player_id}) 修改排队目标: {old_content} -> {content}")
                            # 替换内容和时间，并更新物体名称
                            entry_q[4] = content
                            entry_q[1] = msg_dt
                            # 获取新的物体名称
                            new_target_name = get_object_name_by_id(content)
                            if len(entry_q) == 6:
                                entry_q.append(new_target_name)
                            else:
                                entry_q[6] = new_target_name
                            queue_updated = True  # 标记队列已更新
                        break
            skip_reason = "已在队列中（目标已更新）"
        elif player_id == current_player_id:
            skip_reason = "正在游戏中"
        else:
            # 检查游戏间隔
            if player_id in player_games and player_games[player_id]:
                last_play_time_str = player_games[player_id][-1][0]
                try:
                    time_format = "%a %b %d %H:%M:%S %Y"
                    last_time = datetime.strptime(last_play_time_str, time_format)
                    current_req_time = datetime.strptime(msg_dt, time_format)
                    time_diff = (current_req_time - last_time).total_seconds()
                    
                    if time_diff < min_interval:
                        skip_reason = f"游戏间隔不足 ({time_diff:.0f}s < {min_interval}s)"
                except Exception as e:
                    logger.error(f"计算玩家 {name}({player_id}) 时间间隔出错: {e}")
                    skip_reason = f"时间格式解析失败"
        
        if skip_reason:
            logger.info(f"玩家 {name}({player_id}) 请求被跳过: {skip_reason}")
            processed_player_ids.append(player_id)
            continue
        
        # 检查订单和免费游戏：只看是否有已验证过的订单号
        order_id_to_use = None
        temp_his_order_id = player_info[player_id].get('temp_his_orderID_toUse')
        free_games_used = player_info[player_id].get('free_games_used_this_session', 0)

        if temp_his_order_id:
            # —— 优先：已验证历史订单 —— 
            order_id_to_use = temp_his_order_id
            player_info[player_id]['temp_his_orderID_toUse'] = None
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 使用已验证订单 {temp_his_order_id} 加入队列")
            if db_sync_manager:
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'temp_his_orderID_toUse': None
                })
        elif free_games_used < max_free_games:
            # —— 普通免费名额 —— 
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 免费游戏请求加入队列 ({free_games_used}/{max_free_games})")
        else:
            # —— 免费次数用尽，存为待处理 —— 
            player_info[player_id]['pending_game_entry_details'] = entry
            pending_json = json.dumps(entry, ensure_ascii=False)
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 免费次数用尽，存储待验证游戏请求")
            if db_sync_manager:
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'pending_game_entry_details_json': pending_json
                })
            processed_player_ids.append(player_id)
            continue

        # 获取物体名称
        target_object_name = get_object_name_by_id(content)
        
        # 创建带物体名称的entry（7个字段）
        entry_with_object_name = [plat, msg_dt, name, player_id, content, head_img, target_object_name]

        # 计算可能性和优先级
        possibility = calculate_game_possibility(player_id, player_info, player_games, config, player_games_lock, order_id_to_use)
        priority = calculate_priority(player_id, entry_with_object_name, player_info, player_comments, player_games, config, order_id=order_id_to_use)
        
        if order_id_to_use is not None:
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 带订单 {order_id_to_use} 加入队列，目标: {content}({target_object_name})，优先级: {priority:.2f}")
            prepared_requests_to_queue.append((entry_with_object_name, possibility, priority, order_id_to_use))
        else:
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 免费游戏加入队列，目标: {content}({target_object_name})，优先级: {priority:.2f}")
            prepared_requests_to_queue.append((entry_with_object_name, possibility, priority, None))
            
        processed_player_ids.append(player_id)

    # 修改此处的判断条件 - 即使没有新请求，但队列有更新也要处理
    if not prepared_requests_to_queue and not queue_updated:
        logger.debug("[游戏请求] 批量处理完成，队列无实际变化。")
        return [], processed_player_ids
    
    # 更新队列
    updated_queue_snapshot = []
    lock_acquired = False
    try:
        lock_acquired = queue_lock.acquire(timeout=5)
        if lock_acquired:
            if prepared_requests_to_queue:  # 只有当有新请求时才添加
                in_que.extend(prepared_requests_to_queue)
            
            # 只要有新请求加入 或 现有请求更新，就进行重排序
            update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
            updated_queue_snapshot = copy.deepcopy(in_que)

            # 改进日志记录
            log_message_suffix = f"当前队列长度: {len(in_que)}"
            if prepared_requests_to_queue and queue_updated:
                logger.info(f"[游戏请求] 批量处理完成，{len(prepared_requests_to_queue)} 个新请求已加入，且现有条目已更新。{log_message_suffix}")
            elif prepared_requests_to_queue:
                logger.info(f"[游戏请求] 批量处理完成，{len(prepared_requests_to_queue)} 个新请求已加入。{log_message_suffix}")
            elif queue_updated:
                logger.info(f"[游戏请求] 批量处理完成，队列中现有条目目标已更新并重排。{log_message_suffix}")
            
            # 发送队列更新事件
            if status_update_event is not None:
                player_info_copy = copy.deepcopy(player_info)
                status_update_event.put({
                    'type': 'queue_update',
                    'in_que': updated_queue_snapshot,
                    'player_info': player_info_copy
                })
                logger.debug(f"[游戏请求] 已发送队列更新事件 (因批量请求处理)，队列长度: {len(updated_queue_snapshot)}")
                
    except Exception as e:
        logger.error(f"更新队列时出错: {e}")
    finally:
        if lock_acquired:
            queue_lock.release()
    
    if db_sync_manager and (prepared_requests_to_queue or queue_updated):
        current_session_id = Play_db.get_current_session_id()
        db_sync_manager.add_sync_task("final_queue_update", (current_session_id, updated_queue_snapshot))
    
    return updated_queue_snapshot, processed_player_ids

def process_order_number(player_id: str, order_number: str, player_name: str, config: Dict, 
                         player_info: Dict, in_que: List,
                         player_info_lock: threading.Lock, queue_lock: threading.Lock, 
                         db_sync_manager=None,
                         player_comments_lock: threading.Lock=None, player_games_lock: threading.Lock=None,
                         status_update_event=None, player_comments: Dict=None, player_games: Dict=None) -> None:
    """处理评论中检测到的订单号 - 使用状态管理模式"""
    logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 发送订单号: {order_number}")
    
    with player_info_lock, orderbook_status_lock:
        if player_id not in player_info:
            logger.error(f"[订单处理] 玩家 {player_name}({player_id}) 信息不存在，无法处理订单 {order_number}")
            return

        # 检查订单状态
        status = orderbook_status.get(order_number)
        
        if status == 'available':
            logger.info(f"[订单处理] 订单 {order_number} 可用，分配给玩家 {player_name}({player_id})")
            # 1) 内存先行：更新内存中的订单状态
            orderbook_status[order_number] = 'claimed'
            player_info[player_id]['temp_order_toVerify'] = None

            # 2) 异步落库：触发数据库更新
            if db_sync_manager:
                # 更新数据库中的订单状态
                db_sync_manager.add_sync_task("update_pending_order_status", {
                    'order_id': order_number,
                    'status': 'claimed'
                })
                # 清理验证字段
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'temp_order_toVerify': None
                })
            
            logger.info(f"[订单处理] 订单 {order_number} 已被玩家 {player_name}({player_id}) 成功领取")

            pending_entry_original = player_info[player_id].get('pending_game_entry_details')  # This is 6 elements
            if pending_entry_original:
                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 有待处理的游戏请求，将使用订单 {order_number} 立即加入队列")
                player_info[player_id]['pending_game_entry_details'] = None
                
                if db_sync_manager:
                    db_sync_manager.add_sync_task("update_player_order_state", {
                        'player_id': player_id,
                        'pending_game_entry_details_json': None
                    })

                # Extract content for object name lookup - 确保安全解包
                pending_content = pending_entry_original[4]  # 直接使用索引而不是解包
                target_object_name_for_pending = get_object_name_by_id(pending_content)
                
                # Create a 7-element entry for the queue
                entry_for_queue_from_pending = pending_entry_original + [target_object_name_for_pending]

                # 计算优先级并加入队列
                if player_comments_lock and player_games_lock and player_comments is not None and player_games is not None:
                    with player_comments_lock, player_games_lock:
                        possibility = calculate_game_possibility(player_id, player_info, player_games, config, player_games_lock, order_id=order_number)
                        # Pass the 7-element entry to calculate_priority
                        priority = calculate_priority(player_id, entry_for_queue_from_pending, player_info, player_comments, player_games, config, order_id=order_number)
                else:
                    possibility = 0.5
                    priority = 10.0
                
                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 使用订单 {order_number} 进入队列，目标: {pending_content}({target_object_name_for_pending})，优先级: {priority:.2f}")
                
                with queue_lock:
                    # Add the 7-element entry to the queue
                    in_que.append((entry_for_queue_from_pending, possibility, priority, order_number))
                    if player_comments is not None and player_games is not None:
                        update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
                
                if status_update_event:
                    status_update_event.put({
                        'type': 'queue_update',
                        'in_que': copy.deepcopy(in_que),
                        'player_info': copy.deepcopy(player_info)
                    })
            else:
                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 无待处理游戏请求，检查是否可以升级现有队列项目")
                # 检查是否已在队列并升级
                with queue_lock:
                    player_in_queue_idx = -1
                    for i, queue_item in enumerate(in_que):
                        if len(queue_item) >= 3:
                            entry_q = queue_item[0]
                            if entry_q[3] == player_id:
                                has_order_id = len(queue_item) == 4 and queue_item[3] is not None
                                if not has_order_id:
                                    player_in_queue_idx = i
                                    logger.info(f"[订单处理] 找到玩家 {player_name}({player_id}) 在队列中的位置: {i+1}，准备升级为付费")
                                    break

                    if player_in_queue_idx != -1:
                        if player_comments_lock and player_games_lock and player_comments is not None and player_games is not None:
                            with player_comments_lock, player_games_lock:
                                entry_q, possibility_q, old_priority = in_que[player_in_queue_idx]
                                new_priority_q = calculate_priority(player_id, entry_q, player_info, player_comments, player_games, config, order_id=order_number)
                                in_que[player_in_queue_idx] = (entry_q, possibility_q, new_priority_q, order_number)
                                update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
                                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 队列项已升级为付费: {old_priority:.2f} -> {new_priority_q:.2f} (使用订单 {order_number})")
                        
                        queue_snapshot = copy.deepcopy(in_que)
                        
                        if status_update_event:
                            status_update_event.put({
                                'type': 'queue_update',
                                'in_que': queue_snapshot,
                                'player_info': copy.deepcopy(player_info)
                            })
                
                if player_in_queue_idx == -1:
                    logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 不在队列中，将订单 {order_number} 存储为 temp_his_orderID_toUse")
                    player_info[player_id]['temp_his_orderID_toUse'] = order_number
                    if db_sync_manager:
                        db_sync_manager.add_sync_task("update_player_order_state", {
                            'player_id': player_id,
                            'temp_his_orderID_toUse': order_number
                        })
                        
        elif status == 'claimed':
            logger.warning(f"[订单处理] 订单 {order_number} 状态为 {status}，已被其他玩家使用，无法重复认领")
            # 可以选择通知玩家或采取其他措施
            return
        else:
            # 订单尚未抓到或未知状态，按原逻辑处理为待验证
            logger.info(f"[订单处理] 订单 {order_number} 未知或状态异常，存储为 temp_order_toVerify 等待验证")
            player_info[player_id]['temp_order_toVerify'] = order_number
            
            if db_sync_manager:
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'temp_order_toVerify': order_number,
                    # 'pending_game_entry_details_json': None 这里不要删除原请求，等待订单验证即可。
                })

def extract_order_number(content: str) -> Optional[str]:
    """从内容中提取潜在的订单号（连续的12位以上数字）"""
    if not content:
        return None
    
    # 使用正则表达式查找连续的12位以上数字
    pattern = r'\d{12,}'
    match = re.search(pattern, content)
    
    if match:
        return match.group(0)
    return None

def process_messages_batch(msg_gen, config, session_id, player_info, player_comments, player_games, 
                          in_que, queue_lock, current_player, stop_flag, status_update_event,
                          player_info_lock, player_comments_lock, player_games_lock):
    """批量处理消息的主函数"""
    import play_init
    import play_db_sync
    
    batch_start_time = time.time()

    for msg in msg_gen:
        if not stop_flag.get('running', True):
            break
            
        # 处理心跳消息
        if isinstance(msg, dict) and msg.get('type') == 'Heartbeat':
            with queue_lock, player_info_lock, player_comments_lock, player_games_lock:
                update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
            
            # 只在队列非空时才发送更新事件，避免频繁发送空队列更新
            if in_que:
                with player_info_lock:
                    player_info_copy = copy.deepcopy(player_info)
                status_update_event.put({
                    'type': 'queue_update',
                    'in_que': in_que.copy(),
                    'player_info': player_info_copy
                })
            
            current_time = time.time()
            if current_time - batch_start_time > 5:
                batch_start_time = current_time
            continue
        
        # 常规消息处理
        if isinstance(msg, dict) and "message" in msg and isinstance(msg["message"], list):
            messages = msg["message"]
            batch_game_requests = []
            batch_comments = []
            
            for message in messages:
                dt = message.get('time', time.ctime())
                # parse_message 返回恰好6个元素，这里不需要修改
                plat, msg_dt, name, player_id, content, head_img = play_init.parse_message(message, dt)
                
                if not player_id and not name:
                    continue
                
                # 修正：处理新玩家时移除 paid 字段
                with player_info_lock:
                    if player_id not in player_info:
                        player_info[player_id] = {
                            'plat': plat, 'name': name, 'player': player_id, 'head_img': head_img,
                            'come_time': msg_dt,  # 设置首次出现时间
                            'comments_after_game': 0,
                            'temp_order_toVerify': None, 'temp_his_orderID_toUse': None,
                            'pending_game_entry_details': None, 'free_games_used_this_session': 0
                        }
                        # 使用新的专门同步任务确保玩家记录立即添加到数据库
                        if play_db_sync.db_sync_manager:
                            player_data_for_db = {
                                'player_id': player_id,
                                'plat': plat,
                                'name': name,
                                'head_img': head_img,
                                'come_time': msg_dt,
                                'comments_after_game': 0,
                                'temp_order_toVerify': None,
                                'temp_his_orderID_toUse': None,
                                'pending_game_entry_details': None,
                                'free_games_used_this_session': 0
                            }
                            play_db_sync.db_sync_manager.add_sync_task("add_new_player", player_data_for_db)
                    else:
                        # 更新玩家姓名（如果有变化）
                        if player_info[player_id]['name'] != name:
                            player_info[player_id]['name'] = name
                        # 如果come_time为空或未设置，则设置为当前消息时间
                        if not player_info[player_id].get('come_time'):
                            player_info[player_id]['come_time'] = msg_dt
                
                if content:
                    is_game_request = bool(re.fullmatch(r'\d{1,2}', content))
                    extracted_order_number = extract_order_number(content)
                    
                    # entry 在这里就是6个元素，符合预期
                    with player_info_lock:
                        entry = [plat, msg_dt, name, player_id, content, head_img]
                    
                    if is_game_request:
                        logger.info(f"[消息处理] 玩家 {name}({player_id}) 发送游戏请求: {content}")
                        batch_game_requests.append((player_id, entry))
                    elif extracted_order_number:
                        logger.info(f"[消息处理] 玩家 {name}({player_id}) 发送包含订单号的消息: {content}，提取订单号: {extracted_order_number}")
                        process_order_number(
                            player_id, extracted_order_number, name, config, player_info, in_que,
                            player_info_lock, queue_lock, play_db_sync.db_sync_manager,
                            player_comments_lock, player_games_lock, status_update_event,
                            player_comments, player_games
                        )
                    else:
                        batch_comments.append((player_id, name, msg_dt, content))
            
            # 处理评论
            if batch_comments:
                with player_comments_lock:
                    for player_id, name, msg_dt, content in batch_comments:
                        if player_id not in player_comments:
                            player_comments[player_id] = []
                        if [msg_dt, content] not in player_comments[player_id]:
                            player_comments[player_id].append([msg_dt, content])
                            if play_db_sync.db_sync_manager:
                                play_db_sync.db_sync_manager.add_sync_task("comment_update", (player_id, msg_dt, content))
                            status_update_event.put({
                                'type': 'new_comment', 'player_id': player_id,
                                'comment_text': content, 'comment_dt': msg_dt
                            })
                
                for player_id, name, msg_dt, content in batch_comments:
                    _recalculate_and_update_comments_after_game(
                        player_id, player_info, player_comments, player_games, logger,
                        player_info_lock, player_comments_lock, player_games_lock
                    )
                
            # 处理游戏请求
            if batch_game_requests:
                updated_queue_snapshot, processed_players = process_batch_game_requests(
                    batch_game_requests, player_info, player_comments, player_games,
                    in_que, queue_lock, config, current_player, play_db_sync.db_sync_manager, 
                    player_games_lock, status_update_event
                )
                # 注意：现在 process_batch_game_requests 内部已经发送了 queue_update 事件
                logger.debug(f"[消息处理] 批量游戏请求处理完成，处理了 {len(processed_players)} 个玩家")

def handle_status_updates_worker(status_update_event, gui_update_queue, stop_flag, 
                                player_comments, player_games, player_info, current_player, config, 
                                db_sync_manager, player_comments_lock, player_games_lock, 
                                player_info_lock, move_service_client):
    """处理状态更新队列中的更新"""
    import Play_obs
    import queue
    import traceback
    
    try:
        Play_obs.init_obs_controller()
        obs_controller = Play_obs.obs_controller
    except Exception as e:
        logger.error(f"初始化OBS控制器失败: {e}")
        obs_controller = None
    
    while stop_flag.get('running', True):
        try:
            update = status_update_event.get(timeout=0.1)
            update_type = update.get('type')
            
            try:
                if update_type == "queue_update":
                    if obs_controller:
                        q = update.get('in_que', [])
                        p = update.get('player_info', {})
                        obs_controller.update_queue_display(q, p)
                    if gui_update_queue:
                        gui_update_queue.put({'type': 'queue_update'})
                    
                elif update_type == "caught":
                    player_name = update.get('player', '')
                    item_name = update.get('item', '')
                    if obs_controller:
                        obs_controller.show_congrats(player_name, item_name)
                    if gui_update_queue:
                        gui_update_queue.put({'type': 'caught', 'item_name': item_name})
                    
                elif update_type == "game_start":
                    player_name = update.get('name', '')
                    if obs_controller:
                        obs_controller.show_current_player(player_name)
                    if gui_update_queue:
                        gui_update_queue.put({
                            'type': 'game_start', 'player_id': update.get('player_id', ''),
                            'player_name': player_name, 'target_id': update.get('target_id', ''),
                            'target_object': update.get('target_object', '')
                        })
                    
                elif update_type == "game_end":
                    if gui_update_queue:
                        gui_update_queue.put({'type': 'game_end'})
                    
                elif update_type == "new_comment":
                    player_id = update.get('player_id', '')
                    total_comments = 0
                    total_games = 0
                    
                    with player_comments_lock:
                        if player_id in player_comments:
                            total_comments = len(player_comments[player_id])
                    
                    with player_games_lock:
                        if player_id in player_games:
                            total_games = len(player_games[player_id])
                    
                    if gui_update_queue:
                        gui_update_queue.put({
                            'type': 'new_comment', 'player_id': player_id,
                            'comment_text': update.get('comment_text', ''),
                            'comment_dt': update.get('comment_dt', ''),
                            'total_comments': total_comments, 'total_games': total_games
                        })
                
                elif update_type == "move_service_event":
                    # 处理来自移动服务的事件
                    request_id = update.get('request_id')
                    player_id = update.get('player_id')
                    player_name = update.get('player_name')
                    event_name = update.get('event_name')
                    event_data = update.get('data', {})
                    
                    logger.info(f"[移动服务事件] ReqID: {request_id}, Player: {player_name}({player_id}), Event: {event_name}")
                    
                    # 检查是否是当前正在处理的玩家
                    if current_player.get('player_id') == player_id and current_player.get('move_command_id') == request_id:
                        if event_name == 'cycle_completed':
                            # 抓取周期完成
                            status = event_data.get('status', 'unknown')
                            message = event_data.get('message', '')
                            
                            # 根据状态确定抓取结果
                            if status == 'success':
                                captured_item = current_player.get('target_object', '未知物体')
                                logger.info(f"[移动服务事件] 玩家 {player_name}({player_id}) 抓取成功: {captured_item}")
                            else:
                                captured_item = "nothing"
                                logger.info(f"[移动服务事件] 玩家 {player_name}({player_id}) 抓取失败: {message}")
                            
                            # 处理游戏完成
                            order_id = current_player.get('order_id')
                            game_start_time = current_player.get('start_time', time.time())
                            
                            _handle_game_completion(
                                player_id, player_name, captured_item, order_id, config, current_player,
                                player_info, player_games, db_sync_manager, status_update_event,
                                player_info_lock, player_games_lock, game_start_time
                            )
                        
                        elif event_name in ['arm_at_hover_position', 'gripper_status_checked', 'arm_at_display_position']:
                            # 这些是中间状态事件，可以用于GUI显示或日志记录
                            logger.debug(f"[移动服务事件] 玩家 {player_name}({player_id}) 中间状态: {event_name}")
                            if gui_update_queue:
                                gui_update_queue.put({
                                    'type': 'move_service_status',
                                    'player_id': player_id,
                                    'event_name': event_name,
                                    'data': event_data
                                })
                        
                        elif event_name == 'operation_error':
                            # 操作错误
                            error_message = event_data.get('error_message', '未知错误')
                            logger.error(f"[移动服务事件] 玩家 {player_name}({player_id}) 操作错误: {error_message}")
                            
                            # 处理为游戏失败
                            order_id = current_player.get('order_id')
                            game_start_time = current_player.get('start_time', time.time())
                            
                            _handle_game_completion(
                                player_id, player_name, "nothing", order_id, config, current_player,
                                player_info, player_games, db_sync_manager, status_update_event,
                                player_info_lock, player_games_lock, game_start_time
                            )
                    else:
                        logger.warning(f"[移动服务事件] 收到非当前玩家的事件: ReqID={request_id}, Event={event_name}, 当前玩家: {current_player.get('player_id')}")
                
            except Exception as e:
                logger.error(f"处理事件 {update_type} 时出错: {e}")
                logger.error(traceback.format_exc())

            status_update_event.task_done()
            
        except queue.Empty:
            pass
        except Exception as e:
            logger.error(f"处理状态更新时出错: {e}")
            logger.error(traceback.format_exc())
            time.sleep(0.5)

def get_orderbook_status_summary() -> Dict[str, int]:
    """获取订单状态簿的统计信息"""
    summary = {'available': 0, 'claimed': 0, 'total': 0}
    with orderbook_status_lock:
        for status in orderbook_status.values():
            summary[status] = summary.get(status, 0) + 1
        summary['total'] = len(orderbook_status)
    return summary

