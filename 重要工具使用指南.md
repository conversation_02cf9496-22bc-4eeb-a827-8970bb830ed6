# 重要工具使用指南

## 📋 核心测试工具（已清理后保留）

### 🎯 主要测试工具

#### 1. `Tool_BatchPlayerSender.py` ⭐ **最佳测试工具**
**用途**: 批量发送玩家消息，测试各种模式
**使用方法**:
```bash
python Tool_BatchPlayerSender.py
```
**功能**:
- 填充模式测试 (1个玩家)
- 平衡模式测试 (8个玩家) 
- 稀疏模式测试 (14个玩家)
- 重负载测试 (60个玩家)
- 自定义批量发送

#### 2. `Tool_MockSender.py` 
**用途**: 模拟消息发送器，使用现有消息文件
**使用方法**:
```bash
python Tool_MockSender.py
```
**功能**:
- 使用MockLiveMSG.txt文件
- 自动生成不同数量玩家消息
- 支持循环模式

#### 3. `simple_timing_check.py`
**用途**: 快速验证虚拟玩家插入时机
**使用方法**:
```bash
python simple_timing_check.py
```
**功能**:
- 显示最近20条游戏记录
- 分析当前队列状态
- 自动判断运行模式

### 🔧 模拟服务

#### 4. `Mock_DetectionService.py`
**用途**: 模拟检测服务，用于独立测试
**使用方法**:
```bash
python Mock_DetectionService.py
```

#### 5. `Mock_MoveService.py`
**用途**: 模拟移动服务，用于独立测试
**使用方法**:
```bash
python Mock_MoveService.py
```

### 📚 文档资源

#### 6. `虚拟玩家系统测试指南.md` ⭐ **完整测试指南**
**内容**: 
- 详细的测试流程
- 各种测试场景
- 验证方法和预期结果
- 重要发现和总结

## 🗂️ 其他重要文件

### 配置文件
- `config_play.yaml` - 主要配置文件
- `托玩家列表.txt` - 虚拟玩家名单

### 核心程序
- `play_main.py` - 主程序
- `Play_*.py` - 各功能模块

### 工具文档
- `Tool_web2OBSpreview.py` - OBS预览工具
- `Tool_DatabaseMonitor.py` - 数据库监控工具

## 🎯 推荐使用流程

### 基础测试
1. 启动模拟服务: `Mock_DetectionService.py` + `Mock_MoveService.py`
2. 启动主程序: `play_main.py`
3. 使用 `Tool_BatchPlayerSender.py` 进行测试
4. 使用 `simple_timing_check.py` 验证结果

### 详细测试
参考 `虚拟玩家系统测试指南.md` 进行完整的系统测试

## ✅ 清理总结

**保留**: 6个核心测试工具 + 必要的系统文件

现在目录更加整洁，每个工具都有明确的用途，避免了重复和混乱。
