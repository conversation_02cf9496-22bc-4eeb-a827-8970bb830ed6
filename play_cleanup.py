import logging
import threading
from typing import Dict, List, Any, Optional, MutableMapping

import Play_receiveMSG
import Play_db
import play_db_sync
from Play_request_guard import stop_health_check

logger = logging.getLogger(__name__)

def perform_cleanup(
    stop_flag: MutableMapping[str, bool],
    obs_controller: Any,
    db_sync_manager: Any,
    queue_lock: threading.Lock,
    in_que: List,
    game_thread: Optional[threading.Thread] = None,
    monitor_thread: Optional[threading.Thread] = None,
    health_stopped: bool = False,
    gui_thread: Optional[threading.Thread] = None,  # 保留兼容性，但不再使用
    order_fetching_thread: Optional[threading.Thread] = None,
    detection_receiver = None,
    move_service_client = None,
    web_display = None,
    virtual_player_manager_thread: Optional[threading.Thread] = None,
    # --- 新增参数 ---
    gui_process: Optional[Any] = None, # GUI进程对象
    mp_gui_queue: Optional[Any] = None # GUI多进程队列
) -> bool:
    """
    执行程序退出时的清理操作
    
    参数:
    - stop_flag: 停止标志字典
    - obs_controller: OBS控制器实例
    - db_sync_manager: 数据库同步管理器实例
    - queue_lock: 队列锁
    - in_que: 游戏请求队列
    - game_thread: 游戏处理线程
    - monitor_thread: 监控线程
    - health_stopped: 健康检查线程是否已停止
    - gui_thread: GUI线程（已弃用，保留兼容性）
    - order_fetching_thread: 订单抓取线程
    - detection_receiver: 检测数据接收器实例
    - move_service_client: 移动服务客户端实例
    - web_display: Web显示实例
    - virtual_player_manager_thread: 虚拟玩家管理器线程
    - gui_process: GUI进程对象（新增）
    - mp_gui_queue: GUI多进程队列（新增）
    
    返回:
    - bool: 清理是否成功完成
    """
    logger.info("[清理] 开始执行清理操作")
    
    # 确保停止标志已设置
    if 'running' in stop_flag:
        stop_flag['running'] = False
        logger.debug("[清理] 设置 stop_flag['running'] = False")
    else:
        logger.warning("[清理] 停止标志字典中未找到'running'键")

    # --- 调整后的清理顺序 ---

    # 1. 停止所有外部交互和数据输入源
    logger.info("[清理] 阶段1: 停止所有外部输入...")
    success = True
    try:
        logger.debug("[清理] 停止消息获取线程...")
        Play_receiveMSG.stop_message_thread()
    except Exception as e:
        logger.error(f"[清理] 停止消息获取线程失败: {e}", exc_info=True)
        success = False

    if move_service_client:
        try:
            logger.debug("[清理] 停止移动服务客户端...")
            move_service_client.stop()
        except Exception as e:
            logger.error(f"[清理] 停止移动服务客户端失败: {e}", exc_info=True)
            success = False

    if detection_receiver:
        try:
            logger.debug("[清理] 断开检测服务器连接...")
            detection_receiver.disconnect()
        except Exception as e:
            logger.error(f"[清理] 停止检测数据接收器失败: {e}", exc_info=True)
            success = False

    # 2. 停止所有独立进程 (GUI, Web)
    logger.info("[清理] 阶段2: 停止独立进程...")
    if mp_gui_queue:
        try:
            logger.debug("[清理] 关闭GUI通信队列...")
            mp_gui_queue.close()
            mp_gui_queue.join_thread()
        except (OSError, ValueError):
            logger.warning("[清理] GUI通信队列可能已关闭或损坏，忽略。")
        except Exception as e:
            logger.error(f"[清理] 关闭GUI通信队列失败: {e}", exc_info=True)

    if gui_process and gui_process.is_alive():
        try:
            logger.debug("[清理] 正在终止GUI进程...")
            gui_process.terminate()
            gui_process.join(timeout=5)
            if gui_process.is_alive():
                logger.warning("[清理] GUI进程在5秒后仍未终止。")
            else:
                logger.info("[清理] GUI进程已终止。")
        except Exception as e:
            logger.error(f"[清理] 终止GUI进程失败: {e}", exc_info=True)

    if web_display:
        try:
            logger.debug("[清理] 停止Web显示进程...")
            web_display.destroy()
        except Exception as e:
            logger.error(f"[清理] 停止Web显示进程失败: {e}", exc_info=True)
            success = False

    # 3. 等待所有内部工作线程结束
    logger.info("[清理] 阶段3: 等待内部线程结束...")
    # (按依赖关系的反向顺序停止)
    thread_map = {
        "游戏线程": (game_thread, 5),
        "订单抓取线程": (order_fetching_thread, 10),
        "虚拟玩家管理器线程": (virtual_player_manager_thread, 5),
        "主循环监控线程": (monitor_thread, 2)
    }

    for name, (thread, timeout) in thread_map.items():
        if thread and thread.is_alive():
            try:
                logger.debug(f"[清理] 等待{name}结束…")
                thread.join(timeout=timeout)
                if thread.is_alive():
                    logger.warning(f"[清理] {name}未在{timeout}秒内结束")
                else:
                    logger.info(f"[清理] {name}已结束")
            except Exception as e:
                logger.error(f"[清理] 等待{name}结束时出错: {e}", exc_info=True)
                success = False

    # 保留兼容性处理
    if gui_thread and gui_thread.is_alive():
        try:
            logger.debug("[清理] 等待旧的GUI线程结束（已弃用）...")
            gui_thread.join(timeout=1)
            if gui_thread.is_alive():
                logger.warning("[清理] 旧的GUI线程未在1秒内结束（预期行为，因为已迁移到独立进程）")
        except Exception as e:
            logger.warning(f"[清理] 等待旧GUI线程时出错（可忽略）: {e}")

    # 4. 停止剩余服务和执行最终数据同步
    logger.info("[清理] 阶段4: 最终数据同步和资源释放...")
    if db_sync_manager:
        try:
            logger.debug("[清理] 停止数据库同步管理器...")
            db_sync_manager.stop() # 这会等待同步线程结束
        except Exception as e:
            logger.error(f"[清理] 停止数据库同步管理器失败: {e}", exc_info=True)
            success = False

    if obs_controller:
        try:
            obs_controller._update_source_visibility(obs_controller.congrats_video_source, False)
            logger.debug("[清理] 强制隐藏 OBS 视频源")
        except Exception as e:
            logger.error(f"[清理] 强制隐藏 OBS 视频源失败: {e}", exc_info=True)
            success = False

    if not health_stopped:
        try:
            logger.debug("[清理] 停止健康检查线程...")
            stop_health_check()
        except Exception as e:
            logger.error(f"[清理] 停止健康检查失败: {e}", exc_info=True)
            success = False

    # 5. 最终数据库操作
    session_id = Play_db.get_current_session_id()
    if session_id and db_sync_manager:
        lock_acquired = False
        try:
            # 使用超时机制获取队列锁，确保不会永久阻塞
            lock_acquired = queue_lock.acquire(timeout=5)
            if lock_acquired:
                logger.debug("[清理] 已获取队列锁，执行最终数据同步...")
                # 直接执行 final_queue_update，确保退出时把 in_que 写入数据库
                db_sync_manager._process_sync_task("final_queue_update", (session_id, in_que.copy()))
                # 再做一次全量同步，刷入其它遗漏的数据
                db_sync_manager._perform_full_sync()
                logger.info("[清理] 最终数据同步已完成")
            else:
                logger.warning("[清理] 获取队列锁超时(5秒)，跳过最终队列同步")
            
            # 关闭会话不需要锁
            logger.debug("[清理] 关闭数据库会话...")
            Play_db.close_session(session_id)
            logger.info("[清理] 数据库会话已关闭")
        except Exception as e:
            logger.error(f"[清理] 最终同步/关闭会话失败: {e}", exc_info=True)
            success = False
        finally:
            if lock_acquired:
                queue_lock.release()
                logger.debug("[清理] 队列锁已释放")

    logger.info(f"[清理] 清理操作{'成功' if success else '部分失败'}")
    return success
