# 抓取状态事件统一说明

## 变更概述

为了简化网络通信和事件处理逻辑，将抓取成功和失败的通知统一为一个事件机制。

## 变更前的机制

**抓取成功时：**
```json
{
    "type": "event",
    "request_id": "req_123",
    "event_name": "object_picked_successfully",
    "data": {
        "message": "物体已成功抓取"
    }
}
```

**抓取失败时：**
```json
{
    "type": "event", 
    "request_id": "req_123",
    "event_name": "cycle_completed",
    "data": {
        "status": "pickup_failed",
        "message": "抓取失败，机械臂已返回起点"
    }
}
```

## 变更后的统一机制

**统一的抓取状态事件：**
```json
{
    "type": "event",
    "request_id": "req_123",
    "event_name": "object_picked",
    "data": {
        "success": true,           // 布尔值：true=成功，false=失败
        "object_id": 5,           // 物品编号（成功和失败都包含）
        "message": "苹果"         // 成功时只包含缓存的物体名称
    }
}
```

**抓取失败示例：**
```json
{
    "type": "event",
    "request_id": "req_123",
    "event_name": "object_picked",
    "data": {
        "success": false,
        "object_id": 5,
        "message": "抓取失败，未检测到物体"  // 失败时使用通用消息
    }
}
```

## 物体名称缓存机制

为了确保事件消息的准确性，系统在开始抓取流程时会缓存目标物体的名称：

- **缓存时机**：在 `move_to_object` 函数开始时，从当前物体列表中获取目标物体的 `class_name` 并缓存
- **使用场景**：抓取成功时，事件消息中只包含缓存的物体名称，如 `"苹果"`
- **缓存原因**：物体检测结果以15Hz频率更新，在抓取过程中物体名称可能发生变化，使用缓存确保消息的一致性

## 优势

1. **逻辑统一**：抓取成功和失败是同一个操作的两种结果状态，使用同一个事件更符合逻辑
2. **简化处理**：客户端只需监听一个事件类型，通过 `success` 字段判断结果
3. **信息完整**：无论成功失败都包含物品编号，便于客户端跟踪
4. **易于扩展**：未来可以在同一事件中添加更多状态信息
5. **名称一致性**：使用缓存的物体名称，避免检测结果变化导致的消息不一致

## 兼容性说明

- `cycle_completed` 事件仍然存在，用于表示整个抓取周期的完成
- 其他事件（如 `arm_at_display_position` 等）保持不变
- `gripper_status_checked` 事件已被删除，统一使用 `object_picked` 事件
- 将 `object_picked_successfully` 和抓取失败的分离机制统一为 `object_picked` 事件

## 客户端适配建议

```python
def handle_object_picked_event(event_data):
    """处理统一的抓取状态事件"""
    success = event_data.get('success', False)
    object_id = event_data.get('object_id')
    message = event_data.get('message', '')
    
    if success:
        print(f"抓取成功：物品 {object_id} - {message}")
        # 处理抓取成功逻辑
    else:
        print(f"抓取失败：物品 {object_id} - {message}")
        # 处理抓取失败逻辑
```
