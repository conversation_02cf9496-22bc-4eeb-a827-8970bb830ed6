"""""
加载配置: 从 YAML 配置文件 (config_play.yaml) 加载程序运行所需的参数。
配置日志: 设置日志记录器，配置日志级别、格式，并将日志同时输出到控制台和文件。
生成玩家ID: 根据平台和昵称生成唯一的哈希值作为玩家ID。
解析消息: 从接收到的消息对象中解析出平台、时间、昵称、玩家ID、内容和头像等字段。
设置信号处理: 设置信号处理函数，以便在接收到终止信号（如 Ctrl+C）时，安全地停止程序。
检查会话: 检查是否存在有效的历史会话，并询问用户是否继续使用该会话，否则创建新的会话。
加载会话数据: 从数据库加载会话相关的玩家信息、评论、游戏记录和队列数据。
初始化会话: 初始化会话，包括创建或加载会话数据，初始化 OBS 控制器和数据库同步管理器。
主循环监控: 启动一个监控线程，定期检查主循环的运行状态，并在主循环卡住时发出警告。
订单抓取: 启动一个订单抓取线程，定期从指定来源抓取订单信息，并更新到数据库。
"""""
# --- 关键修改：导入 MutableMapping ---
from typing import Dict, List, Tuple, Any, Optional, Union, cast, TypeVar, MutableMapping
from threading import Lock, RLock
import yaml
import logging
import hashlib
import signal
import sys
import traceback
import threading
import copy
from datetime import datetime, timedelta

import Play_db
import Play_obs
import play_db_sync
import play_processing
from play_processing import _recalculate_and_update_comments_after_game

logger: Optional[logging.Logger] = None

def load_config(config_file='config_play.yaml') -> Dict[str, Any]:
    """从配置文件加载参数"""
    global _config_file_mtime_cache

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 初始化配置文件修改时间缓存
        current_mtime = get_config_file_mtime(config_file)
        if current_mtime is not None:
            _config_file_mtime_cache[config_file] = current_mtime

        return config
    except FileNotFoundError:
        print(f"错误：找不到配置文件 {config_file}")
        sys.exit(1)
    except yaml.YAMLError:
        print(f"错误：YAML配置文件 {config_file} 格式不正确")
        sys.exit(1)
    except Exception as e:
        print(f"加载配置文件时发生未知错误: {e}")
        sys.exit(1)

# 配置重新加载锁，确保线程安全
_config_reload_lock = threading.Lock()

# 配置文件修改时间缓存
_config_file_mtime_cache = {}

def get_config_file_mtime(config_file='config_play.yaml') -> Optional[float]:
    """获取配置文件的修改时间"""
    try:
        import os
        return os.path.getmtime(config_file)
    except (OSError, FileNotFoundError):
        return None

def reload_specific_config_sections(config_file='config_play.yaml', force_reload=False) -> Optional[Dict[str, Any]]:
    """
    重新加载配置文件中的特定部分：game、obs（除enabled、网址、端口、密码外）、web_display（除enabled、端口外）

    Args:
        config_file: 配置文件路径
        force_reload: 是否强制重新加载，忽略文件修改时间检查

    Returns:
        包含这三个部分的字典，如果无需更新或加载失败返回None
    """
    global logger, _config_file_mtime_cache

    with _config_reload_lock:
        try:
            # 检查文件修改时间
            current_mtime = get_config_file_mtime(config_file)
            if current_mtime is None:
                if logger:
                    logger.error(f"无法获取配置文件 {config_file} 的修改时间")
                return None

            # 如果不是强制重新加载，检查文件是否有修改
            if not force_reload:
                cached_mtime = _config_file_mtime_cache.get(config_file)
                if cached_mtime is not None and cached_mtime == current_mtime:
                    if logger:
                        logger.debug(f"配置文件 {config_file} 未发生修改，跳过重新加载")
                    return None

            if logger:
                logger.info(f"开始重新加载配置文件的特定部分... (修改时间: {current_mtime})")

            with open(config_file, 'r', encoding='utf-8') as f:
                new_config = yaml.safe_load(f)

            # 更新修改时间缓存
            _config_file_mtime_cache[config_file] = current_mtime

            # 提取需要更新的配置部分
            updated_sections = {}

            # 1. game 部分（完整加载）
            if 'game' in new_config:
                updated_sections['game'] = copy.deepcopy(new_config['game'])
                if logger:
                    logger.info(f"重新加载 game 配置部分")

            # 2. obs 部分（除了 enabled、host、port、password 外的所有参数）
            if 'obs' in new_config:
                obs_config = copy.deepcopy(new_config['obs'])
                # 移除不允许运行时更改的参数
                for key in ['enabled', 'host', 'port', 'password']:
                    if key in obs_config:
                        del obs_config[key]
                updated_sections['obs'] = obs_config
                if logger:
                    logger.info(f"重新加载 obs 配置部分（除连接参数外）")

            # 3. web_display 部分（除了 enabled、websocket_port 外的所有参数）
            if 'web_display' in new_config:
                web_display_config = copy.deepcopy(new_config['web_display'])
                # 移除不允许运行时更改的参数
                for key in ['enabled', 'websocket_port']:
                    if key in web_display_config:
                        del web_display_config[key]
                updated_sections['web_display'] = web_display_config
                if logger:
                    logger.info(f"重新加载 web_display 配置部分（除启用和端口参数外）")

            if logger:
                logger.info(f"配置重新加载完成，更新了 {len(updated_sections)} 个配置部分")

            return updated_sections

        except FileNotFoundError:
            if logger:
                logger.error(f"配置重新加载失败：找不到配置文件 {config_file}")
            return None
        except yaml.YAMLError as e:
            if logger:
                logger.error(f"配置重新加载失败：YAML格式错误 - {e}")
            return None
        except Exception as e:
            if logger:
                logger.error(f"配置重新加载时发生未知错误: {e}")
            return None

def setup_logging(config):
    """配置日志记录器，同时输出到控制台和文件"""
    global logger
    log_config = config.get('logging', {})
    log_level_str = log_config.get('level', 'INFO').upper()
    console_level_str = log_config.get('console_level', 'INFO').upper()
    file_level_str = log_config.get('file_level', 'INFO').upper()

    log_level = getattr(logging, log_level_str, logging.INFO)
    console_level = getattr(logging, console_level_str, logging.INFO)
    file_level = getattr(logging, file_level_str, logging.INFO)

    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        handler.close()

    root_logger.setLevel(log_level)
    formatter = logging.Formatter('%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s')

    console_handler = logging.StreamHandler()
    console_handler.setLevel(console_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    try:
        file_handler = logging.FileHandler('play_main.log', encoding='utf-8')
        file_handler.setLevel(file_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    except Exception as e:
        print(f"无法创建日志文件 play_main.log: {e}")

    logger = logging.getLogger(__name__)
    
    # 设置其他模块的日志级别
    for module in ['Play_receiveMSG', 'Play_db', 'play_obs', 'request_guard', 'poll_request']:
        logging.getLogger(module).setLevel(log_level)

    if logger:
        logger.info("日志系统初始化完成")
    return logger

def generate_player_id(plat: str, name: str) -> str:
    """根据平台和昵称生成32位唯一哈希值作为玩家ID"""
    plat = plat or '抖音'
    name = name or '匿名用户'
    combined = f"{plat}:{name}"
    hash_obj = hashlib.sha256(combined.encode('utf-8'))
    return hash_obj.hexdigest()[:32]

def parse_message(message: Dict[str, Any], dt: str) -> Tuple[str, str, str, str, str, str]:
    """从消息对象中解析出所需字段"""
    plat = message.get('plat', '抖音')
    name = message.get('name', '')
    player = message.get('player', '')

    if not player and not name:
        return ('', '', '', '', '', '')

    if not player:
        player = generate_player_id(plat, name)

    return (plat, dt, name, player, message.get('content', ''), message.get('head_img', ''))

def setup_signal_handler(stop_flag: MutableMapping[str, bool], *args, **kwargs):
    """设置信号处理（Ctrl+C 退出），只做 stop_flag 标志置位"""
    def signal_handler(sig, frame):
        if stop_flag.get('running', True):
            if logger:
                logger.info("收到终止信号，标志置 False，主线程将退出并在 finally 做清理")
            stop_flag['running'] = False

    signal.signal(signal.SIGINT, signal_handler)
    if logger:
        logger.info("信号处理器设置完成（仅置标志）")

def check_previous_session(config: Dict[str, Any]) -> Optional[int]:
    """检查是否有有效的上次会话，并询问用户是否继续"""
    if logger:
        logger.info("检查历史会话记录...")
    
    # 确保数据库模块使用正确的配置
    Play_db.set_config(config)
    
    session_info = Play_db.get_session_info_batch()
    last_session = session_info['last_session']
    
    if not last_session:
        if logger:
            logger.info("未找到历史会话记录，将创建新会话")
        return Play_db.create_new_session()

    session_id = last_session['session_id']
    session_expire_hours = config.get('database', {}).get('session_expire_hours', 24)

    try:
        start_time = datetime.fromisoformat(last_session['start_time'])
        expire_time = start_time + timedelta(hours=session_expire_hours)
        session_valid = datetime.now() <= expire_time
    except ValueError:
        if logger:
            logger.error(f"解析会话开始时间出错: {last_session['start_time']}")
        session_valid = False

    if not session_valid:
        if logger:
            logger.info(f"上次会话(ID:{session_id})已过期，将创建新会话")
        if last_session['status'] == 'active':
            Play_db.close_session(session_id)
        return Play_db.create_new_session()

    # 显示会话信息
    stats = session_info['stats']
    recent_comments = session_info['recent_comments']
    queue_items = session_info['queue_items']

    if logger:
        logger.info("\n" + "="*50)
        logger.info(f"发现有效的历史会话(ID:{session_id})")
        logger.info(f"开始时间: {last_session['start_time']}")
        logger.info(f"状态: {last_session['status']}")
        logger.info(f"玩家数量: {stats.get('player_count', 0)}")
        logger.info(f"评论数量: {stats.get('comment_count', 0)}")
        logger.info(f"游戏次数: {stats.get('game_count', 0)}")

    prize_stats = stats.get('prize_stats', {})
    if prize_stats and logger:
        logger.info("奖品统计:")
        for prize, count in prize_stats.items():
            logger.info(f"  - {prize}: {count}个")

    if recent_comments and logger:
        logger.info("\n最近的评论:")
        for i, comment in enumerate(recent_comments[:5]):  # 只显示前5条
            logger.info(f"{i+1}. {comment['name']}: {comment['content']}")

    if queue_items and logger:
        logger.info("\n当前排队列表:")
        for i, item in enumerate(queue_items[:5]):  # 只显示前5个
            logger.info(f"{i+1}. {item['name']}({item['player_id'][:8]}...): 优先级 {item['priority']:.2f}")

    if logger:
        logger.info("\n" + "="*50)

    while True:
        choice = input("\n是否继续上次会话? (y/n): ").strip().lower()
        if choice in ('y', 'yes'):
            if logger:
                logger.info("将继续上次会话...")
            if last_session['status'] == 'closed':
                Play_db.reactivate_session(session_id)
                if logger:
                    logger.info(f"会话(ID:{session_id})已从关闭状态重新激活")
            Play_db.set_current_session_id(session_id)
            return session_id
        elif choice in ('n', 'no'):
            if logger:
                logger.info("将创建新会话...")
            if last_session['status'] == 'active':
                Play_db.close_session(session_id)
            return Play_db.create_new_session()
        else:
            if logger:
                logger.info("无效输入，请输入 y 或 n")

def load_session_data(session_id: int) -> Tuple[Dict, Dict, Dict, List]:
    """从数据库加载会话数据到内存"""
    if logger:
        logger.info(f"正在从数据库加载会话 {session_id} 的数据...")

    player_info = Play_db.get_player_info(session_id)
    player_comments = Play_db.get_player_comments(session_id)
    player_games = Play_db.get_player_games(session_id)
    queue = Play_db.reconstruct_queue(session_id)

    # 确保必要字段有默认值（已移除 paid 字段相关处理）
    if logger:
        logger.info(f"已加载 {len(player_info)} 名玩家信息, {sum(len(comments) for comments in player_comments.values())} 条评论, "
                    f"{sum(len(games) for games in player_games.values())} 条游戏记录, {len(queue)} 个队列项")

    return player_info, player_comments, player_games, queue

def init_session(config: Dict[str, Any], player_info: Dict, player_comments: Dict, player_games: Dict,
                in_que: List, current_player: Dict, player_info_lock: RLock,
                player_comments_lock: RLock, player_games_lock: RLock,
                queue_lock: Lock) -> Tuple[int, Any, Any, bool, Optional[str]]:
    """初始化会话，包括创建或加载会话数据、初始化OBS控制器和数据库同步管理器"""
    from play_processing import orderbook_status, orderbook_status_lock
    
    # 设置数据库配置
    Play_db.set_config(config)
    
    # 初始化 OBS 控制器
    obs_controller = Play_obs.init_obs_controller(config)

    # 会话管理: 检查或创建会话
    session_id = check_previous_session(config)
    if session_id is None:
        if logger:
            logger.critical("未能获取有效的会话ID。程序无法继续。")
        raise RuntimeError("未能获取有效的会话ID。")

    if logger:
        logger.info(f"当前使用会话 ID: {session_id}")
    
    # 修正：确保设置当前会话ID
    Play_db.set_current_session_id(session_id)
    
    session_info = Play_db.get_session_info_batch(session_id)
    session_details = session_info['session_details']
    is_new_session = session_info['is_new_session']
    
    session_start_time_iso = session_details.get('start_time') if session_details else None
    if logger:
        logger.info(f"会话开始时间: {session_start_time_iso}")

    # 初始化共享数据结构
    loaded_player_info_for_sync = None
    loaded_player_comments_for_sync = None
    loaded_player_games_for_sync = None
    
    if is_new_session:
        if logger:
            logger.info("使用新创建的会话，初始化空数据结构")
        with player_info_lock, player_comments_lock, player_games_lock, queue_lock:
            player_info.clear()
            player_comments.clear()
            player_games.clear()
            in_que.clear()
    else:
        if logger:
            logger.info(f"正在从数据库加载会话 {session_id} 的数据...")
        loaded_player_info, loaded_player_comments, loaded_player_games, loaded_queue = load_session_data(session_id)
        
        # 更新全局变量
        with player_info_lock:
            player_info.clear()
            player_info.update(loaded_player_info)
            
            # 添加缺失字段，包含 free_games_used_this_session
            for player_id in player_info:
                player_data = player_info[player_id]
                for field in ['temp_order_toVerify', 'temp_his_orderID_toUse', 'pending_game_entry_details', 'free_games_used_this_session']:
                    if field not in player_data:
                        # free_games_used_this_session 默认为 0，其他为 None
                        player_data[field] = 0 if field == 'free_games_used_this_session' else None
                
        with player_comments_lock:
            player_comments.clear()
            player_comments.update(loaded_player_comments)
        with player_games_lock:
            player_games.clear()
            player_games.update(loaded_player_games)
        with queue_lock:
            in_que.clear()
            in_que.extend(loaded_queue)

        # 重新计算有效评论数
        with player_comments_lock, player_games_lock, player_info_lock:
            for pid in list(player_info.keys()):
                _recalculate_and_update_comments_after_game(
                    pid, player_info, player_comments, player_games,
                    logger, cast(Optional[Lock], player_info_lock), 
                    cast(Optional[Lock], player_comments_lock), 
                    cast(Optional[Lock], player_games_lock)
                )

        # 重新排序队列
        if in_que:
            with queue_lock, player_info_lock, player_comments_lock, player_games_lock:
                play_processing.update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)

        # 保存加载的数据
        loaded_player_info_for_sync = loaded_player_info
        loaded_player_comments_for_sync = loaded_player_comments
        loaded_player_games_for_sync = loaded_player_games
    
    # 新增：拉取所有pending_orders，初始化orderbook_status（保持原有状态）
    all_pending_orders = Play_db.get_all_pending_orders_with_status()
    with orderbook_status_lock:
        for order_row in all_pending_orders:
            order_id = order_row.get('order_id')
            order_status = order_row.get('status', 'available')
            if order_id:
                orderbook_status[order_id] = order_status
                if logger:
                    logger.debug(f"[会话初始化] 订单 {order_id} 状态初始化为: {order_status}")
    
    if all_pending_orders and logger:
        logger.info(f"[会话初始化] 已加载 {len(all_pending_orders)} 个订单到状态簿")

    # 初始化数据库同步管理器
    db_sync_manager = play_db_sync.init_sync_manager(config)
    
    if not is_new_session:
        db_sync_manager.start(session_id, queue_lock, current_player,
                            player_info_lock=cast(Optional[Lock], player_info_lock), 
                            player_comments_lock=cast(Optional[Lock], player_comments_lock),
                            player_games_lock=cast(Optional[Lock], player_games_lock), 
                            initial_player_info=loaded_player_info_for_sync,
                            initial_player_comments=loaded_player_comments_for_sync,
                            initial_player_games=loaded_player_games_for_sync)
    else:
        db_sync_manager.start(session_id, queue_lock, current_player,
                            player_info_lock=cast(Optional[Lock], player_info_lock), 
                            player_comments_lock=cast(Optional[Lock], player_comments_lock),
                            player_games_lock=cast(Optional[Lock], player_games_lock))
    
    return session_id, obs_controller, db_sync_manager, is_new_session, session_start_time_iso

def monitor_main_loop_worker(stop_flag, last_loop_time_ref):
    """主循环监控线程函数"""
    import time
    
    while stop_flag.get('running', True):
        time.sleep(10)
        current = time.time()
        if current - last_loop_time_ref[0] > 20:
            if logger:
                logger.warning(f"[监控线程] 警告：主循环可能卡住了！距离上次活动: {current - last_loop_time_ref[0]:.1f} 秒")

async def _actual_order_scraping_logic(scraper, config, earliest_creation_time_str=None, current_logger: Optional[logging.Logger] = None):
    """实际执行订单抓取的异步函数"""
    orders = []
    try:
        if current_logger:
            current_logger.info(f"[订单线程] 开始抓取订单，最早时间限制: {earliest_creation_time_str or '无'}")
        orders = await scraper.scrape_orders(earliest_creation_time_str)
        if current_logger:
            current_logger.info(f"[订单线程] 抓取完成，获取到 {len(orders)} 个订单")
    except Exception as e:
        if current_logger:
            current_logger.error(f"[订单线程] 订单抓取失败: {e}")
            import traceback
            current_logger.error(traceback.format_exc())
    return orders

def order_fetching_worker(config, session_id, init_start_time_iso, player_info, player_info_lock, in_que, queue_lock, player_comments, player_games, 
                         player_comments_lock, player_games_lock, status_update_event, stop_flag):
    """订单抓取线程的工作函数 - 使用状态管理模式"""
    import asyncio
    import time
    import copy
    import traceback
    from datetime import datetime, timedelta
    from play_getorder import OrderScraper
    import play_db_sync
    import play_processing
    import Play_db
    import json
    from play_processing import orderbook_status, orderbook_status_lock
    
    orders_config = config.get('orders', {})
    fetch_interval = orders_config.get('fetch_interval_seconds', 300)
    check_earliest_time = orders_config.get('check_earliest_creation_time', False)
    time_buffer_seconds = orders_config.get('order_time_buffer_seconds', 5)
    db_sync_manager = play_db_sync.db_sync_manager
    
    if logger:
        logger.info(f"[订单线程] 启动，抓取间隔 {fetch_interval} 秒")
        logger.info(f"[订单线程] check_earliest_time: {check_earliest_time}, 传入的 init_start_time_iso: {init_start_time_iso}")

    # 设置初始的最早创建时间
    earliest_creation_time_str = None
    last_successful_order_fetch_newest_time_iso = None
    
    if check_earliest_time and init_start_time_iso:
        try:
            dt = datetime.fromisoformat(init_start_time_iso)
            earliest_creation_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            if logger:
                logger.error(f"[订单线程] 转换会话开始时间失败: {e}")
            earliest_creation_time_str = None
    
    # 创建异步事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    # 修改如下：
    # scraper = OrderScraper()
    scraper = OrderScraper(logger=logger, interactive=False)
    browser_initialized = False

    try:
        browser_initialized = loop.run_until_complete(scraper.initialize_browser())
        if not browser_initialized and logger:
            logger.error("[订单线程] 浏览器初始化失败，订单抓取线程将不执行抓取操作。")

        while stop_flag.get('running', True):
            if not browser_initialized:
                for _ in range(int(fetch_interval / 5)):
                    if not stop_flag.get('running', True):
                        break
                    time.sleep(min(5, fetch_interval))
                continue

            try:
                # 从数据库加载最新的订单状态到状态簿（只初始化新订单，不覆盖已有状态）
                available_orders = Play_db.get_all_available_pending_orders()
                orders_for_status_book = []
                for order in available_orders:
                    order_id = order.get('order_id')
                    if order_id:
                        orders_for_status_book.append({
                            'order_id': order_id,
                            'creation_time': order.get('creation_time'),
                            'buyer_account': order.get('buyer_account'),
                            'product_name': order.get('product_name'),
                            'product_quantity': order.get('product_quantity'),
                            'order_details_json': order.get('order_details_json', '{}')
                        })
                
                # 修正：只初始化新订单，不覆盖已有状态
                new_count = 0
                with orderbook_status_lock:
                    for order_data in orders_for_status_book:
                        order_id = order_data.get('order_id')
                        if order_id and order_id not in orderbook_status:
                            # 只在发现新订单时初始化为available
                            orderbook_status[order_id] = 'available'
                            if logger:
                                logger.info(f"[订单状态] 新订单 {order_id} 加入状态簿，状态: available")
                            new_count += 1
                
                if new_count > 0 and logger:
                    logger.info(f"[订单线程] 从数据库加载了 {new_count} 个新订单到状态簿")
                
                # 执行异步抓取逻辑
                orders_from_scraper = loop.run_until_complete(_actual_order_scraping_logic(scraper, config, earliest_creation_time_str, logger))
                
                if orders_from_scraper:
                    newest_time_dt = None
                    orders_to_add_to_db_batch = []  # 修正：初始化为空列表，用于收集本轮所有待处理订单
                    
                    # 修正：将数据库操作移出循环，先收集所有订单数据
                    new_scraped_count = 0
                    with orderbook_status_lock:
                        for order_data_scraped in orders_from_scraper:
                            order_id = order_data_scraped.get('order_id')
                            creation_time_str = order_data_scraped.get('creation_time')
                            
                            if order_id and order_id not in orderbook_status:
                                # 只在发现新订单时初始化为available
                                orderbook_status[order_id] = 'available'
                                if logger:
                                    logger.info(f"[订单状态] 新抓取订单 {order_id} 加入状态簿，状态: available")
                                new_scraped_count += 1
                            
                            # 无论是否是新加入状态簿的，所有从 scraper 获取的订单都应该尝试添加到数据库（或更新）
                            if order_id:
                                
                                db_order_item = {
                                    'order_id': order_id, 'creation_time': creation_time_str,
                                    'buyer_account': order_data_scraped.get('buyer_account', ''),
                                    'product_name': order_data_scraped.get('product_name', ''),
                                    'product_quantity': order_data_scraped.get('product_quantity', 1),
                                    'order_details_json': json.dumps(order_data_scraped, ensure_ascii=False)
                                }
                                orders_to_add_to_db_batch.append(db_order_item)
                                
                                if creation_time_str and creation_time_str != "N/A":
                                    try:
                                        current_order_dt = datetime.strptime(creation_time_str, "%Y-%m-%d %H:%M:%S")
                                        if newest_time_dt is None or current_order_dt > newest_time_dt:
                                            newest_time_dt = current_order_dt
                                    except ValueError:
                                        if logger:
                                            logger.warning(f"无法解析订单时间: {creation_time_str} for order {order_id}")
                    
                    # 修正：在遍历完所有订单后，一次性调用批量数据库操作
                    if orders_to_add_to_db_batch and db_sync_manager:
                        db_sync_manager.add_sync_task("add_pending_orders_batch", orders_to_add_to_db_batch)
                        if logger:
                            logger.info(f"[订单线程] 请求批量添加/更新 {len(orders_to_add_to_db_batch)} 个订单到数据库")
                        # Play_db.py 中的 add_pending_orders_batch 会记录实际插入和更新的数量
                    
                    # 更新下次抓取的最早时间
                    if newest_time_dt:
                        newest_time_str = newest_time_dt.strftime("%Y-%m-%d %H:%M:%S")
                        last_successful_order_fetch_newest_time_iso = newest_time_str
                    
                    if check_earliest_time and last_successful_order_fetch_newest_time_iso:
                        dt = datetime.strptime(last_successful_order_fetch_newest_time_iso, "%Y-%m-%d %H:%M:%S")
                        dt = dt - timedelta(seconds=time_buffer_seconds)
                        earliest_creation_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 验证待处理订单（使用状态簿验证）
                    verified_players_for_queue_update = []
                    player_info_items = []
                    
                    with player_info_lock:
                        player_info_items = [(player_id, player_info[player_id].copy()) 
                                             for player_id in player_info 
                                             if player_info[player_id].get('temp_order_toVerify') is not None]
                    
                    if player_info_items and logger:
                        logger.info(f"[订单线程] 开始验证 {len(player_info_items)} 个待验证订单")
                    
                    for player_id, p_info_val in player_info_items:
                        order_to_verify = p_info_val.get('temp_order_toVerify')
                        player_name = p_info_val.get('name', '未知')
                        
                        if order_to_verify:
                            with orderbook_status_lock, player_info_lock:
                                order_status = orderbook_status.get(order_to_verify)
                                if order_status == 'available':
                                    if logger:
                                        logger.info(f"[订单验证] 订单 {order_to_verify} 验证成功，分配给玩家 {player_name}({player_id})")
                                    # 更新状态为claimed
                                    orderbook_status[order_to_verify] = 'claimed'
                                    player_info[player_id]['temp_his_orderID_toUse'] = order_to_verify
                                    player_info[player_id]['temp_order_toVerify'] = None
                                    
                                    if db_sync_manager:
                                        db_sync_manager.add_sync_task("update_pending_order_status", {
                                            'order_id': order_to_verify,
                                            'status': 'claimed'
                                        })
                                        db_sync_manager.add_sync_task("update_player_order_state", {
                                            'player_id': player_id,
                                            'temp_order_toVerify': None,
                                            'temp_his_orderID_toUse': order_to_verify
                                        })
                                    
                                    pending_entry_details = player_info[player_id].get('pending_game_entry_details')
                                    
                                    if pending_entry_details:
                                        if logger:
                                            logger.info(f"[订单验证] 玩家 {player_name}({player_id}) 有待处理游戏请求，使用订单 {order_to_verify} 立即加入队列")
                                        player_info[player_id]['pending_game_entry_details'] = None
                                        
                                        if db_sync_manager:
                                            db_sync_manager.add_sync_task("update_player_order_state", {
                                                'player_id': player_id,
                                                'pending_game_entry_details_json': None
                                            })
                                        
                                        with player_comments_lock, player_games_lock:
                                            possibility = play_processing.calculate_game_possibility(
                                                player_id, player_info, player_games, config, player_games_lock, order_id=order_to_verify
                                            )
                                            priority = play_processing.calculate_priority(
                                                player_id, pending_entry_details, player_info, player_comments, player_games, config,
                                                order_id=order_to_verify
                                            )
                                        
                                        if logger:
                                            logger.info(f"[订单验证] 玩家 {player_name}({player_id}) 使用订单 {order_to_verify} 加入队列，优先级: {priority:.2f}")
                                        
                                        with queue_lock:
                                            in_que.append((pending_entry_details, possibility, priority, order_to_verify))
                                            play_processing.update_queue_priorities_inplace(
                                                in_que, player_info, player_comments, player_games, config
                                            )
                                        
                                        verified_players_for_queue_update.append(player_id)
                                    else:
                                        if logger:
                                            logger.info(f"[订单验证] 玩家 {player_name}({player_id}) 订单 {order_to_verify} 验证成功，存储为 temp_his_orderID_toUse")
                                elif order_status == 'claimed':
                                    if logger:
                                        logger.warning(f"[订单验证] 订单 {order_to_verify} 已被其他玩家使用，玩家 {player_name}({player_id}) 验证失败")
                                else:
                                    if logger:
                                        logger.warning(f"[订单验证] 订单 {order_to_verify} 不在状态簿中，玩家 {player_name}({player_id}) 验证失败")
                    
                    if verified_players_for_queue_update:
                        with player_info_lock:
                            player_info_copy = copy.deepcopy(player_info)
                        with queue_lock:
                            in_que_copy = copy.deepcopy(in_que)
                        
                        status_update_event.put({
                            'type': 'queue_update',
                            'in_que': in_que_copy,
                            'player_info': player_info_copy
                        })
                        
                        if logger:
                            logger.info(f"[订单验证] {len(verified_players_for_queue_update)} 个玩家的订单验证完成并加入队列")
                
                # 打印状态簿统计信息（需要锁保护）
                status_summary = play_processing.get_orderbook_status_summary()
                if logger:
                    logger.info(f"[订单线程] 当前状态簿统计: {status_summary}")
                
            except Exception as e:
                if logger:
                    logger.error(f"[订单线程] 抓取过程中出错: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
            
            # 等待下次抓取
            for _ in range(int(fetch_interval / 5)):
                if not stop_flag.get('running', True):
                    break
                time.sleep(min(5, fetch_interval))
    
    except Exception as e:
        if logger:
            logger.error(f"[订单线程] 异常退出: {e}")
    finally:
        # 清理浏览器和事件循环
        if browser_initialized and scraper:
            try:
                if not loop.is_closed():
                    loop.run_until_complete(scraper.close())
                    loop.run_until_complete(loop.shutdown_asyncgens())
            except Exception as e:
                if logger:
                    logger.error(f"[订单线程] 关闭浏览器时出错: {e}")
        
        try:
            if not loop.is_closed():
                tasks = asyncio.all_tasks(loop)
                pending_tasks = [task for task in tasks if not task.done()]
                if pending_tasks:
                    for task in pending_tasks:
                        task.cancel()
                    try:
                        loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                    except:
                        pass
                loop.close()
        except Exception as e:
            if logger:
                logger.error(f"[订单线程] 关闭事件循环时出错: {e}")
        
        if logger:
            logger.info("[订单线程] 退出")
