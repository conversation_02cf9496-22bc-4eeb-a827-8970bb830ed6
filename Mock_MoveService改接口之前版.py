#!/usr/bin/env python3
"""
模拟移动服务端程序
用于测试虚拟玩家功能，模拟真实的移动服务端行为
基于真实的move_network.py实现，完全匹配网络协议和事件格式
"""

import socket
import threading
import json
import time
import logging
import random
import datetime
import queue
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_timestamp_mock():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

class MockMoveService:
    """模拟移动服务端，完全匹配真实move_network.py的GameServiceServer行为"""

    def __init__(self, host: str = 'localhost', port: int = 5556):
        self.host = host
        self.port = port
        self.server_socket: Optional[socket.socket] = None
        self.running = True  # 修复：初始化时设为True
        self.clients = []
        self.command_queue = queue.Queue()

        # 启动命令处理线程
        self.command_thread = threading.Thread(
            target=self._command_processor,
            daemon=True,
            name="MockCommandProcessor"
        )
        self.command_thread.start()

    def start(self):
        """启动模拟移动服务端，匹配真实GameServiceServer的行为"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.server_socket.settimeout(0.1)  # 匹配真实实现

            print(f"[{get_timestamp_mock()}] 模拟游戏服务端已启动，监听地址: {self.host}:{self.port}")

            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    print(f"[{get_timestamp_mock()}] 游戏客户端已连接: {client_address}")

                    # 为每个客户端启动一个处理线程，匹配真实实现
                    client_thread = threading.Thread(
                        target=self._handle_game_client,
                        args=(client_socket, client_address),
                        daemon=True,
                        name=f"MockGameClient-{client_address[1]}"
                    )
                    client_thread.start()
                    self.clients.append((client_socket, client_thread))

                except socket.timeout:
                    pass  # 匹配真实实现的超时处理
                except socket.error as e:
                    if self.running:
                        print(f"[{get_timestamp_mock()}] 游戏服务器循环错误: {e}")
                    break

        except Exception as e:
            print(f"[{get_timestamp_mock()}] 启动游戏服务器失败: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()
                print(f"[{get_timestamp_mock()}] 游戏服务器已关闭")
            self.stop()

    def _handle_game_client(self, client_socket: socket.socket, client_address):
        """处理游戏客户端连接，匹配真实handle_game_client的行为"""
        print(f"[{get_timestamp_mock()}] 游戏客户端已连接: {client_address}")

        try:
            while self.running:
                try:
                    # 接收数据，匹配真实实现
                    data = client_socket.recv(4096).decode('utf-8')
                    if not data:
                        print(f"[{get_timestamp_mock()}] 游戏客户端断开连接: {client_address}")
                        break

                    # 处理可能的多个JSON消息，匹配真实实现
                    messages = data.strip().split('\n')
                    for msg in messages:
                        if not msg.strip():
                            continue

                        try:
                            request = json.loads(msg)
                            print(f"[{get_timestamp_mock()}] 收到请求: {request}")

                            response = self._process_game_request(request, client_socket)

                            if response:
                                response_msg = json.dumps(response) + '\n'
                                client_socket.sendall(response_msg.encode('utf-8'))
                                print(f"[{get_timestamp_mock()}] 发送响应: {response}")

                        except json.JSONDecodeError as e:
                            print(f"[{get_timestamp_mock()}] JSON解析错误: {e}")
                            error_response = {
                                'type': 'response',
                                'status': 'error',
                                'message': f'无效的JSON请求: {str(e)}'
                            }
                            response_msg = json.dumps(error_response) + '\n'
                            client_socket.sendall(response_msg.encode('utf-8'))

                except socket.timeout:
                    continue
                except socket.error as e:
                    print(f"[{get_timestamp_mock()}] 客户端 {client_address} 连接错误: {e}")
                    break

        except Exception as e:
            print(f"[{get_timestamp_mock()}] 处理客户端 {client_address} 时出错: {e}")
        finally:
            try:
                client_socket.close()
                print(f"[{get_timestamp_mock()}] 客户端 {client_address} 连接已关闭")
            except:
                pass

    def _process_game_request(self, request: Dict[str, Any], client_socket: socket.socket) -> Dict[str, Any]:
        """处理游戏客户端请求，匹配真实process_game_request的行为"""
        request_type = request.get('type', '')

        if request_type == 'command':
            return self._handle_command_request(request, client_socket)
        elif request_type == 'query':
            return self._handle_query_request(request)
        else:
            return {
                'type': 'response',
                'status': 'error',
                'message': f'未知请求类型: {request_type}'
            }

    def _handle_command_request(self, request: Dict[str, Any], client_socket: socket.socket) -> Dict[str, Any]:
        """处理指令请求，匹配真实handle_command_request的行为"""
        action = request.get('action', '')
        command_id = request.get('command_id', 'unknown')

        if action == 'process_movement':
            params = request.get('params', {})

            command_item = {
                'type': 'movement',
                'params': params,
                'request_id': command_id,
                'client_socket': client_socket
            }

            try:
                self.command_queue.put(command_item, timeout=1.0)
                return {
                    'type': 'response',
                    'command_id': command_id,
                    'status': 'queued',
                    'message': '移动指令已加入队列'
                }
            except queue.Full:
                return {
                    'type': 'response',
                    'command_id': command_id,
                    'status': 'error',
                    'message': '指令队列已满'
                }
        elif action == 'pick_object_by_id':
            params = request.get('params', {})
            object_id = params.get('object_id')

            if object_id is None:
                return {
                    'type': 'response',
                    'command_id': command_id,
                    'status': 'error',
                    'message': '缺少 object_id 参数'
                }

            command_item = {
                'type': 'pick_object',
                'params': params,
                'request_id': command_id,
                'client_socket': client_socket
            }

            try:
                self.command_queue.put(command_item, timeout=1.0)
                return {
                    'type': 'response',
                    'command_id': command_id,
                    'status': 'queued',
                    'message': f'抓取物体 {object_id} 指令已加入队列'
                }
            except queue.Full:
                return {
                    'type': 'response',
                    'command_id': command_id,
                    'status': 'error',
                    'message': '指令队列已满，无法加入抓取指令'
                }
        else:
            return {
                'type': 'response',
                'command_id': command_id,
                'status': 'error',
                'message': f'未知指令动作: {action}'
            }

    def _handle_query_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理查询请求，匹配真实handle_query_request的行为"""
        action = request.get('action', '')

        if action == 'get_gripper_status':
            # 模拟爪子状态查询
            gripper_closed = random.choice([True, False])  # 随机模拟爪子状态

            return {
                'type': 'response',
                'query_id': request.get('query_id'),
                'status': 'success',
                'data': {
                    'object_picked': gripper_closed
                },
                'message': '爪子状态查询成功'
            }
        else:
            return {
                'type': 'response',
                'query_id': request.get('query_id'),
                'status': 'error',
                'message': f'未知查询动作: {action}'
            }

    def _command_processor(self):
        """命令处理器，模拟真实move_main.py中的命令处理循环"""
        print(f"[{get_timestamp_mock()}] 模拟命令处理线程已启动")

        while self.running:
            try:
                command_item = self.command_queue.get(timeout=0.1)

                if command_item is None:  # 退出信号
                    break

                command_type = command_item.get('type', '')
                request_id = command_item.get('request_id', 'unknown')
                client_socket = command_item.get('client_socket')
                params = command_item.get('params', {})

                print(f"[{get_timestamp_mock()}] 处理命令: {command_type}, 请求ID: {request_id}")

                if command_type == 'pick_object':
                    self._process_pick_object_command(params, client_socket, request_id)
                elif command_type == 'movement':
                    self._process_movement_command(params, client_socket, request_id)

                self.command_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"[{get_timestamp_mock()}] 命令处理错误: {e}")
                if command_item:
                    self.command_queue.task_done()

        print(f"[{get_timestamp_mock()}] 模拟命令处理线程结束")

    def _process_pick_object_command(self, params: Dict[str, Any], client_socket: socket.socket, request_id: str):
        """处理抓取物体命令，模拟真实的抓取流程和事件发送"""
        object_id = params.get('object_id')
        player_id = params.get('player_id', '')
        player_name = params.get('player_name', '')
        z_offset_extra = params.get('z_offset_extra', 0.0)

        print(f"[{get_timestamp_mock()}] 开始抓取流程 - 玩家: {player_name}({player_id}), 目标: {object_id}, z_offset: {z_offset_extra}")

        # 模拟移动过程（从游戏开始到抓取物品，无论抓到成功与否）
        time.sleep(random.uniform(2.3, 2.3))

        # 根据z_offset_extra判断是否应该成功
        success = (z_offset_extra == 0.0)

        # 对于虚拟玩家，确保100%成功
        if "VirtualPlayer" in player_id:
            success = True
            #success = random.choice([True, False])  # 随机模拟爪子状态
            print(f"[{get_timestamp_mock()}] 虚拟玩家 {player_name} 确保抓取成功")
        else:
            # 对于真实玩家，添加一些随机性
            if success:
                if random.random() < 0.05:  # 5%概率失败
                    success = False
                    print(f"[{get_timestamp_mock()}] 真实玩家 {player_name} 随机失败（模拟机械故障）")
            else:
                if random.random() < 0.10:  # 10%概率意外成功
                    success = True
                    print(f"[{get_timestamp_mock()}] 真实玩家 {player_name} 意外成功")

        # 发送object_picked事件，匹配真实事件格式
        if success:
            item_name = f"物品_{object_id}"
            message = item_name
        else:
            item_name = "nothing"
            message = "抓取失败，未检测到物体"

        self._send_event(client_socket, request_id, 'object_picked', {
            'success': success,
            'object_id': object_id,
            'message': message
        }, player_id, player_name)

        # 模拟额外的处理时间（从抓取物品，无论成功与否，到回到起点）
        time.sleep(random.uniform(3.5, 3.6))

        # 发送cycle_completed事件
        if success:
            status = 'success'
            cycle_message = '成功抓取并投放，机械臂已返回起点'
        else:
            status = 'pickup_failed'
            cycle_message = '抓取失败，机械臂已返回起点'

        self._send_event(client_socket, request_id, 'cycle_completed', {
            'status': status,
            'message': cycle_message
        }, player_id, player_name)

        print(f"[{get_timestamp_mock()}] 抓取流程完成 - 结果: {status}")

    def _process_movement_command(self, params: Dict[str, Any], client_socket: socket.socket, request_id: str):
        """处理移动命令"""
        print(f"[{get_timestamp_mock()}] 处理移动命令: {params}")
        # 模拟移动时间（优化为快速测试）
        time.sleep(random.uniform(0.7, 0.8))
        print(f"[{get_timestamp_mock()}] 移动命令完成: {request_id}")
        # client_socket 参数保留以匹配接口，但在移动命令中不需要发送事件

    def _send_event(self, client_socket: socket.socket, request_id: str, event_name: str,
                   data: Dict[str, Any], player_id: str = '', player_name: str = ''):
        """发送事件给客户端，匹配真实的事件格式"""
        if not client_socket:
            return

        try:
            event = {
                'type': 'event',
                'request_id': request_id,
                'event_name': event_name,
                'data': data,
                'player_id': player_id,
                'player_name': player_name
            }

            event_msg = json.dumps(event) + '\n'
            client_socket.sendall(event_msg.encode('utf-8'))
            print(f"[{get_timestamp_mock()}] 发送事件 {event_name}: {data}")

        except Exception as e:
            print(f"[{get_timestamp_mock()}] 发送事件 {event_name} 失败: {e}")

    def stop(self):
        """停止服务端"""
        print(f"[{get_timestamp_mock()}] 正在停止模拟移动服务端...")
        self.running = False

        # 停止命令处理线程
        try:
            self.command_queue.put_nowait(None)  # 发送退出信号
        except queue.Full:
            pass

        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass

        # 关闭所有客户端连接
        for client_socket, _ in self.clients:
            try:
                client_socket.close()
            except:
                pass

        print(f"[{get_timestamp_mock()}] 模拟移动服务端已停止")

def main():
    """主函数"""
    print("启动模拟移动服务端...")
    print("完全匹配真实move_network.py的GameServiceServer行为")
    print("支持游戏客户端的command和query请求")
    print("按 Ctrl+C 停止服务")

    service = MockMoveService()
    try:
        service.start()
    except KeyboardInterrupt:
        print(f"\n[{get_timestamp_mock()}] 收到停止信号，正在关闭服务...")
        service.stop()
    except Exception as e:
        print(f"[{get_timestamp_mock()}] 服务运行出错: {e}")
        service.stop()

if __name__ == "__main__":
    main()
