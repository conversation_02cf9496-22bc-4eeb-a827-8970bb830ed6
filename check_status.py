#!/usr/bin/env python3
"""
快速检查数据库状态
"""
import sqlite3

def check_database_status():
    try:
        conn = sqlite3.connect('Play_db.db')
        cursor = conn.cursor()
        
        print('=== 稀疏模式测试结果检查 ===')
        
        # 检查players表
        cursor.execute('SELECT COUNT(*) FROM players')
        total_players = cursor.fetchone()[0]
        print(f'总玩家数: {total_players}')
        
        # 检查真实玩家数量
        cursor.execute('SELECT COUNT(*) FROM players WHERE player_id NOT LIKE "Virtual%"')
        real_players = cursor.fetchone()[0]
        print(f'真实玩家数: {real_players}')
        
        # 检查虚拟玩家数量
        cursor.execute('SELECT COUNT(*) FROM players WHERE player_id LIKE "Virtual%"')
        virtual_players = cursor.fetchone()[0]
        print(f'虚拟玩家数: {virtual_players}')
        
        # 检查games表
        cursor.execute('SELECT COUNT(*) FROM games')
        total_games = cursor.fetchone()[0]
        print(f'总游戏次数: {total_games}')
        
        # 检查queue表
        cursor.execute('SELECT COUNT(*) FROM queue')
        queue_total = cursor.fetchone()[0]
        print(f'当前队列总数: {queue_total}')
        
        cursor.execute('SELECT COUNT(*) FROM queue WHERE player_id LIKE "Virtual%"')
        queue_virtual = cursor.fetchone()[0]
        print(f'队列中虚拟玩家数: {queue_virtual}')
        
        cursor.execute('SELECT COUNT(*) FROM queue WHERE player_id NOT LIKE "Virtual%"')
        queue_real = cursor.fetchone()[0]
        print(f'队列中真实玩家数: {queue_real}')
        
        # 显示最近的游戏记录
        print('\n=== 最近10次游戏记录 ===')
        try:
            cursor.execute('SELECT player_id, result, game_time FROM games ORDER BY game_time DESC LIMIT 10')
            games = cursor.fetchall()
            for i, (pid, result, game_time) in enumerate(games, 1):
                player_type = '虚拟' if pid.startswith('Virtual') else '真实'
                print(f'{i}. {pid}({player_type}) - {result} - {game_time}')
        except Exception as e:
            print(f'获取游戏记录时出错: {e}')
        
        # 稀疏模式验证
        print('\n=== 稀疏模式验证 ===')
        if real_players >= 8:
            print(f'✅ 真实玩家数({real_players}) >= 8，符合稀疏模式条件')
            if virtual_players > 0:
                ratio = real_players / virtual_players if virtual_players > 0 else float('inf')
                print(f'✅ 真实玩家与虚拟玩家比例: {ratio:.1f}:1')
                if ratio >= 7:
                    print('✅ 比例符合稀疏模式预期（约7:1）')
                else:
                    print('⚠️ 比例可能不符合稀疏模式预期')
            else:
                print('⚠️ 没有虚拟玩家参与')
        else:
            print(f'⚠️ 真实玩家数({real_players}) < 8，不符合稀疏模式条件')
        
        conn.close()
        
    except Exception as e:
        print(f'检查数据库时出错: {e}')

if __name__ == '__main__':
    check_database_status()
