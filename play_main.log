2025-07-28 12:25:16,564 - [MainThread] - INFO - 日志系统初始化完成
2025-07-28 12:25:16,565 - [MainThread] - INFO - 应用程序启动
2025-07-28 12:25:17,330 - [MainThread] - INFO - 信号处理器设置完成（仅置标志）
2025-07-28 12:25:17,331 - [MainThread] - INFO - 信号处理器已设置(使用共享状态字典)
2025-07-28 12:25:19,531 - [MainThread] - INFO - Web显示进程启动成功
2025-07-28 12:25:19,700 - [MainThread] - INFO - GUI 进程已通过 multiprocessing.Process 启动, PID: 10580
2025-07-28 12:25:19,703 - [MainThread] - INFO - 每个会话最大免费游戏次数: 1
2025-07-28 12:25:19,709 - [MainThread] - INFO - 初始化全局 OBSController
2025-07-28 12:25:19,710 - [MainThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-28 12:25:19,710 - [MainThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-28 12:25:19,723 - [MainThread] - INFO - Successfully identified ReqClient with the server using RPC version:1
2025-07-28 12:25:19,724 - [MainThread] - INFO - 成功连接到OBS WebSocket
2025-07-28 12:25:19,725 - [MainThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetCurrentProgramScene', 'requestId': 259}}
2025-07-28 12:25:19,728 - [MainThread] - DEBUG - Response received {'d': {'requestId': 259, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetCurrentProgramScene', 'responseData': {'currentProgramSceneName': '场景1'}}, 'op': 7}
2025-07-28 12:25:19,735 - [MainThread] - INFO - 当前场景: 场景1
2025-07-28 12:25:19,736 - [MainThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetSceneItemList', 'requestId': 841, 'requestData': {'sceneName': '场景1'}}}
2025-07-28 12:25:19,747 - [MainThread] - DEBUG - Response received {'d': {'requestId': 841, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetSceneItemList', 'responseData': {'sceneItems': [{'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 58, 'sceneItemIndex': 0, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '没抓到特效', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'color_source_v3', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 49, 'sceneItemIndex': 1, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '色源', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'vlc_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 40, 'sceneItemIndex': 2, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 8, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 540.0, 'positionY': 1920.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '背景视频', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'image_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 62, 'sceneItemIndex': 3, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '临时图像', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'dshow_input', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 46, 'sceneItemIndex': 4, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1089.0, 'positionX': 1089.0, 'positionY': -16.0, 'rotation': 90.0, 'scaleX': 1.0083333253860474, 'scaleY': 1.0083333253860474, 'sourceHeight': 1080.0, 'sourceWidth': 1920.0, 'width': 1936.0}, 'sourceName': '主摄像头', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 1, 'sceneItemIndex': 5, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 116.22856903076172, 'positionX': 214.0, 'positionY': 44.77142333984375, 'rotation': 0.0, 'scaleX': 1.6139534711837769, 'scaleY': 1.6142857074737549, 'sourceHeight': 72.0, 'sourceWidth': 444.0, 'width': 716.5953369140625}, 'sourceName': '大标题', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 60, 'sceneItemIndex': 6, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 14.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4541666507720947, 'scaleY': 1.4546159505844116, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 53, 'sceneItemIndex': 7, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': -4.0, 'rotation': 0.0, 'scaleX': 1.5, 'scaleY': 1.5003879070281982, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效2', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 56, 'sceneItemIndex': 8, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 21.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4416667222976685, 'scaleY': 1.44142746925354, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效3', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 57, 'sceneItemIndex': 9, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 22.0, 'positionY': -163.0, 'rotation': 0.0, 'scaleX': 1.4375, 'scaleY': 1.4375485181808472, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效4', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 34, 'sceneItemIndex': 10, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 164.0, 'positionX': 454.0, 'positionY': 132.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 164.0, 'sourceWidth': 626.0, 'width': 626.0}, 'sourceName': '说明1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'monitor_capture', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 41, 'sceneItemIndex': 11, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 734.0, 'rotation': 0.0, 'scaleX': 0.6026041507720947, 'scaleY': 0.6027777791023254, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '显示器采集', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'browser_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 50, 'sceneItemIndex': 12, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '浏览器显示', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}]}}, 'op': 7}
2025-07-28 12:25:19,748 - [MainThread] - INFO - 完成源缓存，已缓存 5 个源的场景项ID
2025-07-28 12:25:19,750 - [MainThread] - INFO - 检查历史会话记录...
2025-07-28 12:25:19,753 - [MainThread] - INFO - 未找到历史会话记录，将创建新会话
2025-07-28 12:25:19,775 - [MainThread] - INFO - 已创建新会话，ID: 1
2025-07-28 12:25:19,777 - [MainThread] - INFO - 当前使用会话 ID: 1
2025-07-28 12:25:19,780 - [MainThread] - DEBUG - 当前会话ID已设置为: 1
2025-07-28 12:25:19,792 - [MainThread] - INFO - 会话开始时间: 2025-07-28T12:25:19.755073
2025-07-28 12:25:19,798 - [MainThread] - INFO - 使用新创建的会话，初始化空数据结构
2025-07-28 12:25:19,814 - [MainThread] - INFO - 初始化全局数据库同步管理器
2025-07-28 12:25:19,815 - [MainThread] - INFO - 启动数据库同步线程，同步间隔: 10秒
2025-07-28 12:25:19,818 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:25:19,819 - [MainThread] - INFO - 会话初始化完成，新会话，会话ID: 1
2025-07-28 12:25:19,820 - [DBSyncThread] - DEBUG - [全量同步] player_info 为空，跳过 comments_after_game 同步。
2025-07-28 12:25:19,857 - [MainThread] - INFO - 成功连接到移动服务 localhost:5556
2025-07-28 12:25:19,858 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已启动。
2025-07-28 12:25:19,859 - [MainThread] - INFO - 移动服务客户端已启动，连接到: localhost:5556
2025-07-28 12:25:19,860 - [DetectionReceiver] - INFO - 检测数据接收器启动
2025-07-28 12:25:19,860 - [MainThread] - INFO - 检测数据接收器线程已启动
2025-07-28 12:25:19,862 - [MainThread] - INFO - [虚拟玩家] 成功加载 785 个虚拟玩家，起始索引: 595
2025-07-28 12:25:19,863 - [MainThread] - INFO - [虚拟玩家] 管理器启动...
2025-07-28 12:25:19,863 - [MainThread] - INFO - 虚拟玩家管理器已启动
2025-07-28 12:25:19,864 - [MainThread] - INFO - 状态更新处理线程已启动
2025-07-28 12:25:19,865 - [MainThread] - INFO - GUI的FPS发送线程已启动
2025-07-28 12:25:19,866 - [MainThread] - INFO - 游戏处理线程已启动
2025-07-28 12:25:19,867 - [DetectionReceiver] - INFO - 成功连接到检测服务器 localhost:5555
2025-07-28 12:25:19,867 - [MainThread] - INFO - 主循环监控线程已启动
2025-07-28 12:25:19,869 - [DetectionReceiver] - DEBUG - 已发送订阅命令
2025-07-28 12:25:19,868 - [MainThread] - INFO - [消息线程] 启动消息获取后台线程
2025-07-28 12:25:19,870 - [StatusUpdateThread] - INFO - [重试机制] 检测到移动服务端已恢复，若有pending_retry_player将立即重试。
2025-07-28 12:25:19,870 - [DetectionReceiver] - INFO - 收到订阅确认: Subscription successful for client 1
2025-07-28 12:25:19,872 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:25:19,871 - [MessagePollThread] - DEBUG - [消息线程] 开始运行消息获取线程
2025-07-28 12:25:19,872 - [MainThread] - INFO - [消息线程] 消息获取线程已启动
2025-07-28 12:25:19,878 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:25:19,880 - [MessagePollThread] - DEBUG - [消息流] 启动健康检查
2025-07-28 12:25:19,881 - [MainThread] - INFO - 消息获取后台线程已启动，开始接收消息...
2025-07-28 12:25:19,885 - [MainThread] - INFO - 开始处理消息，按Ctrl+C停止...
2025-07-28 12:25:19,886 - [HealthCheckThread] - DEBUG - [健康检查] 任务开始
2025-07-28 12:25:19,886 - [MessagePollThread] - INFO - 健康检查线程已启动，间隔: 30秒
2025-07-28 12:25:19,887 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:25:19,887 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:25:19,888 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 3.6 秒
2025-07-28 12:25:19,893 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:19,889 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:25:19,896 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:25:19,902 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:25:19,907 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:25:19,909 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 1
2025-07-28 12:25:19,910 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:25:19,911 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:19,935 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:19,938 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:19,986 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,033 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,080 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,124 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,172 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,222 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,264 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,311 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,362 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,390 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:25:20,390 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:25:20,409 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,456 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,501 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,549 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,594 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,643 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,690 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,736 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,786 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,831 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,882 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,895 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:25:20,897 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:25:20,924 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:20,975 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,022 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,070 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,116 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,162 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,211 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,258 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,305 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,352 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,400 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,414 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:25:21,417 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:25:21,446 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,493 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,542 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,590 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,637 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,683 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,731 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,778 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,826 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,868 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,884 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(7)，添加虚拟玩家。
2025-07-28 12:25:21,886 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小龙女的现代生活(0次)', '云散了(0次)', '清风文具店(0次)', '笑出腹肌的胖子(0次)', '化学物质大发现(0次)']。已选择: 小龙女的现代生活
2025-07-28 12:25:21,904 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:21,930 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {}, pending_retry_player: None
2025-07-28 12:25:21,933 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:25:21,935 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小龙女的现代生活(VirtualPlayerID0596) 确保抓中, z_offset_extra=0
2025-07-28 12:25:21,939 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=a900bee6-6a8d-43be-9ecd-4afe6f9bd213, Player=小龙女的现代生活, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:25:21,941 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:25:21,942 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 抓取指令已发送到移动服务，命令ID: a900bee6-6a8d-43be-9ecd-4afe6f9bd213
2025-07-28 12:25:21,951 - [MoveServiceEventReceiver] - INFO - 抓取指令 a900bee6-6a8d-43be-9ecd-4afe6f9bd213 已被移动服务接受并加入队列
2025-07-28 12:25:21,964 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:21,962 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C8637F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 1
2025-07-28 12:25:21,966 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C8637F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:21,968 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:25:22,008 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,054 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,099 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,147 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,194 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,239 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,286 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,334 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,363 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0596 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:22,374 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0596 订单状态，但该玩家在会话 1 中不存在
2025-07-28 12:25:22,382 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,428 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,458 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213'}, pending_retry_player: None
2025-07-28 12:25:22,458 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:25:22,476 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,524 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,568 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,617 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,664 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,712 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,759 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,808 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,854 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,905 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,948 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:22,965 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213'}, pending_retry_player: None
2025-07-28 12:25:22,966 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:25:22,998 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,046 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,091 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,138 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,182 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,228 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,277 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,323 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,372 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,418 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,465 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,481 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213'}, pending_retry_player: None
2025-07-28 12:25:23,481 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:25:23,513 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,560 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,609 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,657 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,705 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,751 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,799 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,845 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,892 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:25:23,921 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已完成游戏，总游戏次数: 1
2025-07-28 12:25:23,922 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 下次将以付费状态排队。
2025-07-28 12:25:23,923 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(7)，添加虚拟玩家。
2025-07-28 12:25:23,924 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['云散了(0次)', '清风文具店(0次)', '笑出腹肌的胖子(0次)', '化学物质大发现(0次)', '小龙女的现代生活(1次)']。已选择: 云散了
2025-07-28 12:25:23,924 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:23,985 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213'}, pending_retry_player: None
2025-07-28 12:25:23,984 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:25:23,993 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:23,995 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:25:23,999 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:24,012 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:24,507 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213'}, pending_retry_player: None
2025-07-28 12:25:24,513 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:24,963 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:25:24,964 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=a900bee6-6a8d-43be-9ecd-4afe6f9bd213
2025-07-28 12:25:24,968 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:25:24,968 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=2
2025-07-28 12:25:24,969 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:25:24,969 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'player_id': 'VirtualPlayerID0596', 'player_name': '小龙女的现代生活', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:25:24,969 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小龙女的现代生活(VirtualPlayerID0596), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: a900bee6-6a8d-43be-9ecd-4afe6f9bd213, 消息: 物品_2
2025-07-28 12:25:24,970 - [StatusUpdateThread] - INFO - 玩家 小龙女的现代生活 本次是第 1 次成功抓取。
2025-07-28 12:25:24,970 - [StatusUpdateThread] - INFO - 为玩家 小龙女的现代生活 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:25:24,971 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:25:24,972 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 822, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:25:24,973 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 822, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:25,026 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:25,028 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:25,543 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:25,546 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:25,937 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(1) < 目标(3)，添加虚拟玩家。
2025-07-28 12:25:25,941 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['清风文具店(0次)', '笑出腹肌的胖子(0次)', '化学物质大发现(0次)', '小龙女的现代生活(1次)']。已选择: 清风文具店
2025-07-28 12:25:25,943 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:26,049 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 2
2025-07-28 12:25:26,056 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:26,057 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:25:26,063 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:26,068 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:26,580 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:26,588 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:27,098 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:27,101 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:27,619 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:27,624 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:27,951 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(5)，添加虚拟玩家。
2025-07-28 12:25:27,954 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['笑出腹肌的胖子(0次)', '化学物质大发现(0次)', '小龙女的现代生活(1次)']。已选择: 笑出腹肌的胖子
2025-07-28 12:25:27,956 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:27,982 - [Thread-1] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 721, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:25:27,987 - [Thread-1] - DEBUG - Response received {'d': {'requestId': 721, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:28,061 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:25:28,068 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:25:28,070 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:28,090 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:28,140 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:28,147 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:28,658 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:28,665 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:29,175 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676721.9335809, 'target_id': '2', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:29,182 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:29,488 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'a900bee6-6a8d-43be-9ecd-4afe6f9bd213', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:25:29,490 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=a900bee6-6a8d-43be-9ecd-4afe6f9bd213
2025-07-28 12:25:29,492 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:25:29,493 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=2
2025-07-28 12:25:29,494 - [MoveServiceEventReceiver] - INFO - 移动服务请求 a900bee6-6a8d-43be-9ecd-4afe6f9bd213 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:25:29,495 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小龙女的现代生活(VirtualPlayerID0596)
2025-07-28 12:25:29,500 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:25:29,501 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小龙女的现代生活(VirtualPlayerID0596), 结果: 物品_2, 订单: None
2025-07-28 12:25:29,509 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:25:29,512 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:25:29,693 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {}, pending_retry_player: None
2025-07-28 12:25:29,700 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:25:29,704 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 云散了(VirtualPlayerID0597) 确保抓中, z_offset_extra=0
2025-07-28 12:25:29,715 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=ebd05947-5c7a-4fbb-9ceb-175651537e62, Player=云散了, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:25:29,717 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:25:29,720 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 抓取指令已发送到移动服务，命令ID: ebd05947-5c7a-4fbb-9ceb-175651537e62
2025-07-28 12:25:29,724 - [MoveServiceEventReceiver] - INFO - 抓取指令 ebd05947-5c7a-4fbb-9ceb-175651537e62 已被移动服务接受并加入队列
2025-07-28 12:25:29,971 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已完成游戏，总游戏次数: 1
2025-07-28 12:25:29,974 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 下次将以付费状态排队。
2025-07-28 12:25:29,977 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(7)，添加虚拟玩家。
2025-07-28 12:25:29,979 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['化学物质大发现(0次)', '小龙女的现代生活(1次)', '云散了(1次)']。已选择: 化学物质大发现
2025-07-28 12:25:29,980 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.025秒 - 玩家: VirtualPlayerID0596, 结果: 物品_2, 订单: NULL
2025-07-28 12:25:29,981 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:29,981 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0596] = 1
2025-07-28 12:25:29,985 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0597 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:29,987 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0597 订单状态，但该玩家在会话 1 中不存在
2025-07-28 12:25:30,104 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.016秒
2025-07-28 12:25:30,106 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:25:30,129 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-28 12:25:30,129 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859C40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 3
2025-07-28 12:25:30,132 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859C40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:25:30,135 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:25:30,137 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:25:30,138 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:25:30,140 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=0.0s, 心跳间隔=1s
2025-07-28 12:25:30,142 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:25:30,240 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62'}, pending_retry_player: None
2025-07-28 12:25:30,241 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:30,744 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62'}, pending_retry_player: None
2025-07-28 12:25:30,745 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:31,156 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:25:31,162 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:25:31,166 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:25:31,169 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:25:31,173 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:25:31,176 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:25:31,177 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 2
2025-07-28 12:25:31,179 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:25:31,180 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:31,188 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:31,251 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62'}, pending_retry_player: None
2025-07-28 12:25:31,255 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:31,769 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62'}, pending_retry_player: None
2025-07-28 12:25:31,773 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:31,987 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(3) < 目标(7)，添加虚拟玩家。
2025-07-28 12:25:31,989 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小龙女的现代生活(1次)', '云散了(1次)']。已选择: 小龙女的现代生活
2025-07-28 12:25:31,990 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:32,287 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62'}, pending_retry_player: None
2025-07-28 12:25:32,289 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:32,682 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:25:32,684 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=ebd05947-5c7a-4fbb-9ceb-175651537e62
2025-07-28 12:25:32,685 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:25:32,686 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=1
2025-07-28 12:25:32,687 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:25:32,688 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'player_id': 'VirtualPlayerID0597', 'player_name': '云散了', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:25:32,689 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 云散了(VirtualPlayerID0597), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: ebd05947-5c7a-4fbb-9ceb-175651537e62, 消息: 物品_1
2025-07-28 12:25:32,692 - [StatusUpdateThread] - INFO - 玩家 云散了 本次是第 1 次成功抓取。
2025-07-28 12:25:32,692 - [StatusUpdateThread] - INFO - 为玩家 云散了 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:25:32,694 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:25:32,695 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 394, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:25:32,697 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 394, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:32,793 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:32,795 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:33,219 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989E50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 4
2025-07-28 12:25:33,223 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989E50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:33,224 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:25:33,313 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:33,315 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:33,831 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:33,834 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:34,003 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-28 12:25:34,009 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['云散了(1次)']。已选择: 云散了
2025-07-28 12:25:34,012 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:34,351 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:34,355 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:34,871 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:34,874 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:35,234 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:25:35,240 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:25:35,247 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:35,255 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:35,387 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:35,390 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:35,719 - [Thread-2] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 206, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:25:35,722 - [Thread-2] - DEBUG - Response received {'d': {'requestId': 206, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:35,909 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:35,914 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:36,425 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:36,427 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:36,944 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676729.7004783, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:25:36,946 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:37,208 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'ebd05947-5c7a-4fbb-9ceb-175651537e62', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:25:37,209 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=ebd05947-5c7a-4fbb-9ceb-175651537e62
2025-07-28 12:25:37,211 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:25:37,212 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=1
2025-07-28 12:25:37,214 - [MoveServiceEventReceiver] - INFO - 移动服务请求 ebd05947-5c7a-4fbb-9ceb-175651537e62 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:25:37,214 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 云散了(VirtualPlayerID0597)
2025-07-28 12:25:37,215 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:25:37,216 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 云散了(VirtualPlayerID0597), 结果: 物品_1, 订单: None
2025-07-28 12:25:37,218 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:25:37,218 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:25:37,300 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A49D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 5
2025-07-28 12:25:37,304 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A49D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:37,305 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:25:37,455 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:25:37,456 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:25:37,458 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 清风文具店(VirtualPlayerID0598) 确保抓中, z_offset_extra=0
2025-07-28 12:25:37,459 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=40937441-3161-4443-a1e5-4c2f316267a1, Player=清风文具店, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:25:37,460 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:25:37,460 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 抓取指令已发送到移动服务，命令ID: 40937441-3161-4443-a1e5-4c2f316267a1
2025-07-28 12:25:37,460 - [MoveServiceEventReceiver] - INFO - 抓取指令 40937441-3161-4443-a1e5-4c2f316267a1 已被移动服务接受并加入队列
2025-07-28 12:25:37,727 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.035秒 - 玩家: VirtualPlayerID0597, 结果: 物品_1, 订单: NULL
2025-07-28 12:25:37,727 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0597] = 1
2025-07-28 12:25:37,749 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0598 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:37,755 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0598 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:37,977 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1'}, pending_retry_player: None
2025-07-28 12:25:37,981 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:38,037 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已完成游戏，总游戏次数: 1
2025-07-28 12:25:38,040 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 下次将以付费状态排队。
2025-07-28 12:25:38,496 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1'}, pending_retry_player: None
2025-07-28 12:25:38,496 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:39,000 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1'}, pending_retry_player: None
2025-07-28 12:25:39,003 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:39,312 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:25:39,320 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:25:39,324 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:39,339 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:39,517 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1'}, pending_retry_player: None
2025-07-28 12:25:39,519 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:40,023 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1'}, pending_retry_player: None
2025-07-28 12:25:40,028 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:40,051 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(6)，添加虚拟玩家。
2025-07-28 12:25:40,054 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['清风文具店(1次)']。已选择: 清风文具店
2025-07-28 12:25:40,054 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:40,289 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:25:40,331 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-28 12:25:40,414 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:25:40,416 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=40937441-3161-4443-a1e5-4c2f316267a1
2025-07-28 12:25:40,417 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:25:40,418 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=5
2025-07-28 12:25:40,419 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:25:40,419 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'player_id': 'VirtualPlayerID0598', 'player_name': '清风文具店', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:25:40,420 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 清风文具店(VirtualPlayerID0598), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: 40937441-3161-4443-a1e5-4c2f316267a1, 消息: 物品_5
2025-07-28 12:25:40,423 - [StatusUpdateThread] - INFO - 玩家 清风文具店 本次是第 1 次成功抓取。
2025-07-28 12:25:40,424 - [StatusUpdateThread] - INFO - 为玩家 清风文具店 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:25:40,426 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:25:40,427 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 629, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:25:40,430 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 629, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:40,541 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:40,546 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:41,059 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:41,065 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:41,358 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4100>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 6
2025-07-28 12:25:41,359 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4100>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:25:41,360 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:25:41,360 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:25:41,360 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:25:41,361 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.3s, 心跳间隔=1s
2025-07-28 12:25:41,361 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:25:41,362 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:25:41,364 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:25:41,578 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:41,580 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:42,095 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:42,098 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:42,372 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:25:42,376 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:25:42,377 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:25:42,377 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:25:42,377 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:25:42,378 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:25:42,378 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 3
2025-07-28 12:25:42,379 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:25:42,379 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:42,383 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:42,611 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:42,618 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:43,129 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:43,132 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:43,444 - [Thread-3] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 70, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:25:43,449 - [Thread-3] - DEBUG - Response received {'d': {'requestId': 70, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:43,637 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:43,639 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:44,143 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:44,145 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:44,428 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 7
2025-07-28 12:25:44,430 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:44,431 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:25:44,648 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676737.4563112, 'target_id': '5', 'target_object': '卡通公仔', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:25:44,655 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:44,951 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '40937441-3161-4443-a1e5-4c2f316267a1', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:25:44,952 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=40937441-3161-4443-a1e5-4c2f316267a1
2025-07-28 12:25:44,953 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:25:44,954 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=5
2025-07-28 12:25:44,954 - [MoveServiceEventReceiver] - INFO - 移动服务请求 40937441-3161-4443-a1e5-4c2f316267a1 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:25:44,954 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 清风文具店(VirtualPlayerID0598)
2025-07-28 12:25:44,954 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:25:44,955 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 清风文具店(VirtualPlayerID0598), 结果: 物品_5, 订单: None
2025-07-28 12:25:44,956 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:25:44,956 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:25:45,172 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:25:45,178 - [GameProcessThread] - INFO - [游戏线程] 玩家 笑出腹肌的胖子(VirtualPlayerID0599) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:25:45,182 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 笑出腹肌的胖子(VirtualPlayerID0599) 确保抓中, z_offset_extra=0
2025-07-28 12:25:45,188 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=c02b6e31-f456-4116-8808-1022025a9104, Player=笑出腹肌的胖子, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:25:45,190 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:25:45,194 - [GameProcessThread] - INFO - [游戏线程] 玩家 笑出腹肌的胖子(VirtualPlayerID0599) 抓取指令已发送到移动服务，命令ID: c02b6e31-f456-4116-8808-1022025a9104
2025-07-28 12:25:45,198 - [MoveServiceEventReceiver] - INFO - 抓取指令 c02b6e31-f456-4116-8808-1022025a9104 已被移动服务接受并加入队列
2025-07-28 12:25:45,418 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.027秒 - 玩家: VirtualPlayerID0598, 结果: 物品_5, 订单: NULL
2025-07-28 12:25:45,419 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0598] = 1
2025-07-28 12:25:45,440 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0599 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:45,444 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0599 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:45,709 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104'}, pending_retry_player: None
2025-07-28 12:25:45,711 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:46,086 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 已完成游戏，总游戏次数: 1
2025-07-28 12:25:46,088 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 下次将以付费状态排队。
2025-07-28 12:25:46,214 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104'}, pending_retry_player: None
2025-07-28 12:25:46,220 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:46,435 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:25:46,441 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:25:46,441 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:46,446 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:46,734 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104'}, pending_retry_player: None
2025-07-28 12:25:46,735 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:47,256 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104'}, pending_retry_player: None
2025-07-28 12:25:47,261 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:47,772 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104'}, pending_retry_player: None
2025-07-28 12:25:47,775 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:48,148 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:25:48,153 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=c02b6e31-f456-4116-8808-1022025a9104
2025-07-28 12:25:48,156 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:25:48,158 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0599, player_name=笑出腹肌的胖子, requested_object_id=2
2025-07-28 12:25:48,162 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:25:48,166 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'player_id': 'VirtualPlayerID0599', 'player_name': '笑出腹肌的胖子', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:25:48,166 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 笑出腹肌的胖子(VirtualPlayerID0599), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: c02b6e31-f456-4116-8808-1022025a9104, 消息: 物品_2
2025-07-28 12:25:48,169 - [StatusUpdateThread] - INFO - 玩家 笑出腹肌的胖子 本次是第 1 次成功抓取。
2025-07-28 12:25:48,170 - [StatusUpdateThread] - INFO - 为玩家 笑出腹肌的胖子 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:25:48,173 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:25:48,174 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 374, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:25:48,177 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 374, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:48,284 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:48,291 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:48,472 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4CA0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 8
2025-07-28 12:25:48,477 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4CA0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:48,478 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:25:48,805 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:48,812 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:49,326 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:49,333 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:49,846 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:49,852 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:49,908 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:25:49,912 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:25:49,913 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 33.6 秒
2025-07-28 12:25:49,914 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:25:50,114 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(7)，添加虚拟玩家。
2025-07-28 12:25:50,116 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['笑出腹肌的胖子(1次)']。已选择: 笑出腹肌的胖子
2025-07-28 12:25:50,116 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:50,367 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:50,373 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:50,491 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:25:50,495 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:25:50,495 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:50,500 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:50,507 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:25:50,544 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-28 12:25:50,887 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:50,889 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:51,186 - [Thread-4] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 120, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:25:51,189 - [Thread-4] - DEBUG - Response received {'d': {'requestId': 120, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:51,391 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:51,394 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:51,897 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:51,898 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:52,133 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:25:52,135 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学反应方程式 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:52,402 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.177306, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:25:52,407 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:52,528 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9898E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 9
2025-07-28 12:25:52,532 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9898E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:25:52,533 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:25:52,534 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:25:52,535 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:25:52,536 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:25:52,536 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:25:52,537 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:25:52,541 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:25:52,683 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'c02b6e31-f456-4116-8808-1022025a9104', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:25:52,685 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=c02b6e31-f456-4116-8808-1022025a9104
2025-07-28 12:25:52,686 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:25:52,687 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0599, player_name=笑出腹肌的胖子, requested_object_id=2
2025-07-28 12:25:52,688 - [MoveServiceEventReceiver] - INFO - 移动服务请求 c02b6e31-f456-4116-8808-1022025a9104 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:25:52,688 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 笑出腹肌的胖子(VirtualPlayerID0599)
2025-07-28 12:25:52,689 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:25:52,689 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 笑出腹肌的胖子(VirtualPlayerID0599), 结果: 物品_2, 订单: None
2025-07-28 12:25:52,690 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:25:52,690 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:25:52,919 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:25:52,924 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学物质大发现(VirtualPlayerID0600) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:25:52,926 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 化学物质大发现(VirtualPlayerID0600) 确保抓中, z_offset_extra=0
2025-07-28 12:25:52,933 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=bef87209-174e-4333-97dd-4d6277f79793, Player=化学物质大发现, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:25:52,935 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:25:52,937 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学物质大发现(VirtualPlayerID0600) 抓取指令已发送到移动服务，命令ID: bef87209-174e-4333-97dd-4d6277f79793
2025-07-28 12:25:52,938 - [MoveServiceEventReceiver] - INFO - 抓取指令 bef87209-174e-4333-97dd-4d6277f79793 已被移动服务接受并加入队列
2025-07-28 12:25:53,097 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.022秒 - 玩家: VirtualPlayerID0599, 结果: 物品_2, 订单: NULL
2025-07-28 12:25:53,099 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0599] = 1
2025-07-28 12:25:53,119 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0600 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:53,122 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0600 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:25:53,452 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793'}, pending_retry_player: None
2025-07-28 12:25:53,452 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:53,562 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:25:53,563 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:25:53,563 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:25:53,564 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:25:53,564 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:25:53,564 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:25:53,565 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 4
2025-07-28 12:25:53,565 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:25:53,565 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:53,570 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:53,957 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793'}, pending_retry_player: None
2025-07-28 12:25:53,959 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:54,162 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 已完成游戏，总游戏次数: 1
2025-07-28 12:25:54,164 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 下次将以付费状态排队。
2025-07-28 12:25:54,474 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793'}, pending_retry_player: None
2025-07-28 12:25:54,476 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:54,991 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793'}, pending_retry_player: None
2025-07-28 12:25:54,994 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:55,506 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793'}, pending_retry_player: None
2025-07-28 12:25:55,506 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:55,633 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863700>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 10
2025-07-28 12:25:55,637 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863700>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:55,638 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:25:55,942 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:25:55,944 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=bef87209-174e-4333-97dd-4d6277f79793
2025-07-28 12:25:55,945 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:25:55,946 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0600, player_name=化学物质大发现, requested_object_id=4
2025-07-28 12:25:55,947 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:25:55,948 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'player_id': 'VirtualPlayerID0600', 'player_name': '化学物质大发现', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:25:55,949 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 化学物质大发现(VirtualPlayerID0600), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: bef87209-174e-4333-97dd-4d6277f79793, 消息: 物品_4
2025-07-28 12:25:55,951 - [StatusUpdateThread] - INFO - 玩家 化学物质大发现 本次是第 1 次成功抓取。
2025-07-28 12:25:55,953 - [StatusUpdateThread] - INFO - 为玩家 化学物质大发现 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:25:55,956 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:25:55,960 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 409, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:25:55,961 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 409, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:56,020 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:56,027 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:56,176 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:25:56,178 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['化学物质大发现(1次)']。已选择: 化学物质大发现
2025-07-28 12:25:56,180 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:25:56,537 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:56,545 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:57,052 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:57,053 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:57,567 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:57,575 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:57,645 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:25:57,647 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:25:57,648 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:25:57,660 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:25:58,099 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:58,100 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:58,618 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:58,622 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:58,964 - [Thread-5] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 773, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:25:58,969 - [Thread-5] - DEBUG - Response received {'d': {'requestId': 773, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:25:59,137 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:59,141 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:59,656 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:25:59,658 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:25:59,689 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A45B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 11
2025-07-28 12:25:59,692 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A45B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:25:59,693 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:00,171 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676752.924187, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:26:00,173 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:00,389 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'bef87209-174e-4333-97dd-4d6277f79793', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:00,392 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=bef87209-174e-4333-97dd-4d6277f79793
2025-07-28 12:26:00,393 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:00,394 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0600, player_name=化学物质大发现, requested_object_id=4
2025-07-28 12:26:00,394 - [MoveServiceEventReceiver] - INFO - 移动服务请求 bef87209-174e-4333-97dd-4d6277f79793 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:00,395 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 化学物质大发现(VirtualPlayerID0600)
2025-07-28 12:26:00,395 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:26:00,395 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 化学物质大发现(VirtualPlayerID0600), 结果: 物品_4, 订单: None
2025-07-28 12:26:00,395 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:00,396 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:00,686 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:26:00,688 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:26:00,690 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小龙女的现代生活(VirtualPlayerID0596) 确保抓中, z_offset_extra=0
2025-07-28 12:26:00,695 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=c18b876d-d157-420c-acf3-8d8557855865, Player=小龙女的现代生活, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:26:00,698 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:26:00,700 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 抓取指令已发送到移动服务，命令ID: c18b876d-d157-420c-acf3-8d8557855865
2025-07-28 12:26:00,707 - [MoveServiceEventReceiver] - INFO - 抓取指令 c18b876d-d157-420c-acf3-8d8557855865 已被移动服务接受并加入队列
2025-07-28 12:26:00,793 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.029秒 - 玩家: VirtualPlayerID0600, 结果: 物品_4, 订单: NULL
2025-07-28 12:26:00,795 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0600] = 1
2025-07-28 12:26:00,848 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:26:00,860 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 6 个玩家, 成功更新 6 条记录.
2025-07-28 12:26:01,220 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865'}, pending_retry_player: None
2025-07-28 12:26:01,220 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:01,709 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:01,712 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:26:01,713 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:01,718 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:01,727 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865'}, pending_retry_player: None
2025-07-28 12:26:01,729 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:02,214 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已完成游戏，总游戏次数: 2
2025-07-28 12:26:02,214 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:26:02,215 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小龙女的现代生活(2次)']。已选择: 小龙女的现代生活
2025-07-28 12:26:02,215 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:26:02,245 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865'}, pending_retry_player: None
2025-07-28 12:26:02,246 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:02,748 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865'}, pending_retry_player: None
2025-07-28 12:26:02,750 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:03,265 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865'}, pending_retry_player: None
2025-07-28 12:26:03,270 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:03,660 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:26:03,662 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=c18b876d-d157-420c-acf3-8d8557855865
2025-07-28 12:26:03,662 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:26:03,663 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=1
2025-07-28 12:26:03,663 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:26:03,663 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'player_id': 'VirtualPlayerID0596', 'player_name': '小龙女的现代生活', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:26:03,664 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小龙女的现代生活(VirtualPlayerID0596), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: c18b876d-d157-420c-acf3-8d8557855865, 消息: 物品_1
2025-07-28 12:26:03,664 - [StatusUpdateThread] - INFO - 玩家 小龙女的现代生活 本次是第 2 次成功抓取。
2025-07-28 12:26:03,665 - [StatusUpdateThread] - INFO - 为玩家 小龙女的现代生活 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:03,666 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:26:03,666 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 791, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:26:03,667 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 791, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:03,741 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859F10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 12
2025-07-28 12:26:03,750 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859F10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:03,753 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:26:03,757 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:26:03,758 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:26:03,759 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:26:03,760 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:26:03,761 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:26:03,766 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:26:03,787 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:03,794 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:04,310 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:04,318 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:04,780 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:26:04,782 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:26:04,783 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:26:04,785 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:26:04,785 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:26:04,785 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:26:04,786 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 5
2025-07-28 12:26:04,786 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:26:04,786 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:04,791 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:04,828 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:04,829 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:05,346 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:05,354 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:05,867 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:05,870 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:06,372 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:06,374 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:06,675 - [Thread-6] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 91, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:26:06,689 - [Thread-6] - DEBUG - Response received {'d': {'requestId': 91, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:06,816 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC820>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 13
2025-07-28 12:26:06,820 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC820>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:06,821 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:06,882 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:06,884 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:07,387 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:07,392 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:07,910 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676760.688984, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:07,914 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:08,210 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'c18b876d-d157-420c-acf3-8d8557855865', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:08,212 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=c18b876d-d157-420c-acf3-8d8557855865
2025-07-28 12:26:08,213 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:08,214 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=1
2025-07-28 12:26:08,215 - [MoveServiceEventReceiver] - INFO - 移动服务请求 c18b876d-d157-420c-acf3-8d8557855865 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:08,215 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小龙女的现代生活(VirtualPlayerID0596)
2025-07-28 12:26:08,218 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:26:08,219 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小龙女的现代生活(VirtualPlayerID0596), 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:26:08,221 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:08,223 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:08,429 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:26:08,436 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:26:08,441 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 云散了(VirtualPlayerID0597) 确保抓中, z_offset_extra=0
2025-07-28 12:26:08,450 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=dac01103-e6ca-40b1-a55c-bf8186600853, Player=云散了, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:26:08,451 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:26:08,454 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 抓取指令已发送到移动服务，命令ID: dac01103-e6ca-40b1-a55c-bf8186600853
2025-07-28 12:26:08,456 - [MoveServiceEventReceiver] - INFO - 抓取指令 dac01103-e6ca-40b1-a55c-bf8186600853 已被移动服务接受并加入队列
2025-07-28 12:26:08,476 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.047秒 - 玩家: VirtualPlayerID0596, 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:26:08,480 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0596] = 2
2025-07-28 12:26:08,825 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:08,829 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:26:08,829 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:08,839 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:08,970 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853'}, pending_retry_player: None
2025-07-28 12:26:08,977 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:09,492 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853'}, pending_retry_player: None
2025-07-28 12:26:09,494 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:10,012 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853'}, pending_retry_player: None
2025-07-28 12:26:10,012 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:10,264 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已完成游戏，总游戏次数: 2
2025-07-28 12:26:10,518 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853'}, pending_retry_player: None
2025-07-28 12:26:10,525 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:10,867 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4100>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 14
2025-07-28 12:26:10,869 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4100>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:10,870 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:11,041 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853'}, pending_retry_player: None
2025-07-28 12:26:11,046 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:11,082 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.041秒
2025-07-28 12:26:11,082 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:26:11,102 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 6 个玩家, 成功更新 6 条记录.
2025-07-28 12:26:11,418 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:26:11,424 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=dac01103-e6ca-40b1-a55c-bf8186600853
2025-07-28 12:26:11,425 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:26:11,426 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=3
2025-07-28 12:26:11,426 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:26:11,427 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'player_id': 'VirtualPlayerID0597', 'player_name': '云散了', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:26:11,427 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 云散了(VirtualPlayerID0597), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: dac01103-e6ca-40b1-a55c-bf8186600853, 消息: 物品_3
2025-07-28 12:26:11,428 - [StatusUpdateThread] - INFO - 玩家 云散了 本次是第 2 次成功抓取。
2025-07-28 12:26:11,428 - [StatusUpdateThread] - INFO - 为玩家 云散了 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:11,429 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:26:11,429 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 298, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:26:11,430 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 298, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:11,562 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:11,567 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:12,082 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:12,090 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:12,269 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:26:12,277 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['云散了(2次)']。已选择: 云散了
2025-07-28 12:26:12,281 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:26:12,604 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:12,611 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:12,871 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:12,878 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:26:12,878 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:12,881 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:13,125 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:13,130 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:13,641 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:13,644 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:14,158 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:14,161 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:14,439 - [Thread-7] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 524, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:26:14,452 - [Thread-7] - DEBUG - Response received {'d': {'requestId': 524, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:14,675 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:14,682 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:14,927 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 15
2025-07-28 12:26:14,929 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:14,930 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:26:14,930 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:26:14,931 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:26:14,931 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:26:14,932 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:26:14,932 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:26:14,933 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:26:15,192 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:15,198 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:15,712 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676768.4368582, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:15,716 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:15,948 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:26:15,951 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'dac01103-e6ca-40b1-a55c-bf8186600853', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:15,956 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:26:15,962 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=dac01103-e6ca-40b1-a55c-bf8186600853
2025-07-28 12:26:15,964 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:26:15,967 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:15,968 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=3
2025-07-28 12:26:15,967 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:26:15,968 - [MoveServiceEventReceiver] - INFO - 移动服务请求 dac01103-e6ca-40b1-a55c-bf8186600853 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:15,968 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 云散了(VirtualPlayerID0597)
2025-07-28 12:26:15,969 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:26:15,969 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:26:15,970 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:26:15,970 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 云散了(VirtualPlayerID0597), 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:26:15,971 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 6
2025-07-28 12:26:15,971 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:26:15,972 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:15,972 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:15,972 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:15,977 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:16,169 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.033秒 - 玩家: VirtualPlayerID0597, 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:26:16,174 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0597] = 2
2025-07-28 12:26:16,233 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:26:16,234 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:26:16,237 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 清风文具店(VirtualPlayerID0598) 确保抓中, z_offset_extra=0
2025-07-28 12:26:16,246 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=679d889c-fb73-423a-94c4-00c7698e1abd, Player=清风文具店, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:26:16,250 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 抓取指令已发送到移动服务，命令ID: 679d889c-fb73-423a-94c4-00c7698e1abd
2025-07-28 12:26:16,250 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:26:16,252 - [MoveServiceEventReceiver] - INFO - 抓取指令 679d889c-fb73-423a-94c4-00c7698e1abd 已被移动服务接受并加入队列
2025-07-28 12:26:16,311 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已完成游戏，总游戏次数: 2
2025-07-28 12:26:16,313 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:26:16,313 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['清风文具店(2次)']。已选择: 清风文具店
2025-07-28 12:26:16,315 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:26:16,753 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd'}, pending_retry_player: None
2025-07-28 12:26:16,755 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:17,259 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd'}, pending_retry_player: None
2025-07-28 12:26:17,260 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:17,764 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd'}, pending_retry_player: None
2025-07-28 12:26:17,766 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:18,003 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9898E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 16
2025-07-28 12:26:18,010 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9898E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:18,011 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:18,270 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd'}, pending_retry_player: None
2025-07-28 12:26:18,275 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:18,791 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd'}, pending_retry_player: None
2025-07-28 12:26:18,796 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:19,283 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:26:19,284 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=679d889c-fb73-423a-94c4-00c7698e1abd
2025-07-28 12:26:19,285 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:26:19,285 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=3
2025-07-28 12:26:19,286 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:26:19,286 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'player_id': 'VirtualPlayerID0598', 'player_name': '清风文具店', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:26:19,286 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 清风文具店(VirtualPlayerID0598), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 679d889c-fb73-423a-94c4-00c7698e1abd, 消息: 物品_3
2025-07-28 12:26:19,287 - [StatusUpdateThread] - INFO - 玩家 清风文具店 本次是第 2 次成功抓取。
2025-07-28 12:26:19,287 - [StatusUpdateThread] - INFO - 为玩家 清风文具店 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:19,288 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:26:19,289 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 51, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:26:19,290 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 51, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:19,314 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:19,318 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:19,836 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:19,842 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:19,929 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:26:19,938 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:26:19,943 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 63.6 秒
2025-07-28 12:26:19,944 - [HealthCheckThread] - WARNING - 已经 63.6 秒没有成功请求，尝试恢复连接...
2025-07-28 12:26:19,947 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:26:19,949 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:20,022 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:20,029 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:26:20,033 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:20,043 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:20,338 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:26:20,342 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花千树映雪 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:26:20,355 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:20,358 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:20,905 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:20,906 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:21,271 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:26:21,319 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:26:21,424 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:21,424 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:21,928 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:21,930 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:21,991 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4E80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:21,994 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:26:22,085 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC2E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 17
2025-07-28 12:26:22,091 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC2E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:22,092 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:22,301 - [Thread-8] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 461, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:26:22,308 - [Thread-8] - DEBUG - Response received {'d': {'requestId': 461, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:22,444 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:22,446 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:22,958 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:22,959 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:23,462 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.2345176, 'target_id': '3', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:23,464 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:23,810 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '679d889c-fb73-423a-94c4-00c7698e1abd', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:23,811 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=679d889c-fb73-423a-94c4-00c7698e1abd
2025-07-28 12:26:23,812 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:23,813 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=3
2025-07-28 12:26:23,813 - [MoveServiceEventReceiver] - INFO - 移动服务请求 679d889c-fb73-423a-94c4-00c7698e1abd (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:23,813 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 清风文具店(VirtualPlayerID0598)
2025-07-28 12:26:23,814 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:26:23,814 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 清风文具店(VirtualPlayerID0598), 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:26:23,815 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:23,816 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:23,911 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.039秒 - 玩家: VirtualPlayerID0598, 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:26:23,913 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0598] = 2
2025-07-28 12:26:23,968 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:26:23,973 - [GameProcessThread] - INFO - [游戏线程] 玩家 笑出腹肌的胖子(VirtualPlayerID0599) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:26:23,974 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 笑出腹肌的胖子(VirtualPlayerID0599) 确保抓中, z_offset_extra=0
2025-07-28 12:26:23,975 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=5d123689-6514-4f44-a5fe-ef35eaebe82d, Player=笑出腹肌的胖子, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:26:23,976 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:26:23,976 - [GameProcessThread] - INFO - [游戏线程] 玩家 笑出腹肌的胖子(VirtualPlayerID0599) 抓取指令已发送到移动服务，命令ID: 5d123689-6514-4f44-a5fe-ef35eaebe82d
2025-07-28 12:26:23,977 - [MoveServiceEventReceiver] - INFO - 抓取指令 5d123689-6514-4f44-a5fe-ef35eaebe82d 已被移动服务接受并加入队列
2025-07-28 12:26:24,107 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:24,110 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:26:24,111 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:24,122 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:24,373 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 已完成游戏，总游戏次数: 2
2025-07-28 12:26:24,485 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d'}, pending_retry_player: None
2025-07-28 12:26:24,485 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:24,987 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d'}, pending_retry_player: None
2025-07-28 12:26:24,987 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:25,502 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d'}, pending_retry_player: None
2025-07-28 12:26:25,504 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:26,022 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d'}, pending_retry_player: None
2025-07-28 12:26:26,024 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:26,165 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A48E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 18
2025-07-28 12:26:26,169 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A48E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:26,170 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:26:26,171 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:26:26,172 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:26:26,173 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:26:26,174 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:26:26,174 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:26:26,181 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:26:26,542 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d'}, pending_retry_player: None
2025-07-28 12:26:26,545 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:26,921 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:26:26,923 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=5d123689-6514-4f44-a5fe-ef35eaebe82d
2025-07-28 12:26:26,924 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:26:26,925 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0599, player_name=笑出腹肌的胖子, requested_object_id=1
2025-07-28 12:26:26,925 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:26:26,926 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'player_id': 'VirtualPlayerID0599', 'player_name': '笑出腹肌的胖子', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:26:26,927 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 笑出腹肌的胖子(VirtualPlayerID0599), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 5d123689-6514-4f44-a5fe-ef35eaebe82d, 消息: 物品_1
2025-07-28 12:26:26,930 - [StatusUpdateThread] - INFO - 玩家 笑出腹肌的胖子 本次是第 2 次成功抓取。
2025-07-28 12:26:26,931 - [StatusUpdateThread] - INFO - 为玩家 笑出腹肌的胖子 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:26,934 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:26:26,936 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 174, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:26:26,944 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 174, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:27,062 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:27,063 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:27,190 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:26:27,193 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:26:27,195 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:26:27,195 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:26:27,196 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:26:27,196 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:26:27,196 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 7
2025-07-28 12:26:27,197 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:26:27,197 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:27,201 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:27,571 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:27,577 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:28,090 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:28,096 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:28,611 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:28,617 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:29,131 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:29,139 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:29,242 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A41C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 19
2025-07-28 12:26:29,246 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A41C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:29,247 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:29,652 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:29,660 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:29,951 - [Thread-9] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 938, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:26:29,953 - [Thread-9] - DEBUG - Response received {'d': {'requestId': 938, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:30,170 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:30,177 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:30,690 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:30,696 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:31,207 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': 1753676783.973426, 'target_id': '1', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:26:31,211 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:31,254 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:31,260 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:26:31,264 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:31,280 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:31,457 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5d123689-6514-4f44-a5fe-ef35eaebe82d', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:31,459 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=5d123689-6514-4f44-a5fe-ef35eaebe82d
2025-07-28 12:26:31,460 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:31,460 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0599, player_name=笑出腹肌的胖子, requested_object_id=1
2025-07-28 12:26:31,461 - [MoveServiceEventReceiver] - INFO - 移动服务请求 5d123689-6514-4f44-a5fe-ef35eaebe82d (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:31,462 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 笑出腹肌的胖子(VirtualPlayerID0599)
2025-07-28 12:26:31,464 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:26:31,465 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 笑出腹肌的胖子(VirtualPlayerID0599), 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:26:31,467 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:31,468 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:31,558 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.039秒 - 玩家: VirtualPlayerID0599, 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:26:31,560 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0599] = 2
2025-07-28 12:26:31,571 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:26:31,593 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:26:31,723 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:26:31,724 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学反应方程式(VirtualPlayerID0601) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:26:31,726 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 化学反应方程式(VirtualPlayerID0601) 确保抓中, z_offset_extra=0
2025-07-28 12:26:31,733 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=cc8cc229-e494-4b36-a3af-c027cfc2369c, Player=化学反应方程式, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:26:31,735 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:26:31,736 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学反应方程式(VirtualPlayerID0601) 抓取指令已发送到移动服务，命令ID: cc8cc229-e494-4b36-a3af-c027cfc2369c
2025-07-28 12:26:31,740 - [MoveServiceEventReceiver] - INFO - 抓取指令 cc8cc229-e494-4b36-a3af-c027cfc2369c 已被移动服务接受并加入队列
2025-07-28 12:26:32,098 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0601 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:26:32,104 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0601 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:26:32,254 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c'}, pending_retry_player: None
2025-07-28 12:26:32,255 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:32,407 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学反应方程式 已完成游戏，总游戏次数: 1
2025-07-28 12:26:32,410 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学反应方程式 下次将以付费状态排队。
2025-07-28 12:26:32,769 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c'}, pending_retry_player: None
2025-07-28 12:26:32,771 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:33,275 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c'}, pending_retry_player: None
2025-07-28 12:26:33,277 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:33,324 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859E20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 20
2025-07-28 12:26:33,332 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859E20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:33,336 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:33,782 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c'}, pending_retry_player: None
2025-07-28 12:26:33,783 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:34,287 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c'}, pending_retry_player: None
2025-07-28 12:26:34,288 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:34,413 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:26:34,414 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['化学反应方程式(1次)', '笑出腹肌的胖子(2次)']。已选择: 化学反应方程式
2025-07-28 12:26:34,415 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学反应方程式 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:26:34,759 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:26:34,761 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=cc8cc229-e494-4b36-a3af-c027cfc2369c
2025-07-28 12:26:34,761 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:26:34,762 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0601, player_name=化学反应方程式, requested_object_id=5
2025-07-28 12:26:34,763 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:26:34,763 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'player_id': 'VirtualPlayerID0601', 'player_name': '化学反应方程式', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:26:34,764 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 化学反应方程式(VirtualPlayerID0601), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: cc8cc229-e494-4b36-a3af-c027cfc2369c, 消息: 物品_5
2025-07-28 12:26:34,767 - [StatusUpdateThread] - INFO - 玩家 化学反应方程式 本次是第 1 次成功抓取。
2025-07-28 12:26:34,768 - [StatusUpdateThread] - INFO - 为玩家 化学反应方程式 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:34,778 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:26:34,779 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 14, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:26:34,782 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 14, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:34,791 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:34,793 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:35,296 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:35,296 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:35,344 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:35,346 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:26:35,347 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:35,358 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:35,801 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:35,802 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:36,324 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:36,326 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:36,838 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:36,843 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:37,360 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:37,361 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:37,414 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC520>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 21
2025-07-28 12:26:37,416 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC520>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:37,416 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:26:37,417 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:26:37,417 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:26:37,418 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:26:37,418 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:26:37,418 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:26:37,420 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:26:37,799 - [Thread-10] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 612, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:26:37,805 - [Thread-10] - DEBUG - Response received {'d': {'requestId': 612, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:37,878 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:37,880 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:38,381 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:38,383 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:38,428 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:26:38,433 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:26:38,438 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:26:38,444 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:26:38,446 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:26:38,446 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:26:38,447 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 8
2025-07-28 12:26:38,447 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:26:38,447 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:38,452 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:38,886 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': 1753676791.7242885, 'target_id': '5', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:38,888 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:39,238 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cc8cc229-e494-4b36-a3af-c027cfc2369c', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:39,240 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=cc8cc229-e494-4b36-a3af-c027cfc2369c
2025-07-28 12:26:39,241 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:39,241 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0601, player_name=化学反应方程式, requested_object_id=5
2025-07-28 12:26:39,242 - [MoveServiceEventReceiver] - INFO - 移动服务请求 cc8cc229-e494-4b36-a3af-c027cfc2369c (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:39,242 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 化学反应方程式(VirtualPlayerID0601)
2025-07-28 12:26:39,242 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:26:39,243 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 化学反应方程式(VirtualPlayerID0601), 结果: 物品_5, 订单: None
2025-07-28 12:26:39,243 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:39,244 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:39,394 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:26:39,396 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学物质大发现(VirtualPlayerID0600) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:26:39,396 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 化学物质大发现(VirtualPlayerID0600) 确保抓中, z_offset_extra=0
2025-07-28 12:26:39,398 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=f83275c3-de11-437e-9b14-40135ff2b3f6, Player=化学物质大发现, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:26:39,398 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:26:39,399 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学物质大发现(VirtualPlayerID0600) 抓取指令已发送到移动服务，命令ID: f83275c3-de11-437e-9b14-40135ff2b3f6
2025-07-28 12:26:39,400 - [MoveServiceEventReceiver] - INFO - 抓取指令 f83275c3-de11-437e-9b14-40135ff2b3f6 已被移动服务接受并加入队列
2025-07-28 12:26:39,712 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.018秒 - 玩家: VirtualPlayerID0601, 结果: 物品_5, 订单: NULL
2025-07-28 12:26:39,713 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0601] = 1
2025-07-28 12:26:39,916 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6'}, pending_retry_player: None
2025-07-28 12:26:39,918 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:40,422 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6'}, pending_retry_player: None
2025-07-28 12:26:40,423 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:40,454 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 已完成游戏，总游戏次数: 2
2025-07-28 12:26:40,489 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 22
2025-07-28 12:26:40,492 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:40,493 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:40,929 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6'}, pending_retry_player: None
2025-07-28 12:26:40,929 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:41,434 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6'}, pending_retry_player: None
2025-07-28 12:26:41,435 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:41,766 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:26:41,802 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:26:41,938 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6'}, pending_retry_player: None
2025-07-28 12:26:41,944 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:42,363 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:26:42,365 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=f83275c3-de11-437e-9b14-40135ff2b3f6
2025-07-28 12:26:42,367 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:26:42,368 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0600, player_name=化学物质大发现, requested_object_id=5
2025-07-28 12:26:42,368 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:26:42,368 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'player_id': 'VirtualPlayerID0600', 'player_name': '化学物质大发现', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:26:42,369 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 化学物质大发现(VirtualPlayerID0600), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: f83275c3-de11-437e-9b14-40135ff2b3f6, 消息: 物品_5
2025-07-28 12:26:42,369 - [StatusUpdateThread] - INFO - 玩家 化学物质大发现 本次是第 2 次成功抓取。
2025-07-28 12:26:42,370 - [StatusUpdateThread] - INFO - 为玩家 化学物质大发现 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:42,372 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:26:42,375 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 945, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:26:42,379 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 945, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:42,459 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:42,463 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:42,504 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:42,513 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:26:42,515 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:42,531 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:42,977 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:42,979 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:43,481 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:43,482 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:43,988 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:43,990 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:44,508 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:44,509 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:44,555 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C8598E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 23
2025-07-28 12:26:44,558 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C8598E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:44,560 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:45,028 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:45,028 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:45,402 - [Thread-11] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 980, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:26:45,411 - [Thread-11] - DEBUG - Response received {'d': {'requestId': 980, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:45,545 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:45,547 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:46,064 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:46,065 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:46,562 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:46,566 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:26:46,567 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:46,570 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:46,579 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676799.3964958, 'target_id': '5', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:46,580 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:46,831 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f83275c3-de11-437e-9b14-40135ff2b3f6', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:46,834 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=f83275c3-de11-437e-9b14-40135ff2b3f6
2025-07-28 12:26:46,835 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:46,836 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0600, player_name=化学物质大发现, requested_object_id=5
2025-07-28 12:26:46,837 - [MoveServiceEventReceiver] - INFO - 移动服务请求 f83275c3-de11-437e-9b14-40135ff2b3f6 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:46,838 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 化学物质大发现(VirtualPlayerID0600)
2025-07-28 12:26:46,846 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:26:46,849 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 化学物质大发现(VirtualPlayerID0600), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:26:46,850 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:46,851 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:47,082 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:26:47,088 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:26:47,092 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小龙女的现代生活(VirtualPlayerID0596) 确保抓中, z_offset_extra=0
2025-07-28 12:26:47,099 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:26:47,099 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=36ac16c5-e5f1-4d8e-92c9-f48e7f946147, Player=小龙女的现代生活, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:26:47,099 - [MoveServiceEventReceiver] - INFO - 抓取指令 36ac16c5-e5f1-4d8e-92c9-f48e7f946147 已被移动服务接受并加入队列
2025-07-28 12:26:47,101 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 抓取指令已发送到移动服务，命令ID: 36ac16c5-e5f1-4d8e-92c9-f48e7f946147
2025-07-28 12:26:47,380 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.030秒 - 玩家: VirtualPlayerID0600, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:26:47,381 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0600] = 2
2025-07-28 12:26:47,603 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147'}, pending_retry_player: None
2025-07-28 12:26:47,606 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:48,111 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147'}, pending_retry_player: None
2025-07-28 12:26:48,112 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:48,485 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已完成游戏，总游戏次数: 3
2025-07-28 12:26:48,485 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-28 12:26:48,486 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['笑出腹肌的胖子(2次)', '化学物质大发现(2次)', '小龙女的现代生活(3次)']。已选择: 笑出腹肌的胖子
2025-07-28 12:26:48,486 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:26:48,598 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 24
2025-07-28 12:26:48,606 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:48,611 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:26:48,611 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:26:48,614 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:26:48,614 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:26:48,615 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:26:48,615 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:26:48,617 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:26:48,628 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147'}, pending_retry_player: None
2025-07-28 12:26:48,630 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:49,147 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147'}, pending_retry_player: None
2025-07-28 12:26:49,148 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:49,619 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:26:49,623 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:26:49,624 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:26:49,625 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:26:49,626 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:26:49,627 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:26:49,628 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 9
2025-07-28 12:26:49,628 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:26:49,629 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:49,641 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:49,652 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147'}, pending_retry_player: None
2025-07-28 12:26:49,655 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:50,123 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:26:50,124 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=36ac16c5-e5f1-4d8e-92c9-f48e7f946147
2025-07-28 12:26:50,125 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:26:50,125 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=5
2025-07-28 12:26:50,125 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:26:50,126 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'player_id': 'VirtualPlayerID0596', 'player_name': '小龙女的现代生活', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:26:50,126 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小龙女的现代生活(VirtualPlayerID0596), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: 36ac16c5-e5f1-4d8e-92c9-f48e7f946147, 消息: 物品_5
2025-07-28 12:26:50,127 - [StatusUpdateThread] - INFO - 玩家 小龙女的现代生活 本次是第 3 次成功抓取。
2025-07-28 12:26:50,127 - [StatusUpdateThread] - INFO - 为玩家 小龙女的现代生活 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:50,128 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:26:50,128 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 716, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:26:50,130 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 716, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:50,171 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:50,172 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:50,685 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:50,689 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:51,202 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:51,204 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:51,691 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4580>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 25
2025-07-28 12:26:51,694 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4580>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:51,695 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:51,717 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:51,720 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:51,937 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:26:51,973 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:26:51,998 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:26:52,001 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:26:52,001 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 95.7 秒
2025-07-28 12:26:52,008 - [HealthCheckThread] - WARNING - 已经 95.7 秒没有成功请求，尝试恢复连接...
2025-07-28 12:26:52,011 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:26:52,015 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:52,235 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:52,245 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:52,754 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:52,756 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:53,132 - [Thread-12] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 813, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:26:53,134 - [Thread-12] - DEBUG - Response received {'d': {'requestId': 813, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:53,273 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:53,275 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:53,697 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:53,699 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:26:53,699 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:53,703 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:53,778 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:53,785 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:54,060 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC970>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:54,064 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:26:54,296 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': **********.0886161, 'target_id': '5', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:26:54,299 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:54,642 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '36ac16c5-e5f1-4d8e-92c9-f48e7f946147', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:26:54,644 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=36ac16c5-e5f1-4d8e-92c9-f48e7f946147
2025-07-28 12:26:54,645 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:26:54,646 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=5
2025-07-28 12:26:54,647 - [MoveServiceEventReceiver] - INFO - 移动服务请求 36ac16c5-e5f1-4d8e-92c9-f48e7f946147 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:26:54,648 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小龙女的现代生活(VirtualPlayerID0596)
2025-07-28 12:26:54,650 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:26:54,651 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小龙女的现代生活(VirtualPlayerID0596), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:26:54,654 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:26:54,655 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:26:54,812 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:26:54,816 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:26:54,818 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 云散了(VirtualPlayerID0597) 确保抓中, z_offset_extra=0
2025-07-28 12:26:54,825 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e, Player=云散了, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:26:54,825 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:26:54,827 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 抓取指令已发送到移动服务，命令ID: 61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e
2025-07-28 12:26:54,828 - [MoveServiceEventReceiver] - INFO - 抓取指令 61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e 已被移动服务接受并加入队列
2025-07-28 12:26:55,069 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.038秒 - 玩家: VirtualPlayerID0596, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:26:55,075 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0596] = 3
2025-07-28 12:26:55,345 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e'}, pending_retry_player: None
2025-07-28 12:26:55,346 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:55,742 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4850>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 26
2025-07-28 12:26:55,744 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4850>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:26:55,744 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:26:55,861 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e'}, pending_retry_player: None
2025-07-28 12:26:55,863 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:56,370 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e'}, pending_retry_player: None
2025-07-28 12:26:56,376 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:56,527 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已完成游戏，总游戏次数: 3
2025-07-28 12:26:56,528 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(6)，添加虚拟玩家。
2025-07-28 12:26:56,529 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['化学物质大发现(2次)', '小龙女的现代生活(3次)', '云散了(3次)']。已选择: 化学物质大发现
2025-07-28 12:26:56,530 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:26:56,890 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e'}, pending_retry_player: None
2025-07-28 12:26:56,899 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:57,412 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e'}, pending_retry_player: None
2025-07-28 12:26:57,418 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:57,757 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:26:57,759 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:26:57,760 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:26:57,764 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:26:57,854 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:26:57,855 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e
2025-07-28 12:26:57,856 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:26:57,856 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=3
2025-07-28 12:26:57,856 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:26:57,857 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'player_id': 'VirtualPlayerID0597', 'player_name': '云散了', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:26:57,857 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 云散了(VirtualPlayerID0597), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e, 消息: 物品_3
2025-07-28 12:26:57,857 - [StatusUpdateThread] - INFO - 玩家 云散了 本次是第 3 次成功抓取。
2025-07-28 12:26:57,858 - [StatusUpdateThread] - INFO - 为玩家 云散了 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:26:57,859 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:26:57,859 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 434, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:26:57,860 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 434, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:26:57,931 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:57,937 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:58,456 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:58,458 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:58,962 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:58,962 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:59,481 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:59,484 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:26:59,798 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4B50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 27
2025-07-28 12:26:59,802 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4B50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:26:59,803 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:26:59,804 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:26:59,805 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:26:59,805 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:26:59,806 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:26:59,807 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:26:59,813 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:26:59,987 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:26:59,989 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:00,493 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:00,500 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:00,824 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:27:00,827 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:27:00,828 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:27:00,829 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:00,830 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:27:00,831 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:27:00,831 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 10
2025-07-28 12:27:00,831 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:27:00,831 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:00,834 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:00,871 - [Thread-13] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 515, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:27:00,880 - [Thread-13] - DEBUG - Response received {'d': {'requestId': 515, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:01,016 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:01,019 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:01,522 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:01,524 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:02,039 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': 1753676814.8165798, 'target_id': '3', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:02,044 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:02,209 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.030秒
2025-07-28 12:27:02,209 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:27:02,239 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:27:02,403 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:02,410 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e
2025-07-28 12:27:02,412 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:02,413 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=3
2025-07-28 12:27:02,414 - [MoveServiceEventReceiver] - INFO - 移动服务请求 61f1db80-0d25-44b6-a9fb-ebfe8edc5f8e (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:02,414 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 云散了(VirtualPlayerID0597)
2025-07-28 12:27:02,417 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:27:02,417 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 云散了(VirtualPlayerID0597), 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:27:02,418 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:02,418 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:02,559 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:27:02,561 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:27:02,563 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 清风文具店(VirtualPlayerID0598) 确保抓中, z_offset_extra=0
2025-07-28 12:27:02,568 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=12ea7316-99e7-48ef-b9b7-26af0ca797d7, Player=清风文具店, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:27:02,571 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:27:02,574 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 抓取指令已发送到移动服务，命令ID: 12ea7316-99e7-48ef-b9b7-26af0ca797d7
2025-07-28 12:27:02,577 - [MoveServiceEventReceiver] - INFO - 抓取指令 12ea7316-99e7-48ef-b9b7-26af0ca797d7 已被移动服务接受并加入队列
2025-07-28 12:27:02,772 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.025秒 - 玩家: VirtualPlayerID0597, 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:27:02,776 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0597] = 3
2025-07-28 12:27:02,858 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863DF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 28
2025-07-28 12:27:02,865 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863DF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:02,869 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:03,079 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7'}, pending_retry_player: None
2025-07-28 12:27:03,079 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:03,593 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7'}, pending_retry_player: None
2025-07-28 12:27:03,593 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:04,099 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7'}, pending_retry_player: None
2025-07-28 12:27:04,102 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:04,555 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已完成游戏，总游戏次数: 3
2025-07-28 12:27:04,557 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-28 12:27:04,563 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小龙女的现代生活(3次)', '云散了(3次)', '清风文具店(3次)']。已选择: 小龙女的现代生活
2025-07-28 12:27:04,566 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已加入队列末尾。优先级: 2.10 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:04,620 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7'}, pending_retry_player: None
2025-07-28 12:27:04,622 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:04,886 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:04,888 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:27:04,890 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:04,894 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:05,125 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7'}, pending_retry_player: None
2025-07-28 12:27:05,130 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:05,518 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:27:05,520 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=12ea7316-99e7-48ef-b9b7-26af0ca797d7
2025-07-28 12:27:05,521 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:27:05,521 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=4
2025-07-28 12:27:05,522 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:27:05,522 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'player_id': 'VirtualPlayerID0598', 'player_name': '清风文具店', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:27:05,523 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 清风文具店(VirtualPlayerID0598), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: 12ea7316-99e7-48ef-b9b7-26af0ca797d7, 消息: 物品_4
2025-07-28 12:27:05,524 - [StatusUpdateThread] - INFO - 玩家 清风文具店 本次是第 3 次成功抓取。
2025-07-28 12:27:05,524 - [StatusUpdateThread] - INFO - 为玩家 清风文具店 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:05,525 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:27:05,525 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 208, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:27:05,526 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 208, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:05,645 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:05,651 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:06,162 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:06,165 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:06,589 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:27:06,596 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['云散了(3次)', '清风文具店(3次)']。已选择: 云散了
2025-07-28 12:27:06,600 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已加入队列末尾。优先级: 2.10 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:06,684 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:06,687 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:06,945 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 29
2025-07-28 12:27:06,950 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:06,952 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:07,191 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:07,191 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:07,695 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:07,697 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:08,203 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:08,209 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:08,532 - [Thread-14] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 88, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:27:08,545 - [Thread-14] - DEBUG - Response received {'d': {'requestId': 88, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:08,610 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:27:08,612 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['清风文具店(3次)']。已选择: 清风文具店
2025-07-28 12:27:08,614 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:08,722 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:08,725 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:08,957 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:08,959 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:27:08,965 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:08,977 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:09,238 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:09,242 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:09,754 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': 1753676822.5614657, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:27:09,759 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:10,052 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '12ea7316-99e7-48ef-b9b7-26af0ca797d7', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:10,053 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=12ea7316-99e7-48ef-b9b7-26af0ca797d7
2025-07-28 12:27:10,054 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:10,054 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=4
2025-07-28 12:27:10,054 - [MoveServiceEventReceiver] - INFO - 移动服务请求 12ea7316-99e7-48ef-b9b7-26af0ca797d7 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:10,055 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 清风文具店(VirtualPlayerID0598)
2025-07-28 12:27:10,055 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:27:10,056 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 清风文具店(VirtualPlayerID0598), 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:27:10,057 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:10,057 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:10,270 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:27:10,274 - [GameProcessThread] - INFO - [游戏线程] 玩家 花千树映雪(VirtualPlayerID0602) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:27:10,274 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 花千树映雪(VirtualPlayerID0602) 确保抓中, z_offset_extra=0
2025-07-28 12:27:10,277 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=b55c55c5-4d57-45b5-be5c-661464e45bcd, Player=花千树映雪, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:27:10,277 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:27:10,278 - [GameProcessThread] - INFO - [游戏线程] 玩家 花千树映雪(VirtualPlayerID0602) 抓取指令已发送到移动服务，命令ID: b55c55c5-4d57-45b5-be5c-661464e45bcd
2025-07-28 12:27:10,279 - [MoveServiceEventReceiver] - INFO - 抓取指令 b55c55c5-4d57-45b5-be5c-661464e45bcd 已被移动服务接受并加入队列
2025-07-28 12:27:10,384 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.034秒 - 玩家: VirtualPlayerID0598, 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:27:10,384 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0598] = 3
2025-07-28 12:27:10,386 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0602 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:27:10,390 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0602 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:27:10,633 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花千树映雪 已完成游戏，总游戏次数: 1
2025-07-28 12:27:10,633 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花千树映雪 下次将以付费状态排队。
2025-07-28 12:27:10,794 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd'}, pending_retry_player: None
2025-07-28 12:27:10,800 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:11,014 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BCFA0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 30
2025-07-28 12:27:11,016 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BCFA0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:27:11,016 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:27:11,017 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:27:11,017 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:27:11,017 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:27:11,018 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:27:11,018 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:27:11,020 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:27:11,314 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd'}, pending_retry_player: None
2025-07-28 12:27:11,319 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:11,832 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd'}, pending_retry_player: None
2025-07-28 12:27:11,841 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:12,034 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:27:12,038 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:27:12,042 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:27:12,043 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:12,044 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:27:12,044 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:27:12,045 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 11
2025-07-28 12:27:12,046 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:27:12,047 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:12,055 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:12,351 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd'}, pending_retry_player: None
2025-07-28 12:27:12,353 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:12,430 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:27:12,499 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:27:12,873 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd'}, pending_retry_player: None
2025-07-28 12:27:12,880 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:13,235 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:27:13,238 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=b55c55c5-4d57-45b5-be5c-661464e45bcd
2025-07-28 12:27:13,240 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:27:13,245 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0602, player_name=花千树映雪, requested_object_id=3
2025-07-28 12:27:13,246 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:27:13,247 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'player_id': 'VirtualPlayerID0602', 'player_name': '花千树映雪', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:27:13,248 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 花千树映雪(VirtualPlayerID0602), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: b55c55c5-4d57-45b5-be5c-661464e45bcd, 消息: 物品_3
2025-07-28 12:27:13,250 - [StatusUpdateThread] - INFO - 玩家 花千树映雪 本次是第 1 次成功抓取。
2025-07-28 12:27:13,254 - [StatusUpdateThread] - INFO - 为玩家 花千树映雪 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:13,255 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:27:13,256 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 321, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:27:13,257 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 321, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:13,394 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:13,398 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:13,912 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:13,920 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:14,101 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C8638B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 31
2025-07-28 12:27:14,114 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C8638B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:14,117 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:14,433 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:14,439 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:14,665 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:27:14,667 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['花千树映雪(1次)']。已选择: 花千树映雪
2025-07-28 12:27:14,670 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花千树映雪 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:14,950 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:14,959 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:15,470 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:15,473 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:15,990 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:15,991 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:16,133 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:16,144 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:27:16,146 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:16,155 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:16,274 - [Thread-15] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 393, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:27:16,284 - [Thread-15] - DEBUG - Response received {'d': {'requestId': 393, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:16,497 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:16,499 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:17,014 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:17,015 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:17,519 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676830.274494, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:27:17,520 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:17,774 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'b55c55c5-4d57-45b5-be5c-661464e45bcd', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:17,778 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=b55c55c5-4d57-45b5-be5c-661464e45bcd
2025-07-28 12:27:17,779 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:17,780 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0602, player_name=花千树映雪, requested_object_id=3
2025-07-28 12:27:17,781 - [MoveServiceEventReceiver] - INFO - 移动服务请求 b55c55c5-4d57-45b5-be5c-661464e45bcd (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:17,782 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 花千树映雪(VirtualPlayerID0602)
2025-07-28 12:27:17,784 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:27:17,785 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 花千树映雪(VirtualPlayerID0602), 结果: 物品_3, 订单: None
2025-07-28 12:27:17,790 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:17,793 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:18,036 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:27:18,039 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学反应方程式(VirtualPlayerID0601) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:27:18,044 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 化学反应方程式(VirtualPlayerID0601) 确保抓中, z_offset_extra=0
2025-07-28 12:27:18,049 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=81c7dd86-c737-4663-ba2f-9dcdd196d7be, Player=化学反应方程式, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:27:18,051 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:27:18,054 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学反应方程式(VirtualPlayerID0601) 抓取指令已发送到移动服务，命令ID: 81c7dd86-c737-4663-ba2f-9dcdd196d7be
2025-07-28 12:27:18,056 - [MoveServiceEventReceiver] - INFO - 抓取指令 81c7dd86-c737-4663-ba2f-9dcdd196d7be 已被移动服务接受并加入队列
2025-07-28 12:27:18,083 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.030秒 - 玩家: VirtualPlayerID0602, 结果: 物品_3, 订单: NULL
2025-07-28 12:27:18,086 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0602] = 1
2025-07-28 12:27:18,180 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BCBB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 32
2025-07-28 12:27:18,184 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BCBB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:18,185 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:18,560 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be'}, pending_retry_player: None
2025-07-28 12:27:18,562 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:18,687 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学反应方程式 已完成游戏，总游戏次数: 2
2025-07-28 12:27:19,066 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be'}, pending_retry_player: None
2025-07-28 12:27:19,072 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:19,588 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be'}, pending_retry_player: None
2025-07-28 12:27:19,590 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:20,095 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be'}, pending_retry_player: None
2025-07-28 12:27:20,097 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:20,186 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:20,190 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:27:20,191 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:20,205 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:20,614 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be'}, pending_retry_player: None
2025-07-28 12:27:20,618 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:20,705 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:27:20,707 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['化学反应方程式(2次)']。已选择: 化学反应方程式
2025-07-28 12:27:20,709 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学反应方程式 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:21,023 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:27:21,025 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=81c7dd86-c737-4663-ba2f-9dcdd196d7be
2025-07-28 12:27:21,026 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:27:21,026 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0601, player_name=化学反应方程式, requested_object_id=5
2025-07-28 12:27:21,027 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:27:21,028 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'player_id': 'VirtualPlayerID0601', 'player_name': '化学反应方程式', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:27:21,028 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 化学反应方程式(VirtualPlayerID0601), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: 81c7dd86-c737-4663-ba2f-9dcdd196d7be, 消息: 物品_5
2025-07-28 12:27:21,031 - [StatusUpdateThread] - INFO - 玩家 化学反应方程式 本次是第 2 次成功抓取。
2025-07-28 12:27:21,032 - [StatusUpdateThread] - INFO - 为玩家 化学反应方程式 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:21,035 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:27:21,036 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 936, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:27:21,043 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 936, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:21,133 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:21,135 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:21,648 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:21,649 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:22,155 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:22,157 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:22,249 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 33
2025-07-28 12:27:22,254 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:27:22,255 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:27:22,255 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:27:22,256 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:27:22,256 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:27:22,257 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:27:22,258 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:27:22,259 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:27:22,657 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:27:22,672 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:22,676 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:22,708 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:27:23,187 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:23,189 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:23,266 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:27:23,274 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:27:23,280 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:27:23,286 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:23,286 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:27:23,286 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:27:23,287 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 12
2025-07-28 12:27:23,287 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:27:23,288 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:23,289 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:23,707 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:23,711 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:24,054 - [Thread-16] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 909, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:27:24,059 - [Thread-16] - DEBUG - Response received {'d': {'requestId': 909, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:24,069 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:27:24,074 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:27:24,079 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 127.7 秒
2025-07-28 12:27:24,081 - [HealthCheckThread] - WARNING - 已经 127.7 秒没有成功请求，尝试恢复连接...
2025-07-28 12:27:24,086 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:24,091 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:24,227 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:24,229 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:24,733 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:24,739 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:25,249 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0601', 'name': '化学反应方程式', 'start_time': **********.0392942, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:25,250 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:25,329 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A40A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 34
2025-07-28 12:27:25,332 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A40A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:25,333 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:25,546 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '81c7dd86-c737-4663-ba2f-9dcdd196d7be', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:25,549 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=81c7dd86-c737-4663-ba2f-9dcdd196d7be
2025-07-28 12:27:25,550 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:25,551 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0601, player_name=化学反应方程式, requested_object_id=5
2025-07-28 12:27:25,552 - [MoveServiceEventReceiver] - INFO - 移动服务请求 81c7dd86-c737-4663-ba2f-9dcdd196d7be (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:25,553 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 化学反应方程式(VirtualPlayerID0601)
2025-07-28 12:27:25,555 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:27:25,555 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 化学反应方程式(VirtualPlayerID0601), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:27:25,558 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:25,560 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:25,766 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:27:25,774 - [GameProcessThread] - INFO - [游戏线程] 玩家 笑出腹肌的胖子(VirtualPlayerID0599) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:27:25,777 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 笑出腹肌的胖子(VirtualPlayerID0599) 确保抓中, z_offset_extra=0
2025-07-28 12:27:25,785 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=f1769360-3a46-4d1b-b764-12c5e0e81330, Player=笑出腹肌的胖子, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:27:25,785 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:27:25,786 - [GameProcessThread] - INFO - [游戏线程] 玩家 笑出腹肌的胖子(VirtualPlayerID0599) 抓取指令已发送到移动服务，命令ID: f1769360-3a46-4d1b-b764-12c5e0e81330
2025-07-28 12:27:25,786 - [MoveServiceEventReceiver] - INFO - 抓取指令 f1769360-3a46-4d1b-b764-12c5e0e81330 已被移动服务接受并加入队列
2025-07-28 12:27:25,788 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.038秒 - 玩家: VirtualPlayerID0601, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:27:25,789 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0601] = 2
2025-07-28 12:27:26,127 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4C10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:27:26,129 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:27:26,302 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330'}, pending_retry_player: None
2025-07-28 12:27:26,307 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:26,743 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 已完成游戏，总游戏次数: 3
2025-07-28 12:27:26,824 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330'}, pending_retry_player: None
2025-07-28 12:27:26,826 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:27,344 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330'}, pending_retry_player: None
2025-07-28 12:27:27,342 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:27,346 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:27,347 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:27:27,349 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:27,357 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:27,863 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330'}, pending_retry_player: None
2025-07-28 12:27:27,865 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:28,382 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330'}, pending_retry_player: None
2025-07-28 12:27:28,384 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:28,807 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:27:28,807 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=f1769360-3a46-4d1b-b764-12c5e0e81330
2025-07-28 12:27:28,808 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:27:28,808 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0599, player_name=笑出腹肌的胖子, requested_object_id=2
2025-07-28 12:27:28,808 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:27:28,809 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'player_id': 'VirtualPlayerID0599', 'player_name': '笑出腹肌的胖子', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:27:28,809 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 笑出腹肌的胖子(VirtualPlayerID0599), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: f1769360-3a46-4d1b-b764-12c5e0e81330, 消息: 物品_2
2025-07-28 12:27:28,809 - [StatusUpdateThread] - INFO - 玩家 笑出腹肌的胖子 本次是第 3 次成功抓取。
2025-07-28 12:27:28,810 - [StatusUpdateThread] - INFO - 为玩家 笑出腹肌的胖子 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:28,811 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:27:28,811 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 191, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:27:28,812 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 191, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:28,901 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:28,903 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:29,413 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989FD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 35
2025-07-28 12:27:29,415 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989FD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:29,416 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:29,423 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:29,424 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:29,938 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:29,943 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:30,460 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:30,460 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:30,976 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:30,980 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:31,431 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:31,435 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:27:31,436 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:31,441 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:31,496 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:31,497 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:31,828 - [Thread-17] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 266, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:27:31,831 - [Thread-17] - DEBUG - Response received {'d': {'requestId': 266, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:32,002 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:32,006 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:32,522 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:32,529 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:32,885 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:27:32,908 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:27:33,043 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0599', 'name': '笑出腹肌的胖子', 'start_time': **********.7742262, 'target_id': '2', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:33,045 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:33,279 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f1769360-3a46-4d1b-b764-12c5e0e81330', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:33,281 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=f1769360-3a46-4d1b-b764-12c5e0e81330
2025-07-28 12:27:33,282 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:33,283 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0599, player_name=笑出腹肌的胖子, requested_object_id=2
2025-07-28 12:27:33,285 - [MoveServiceEventReceiver] - INFO - 移动服务请求 f1769360-3a46-4d1b-b764-12c5e0e81330 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:33,285 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 笑出腹肌的胖子(VirtualPlayerID0599)
2025-07-28 12:27:33,285 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:27:33,286 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 笑出腹肌的胖子(VirtualPlayerID0599), 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:27:33,286 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:33,287 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:33,450 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.031秒 - 玩家: VirtualPlayerID0599, 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:27:33,450 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0599] = 3
2025-07-28 12:27:33,473 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4F10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 36
2025-07-28 12:27:33,478 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4F10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:27:33,480 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:27:33,482 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:27:33,485 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:27:33,485 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:27:33,486 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:27:33,486 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:27:33,488 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:27:33,560 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:27:33,566 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学物质大发现(VirtualPlayerID0600) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:27:33,569 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 化学物质大发现(VirtualPlayerID0600) 确保抓中, z_offset_extra=0
2025-07-28 12:27:33,574 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=cd84ac70-53bd-4800-b567-964a0b1ff839, Player=化学物质大发现, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:27:33,580 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:27:33,581 - [GameProcessThread] - INFO - [游戏线程] 玩家 化学物质大发现(VirtualPlayerID0600) 抓取指令已发送到移动服务，命令ID: cd84ac70-53bd-4800-b567-964a0b1ff839
2025-07-28 12:27:33,584 - [MoveServiceEventReceiver] - INFO - 抓取指令 cd84ac70-53bd-4800-b567-964a0b1ff839 已被移动服务接受并加入队列
2025-07-28 12:27:34,099 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839'}, pending_retry_player: None
2025-07-28 12:27:34,100 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:34,491 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:27:34,492 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:27:34,493 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:27:34,493 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:34,493 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:27:34,494 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:27:34,494 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 13
2025-07-28 12:27:34,495 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:27:34,495 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:34,499 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:34,619 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839'}, pending_retry_player: None
2025-07-28 12:27:34,620 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:34,790 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 已完成游戏，总游戏次数: 3
2025-07-28 12:27:34,790 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:27:34,792 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['笑出腹肌的胖子(3次)', '化学物质大发现(3次)']。已选择: 笑出腹肌的胖子
2025-07-28 12:27:34,793 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 笑出腹肌的胖子 已加入队列末尾。优先级: 2.10 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:35,124 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839'}, pending_retry_player: None
2025-07-28 12:27:35,126 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:35,642 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839'}, pending_retry_player: None
2025-07-28 12:27:35,645 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:36,163 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839'}, pending_retry_player: None
2025-07-28 12:27:36,167 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:36,527 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BCAC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 37
2025-07-28 12:27:36,530 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BCAC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:36,531 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:36,541 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:27:36,544 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=cd84ac70-53bd-4800-b567-964a0b1ff839
2025-07-28 12:27:36,545 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:27:36,545 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0600, player_name=化学物质大发现, requested_object_id=2
2025-07-28 12:27:36,546 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:27:36,547 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'player_id': 'VirtualPlayerID0600', 'player_name': '化学物质大发现', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:27:36,547 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 化学物质大发现(VirtualPlayerID0600), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: cd84ac70-53bd-4800-b567-964a0b1ff839, 消息: 物品_2
2025-07-28 12:27:36,547 - [StatusUpdateThread] - INFO - 玩家 化学物质大发现 本次是第 3 次成功抓取。
2025-07-28 12:27:36,548 - [StatusUpdateThread] - INFO - 为玩家 化学物质大发现 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:36,549 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:27:36,549 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 961, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:27:36,550 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 961, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:36,683 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:36,689 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:37,204 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:37,210 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:37,723 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:37,728 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:38,242 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:38,245 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:38,543 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:38,545 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:27:38,545 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:38,550 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:38,752 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:38,752 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:39,256 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:39,258 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:39,553 - [Thread-18] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 143, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:27:39,560 - [Thread-18] - DEBUG - Response received {'d': {'requestId': 143, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:39,776 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:39,777 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:40,281 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:40,284 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:40,578 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4B80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 38
2025-07-28 12:27:40,580 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4B80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:40,581 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:40,798 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0600', 'name': '化学物质大发现', 'start_time': 1753676853.5669076, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:40,802 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:40,829 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:27:40,831 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['化学物质大发现(3次)']。已选择: 化学物质大发现
2025-07-28 12:27:40,833 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 化学物质大发现 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:41,019 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cd84ac70-53bd-4800-b567-964a0b1ff839', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:41,020 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=cd84ac70-53bd-4800-b567-964a0b1ff839
2025-07-28 12:27:41,020 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:41,021 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0600, player_name=化学物质大发现, requested_object_id=2
2025-07-28 12:27:41,021 - [MoveServiceEventReceiver] - INFO - 移动服务请求 cd84ac70-53bd-4800-b567-964a0b1ff839 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:41,021 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 化学物质大发现(VirtualPlayerID0600)
2025-07-28 12:27:41,022 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:27:41,022 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 化学物质大发现(VirtualPlayerID0600), 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:27:41,023 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:41,023 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:41,089 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.024秒 - 玩家: VirtualPlayerID0600, 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:27:41,092 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0600] = 3
2025-07-28 12:27:41,319 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:27:41,325 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:27:41,329 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小龙女的现代生活(VirtualPlayerID0596) 确保抓中, z_offset_extra=0
2025-07-28 12:27:41,334 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=5d60e38c-f7ab-47e9-a224-3985bcd996fe, Player=小龙女的现代生活, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:27:41,338 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:27:41,339 - [GameProcessThread] - INFO - [游戏线程] 玩家 小龙女的现代生活(VirtualPlayerID0596) 抓取指令已发送到移动服务，命令ID: 5d60e38c-f7ab-47e9-a224-3985bcd996fe
2025-07-28 12:27:41,343 - [MoveServiceEventReceiver] - INFO - 抓取指令 5d60e38c-f7ab-47e9-a224-3985bcd996fe 已被移动服务接受并加入队列
2025-07-28 12:27:41,851 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe'}, pending_retry_player: None
2025-07-28 12:27:41,853 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:42,356 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe'}, pending_retry_player: None
2025-07-28 12:27:42,362 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:42,592 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:42,594 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:27:42,595 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:42,600 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:42,846 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已完成游戏，总游戏次数: 4
2025-07-28 12:27:42,847 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小龙女的现代生活 已退休。
2025-07-28 12:27:42,848 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 新玩家 眼镜的树叶 已补充到活跃池。
2025-07-28 12:27:42,876 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe'}, pending_retry_player: None
2025-07-28 12:27:42,878 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:43,146 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:27:43,204 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:27:43,396 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe'}, pending_retry_player: None
2025-07-28 12:27:43,399 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:43,902 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe'}, pending_retry_player: None
2025-07-28 12:27:43,908 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:44,359 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:27:44,361 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=5d60e38c-f7ab-47e9-a224-3985bcd996fe
2025-07-28 12:27:44,362 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:27:44,362 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=1
2025-07-28 12:27:44,362 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:27:44,363 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'player_id': 'VirtualPlayerID0596', 'player_name': '小龙女的现代生活', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:27:44,363 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小龙女的现代生活(VirtualPlayerID0596), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 5d60e38c-f7ab-47e9-a224-3985bcd996fe, 消息: 物品_1
2025-07-28 12:27:44,364 - [StatusUpdateThread] - INFO - 玩家 小龙女的现代生活 本次是第 4 次成功抓取。
2025-07-28 12:27:44,364 - [StatusUpdateThread] - INFO - 为玩家 小龙女的现代生活 生成优惠券提示: 获得满减160元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:44,365 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(4次): 抓中特效4
2025-07-28 12:27:44,366 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 139, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': True}}}
2025-07-28 12:27:44,367 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 139, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:44,421 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:44,423 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:44,626 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859B80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 39
2025-07-28 12:27:44,629 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C859B80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:27:44,630 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:27:44,631 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:27:44,631 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:27:44,634 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:27:44,644 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:27:44,645 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:27:44,648 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:27:44,940 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:44,945 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:45,459 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:45,461 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:45,664 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:27:45,670 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:27:45,675 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:27:45,679 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:45,681 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:27:45,681 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:27:45,682 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 14
2025-07-28 12:27:45,683 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:27:45,683 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:45,695 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:45,966 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:45,967 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:46,484 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:46,486 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:47,003 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:47,011 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:47,379 - [Thread-19] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 389, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': False}}}
2025-07-28 12:27:47,384 - [Thread-19] - DEBUG - Response received {'d': {'requestId': 389, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:47,519 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:47,520 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:47,723 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9C7370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 40
2025-07-28 12:27:47,725 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9C7370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:47,726 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:48,026 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:48,029 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:48,532 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0596', 'name': '小龙女的现代生活', 'start_time': 1753676861.3257287, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:27:48,534 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:48,830 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5d60e38c-f7ab-47e9-a224-3985bcd996fe', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:48,837 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=5d60e38c-f7ab-47e9-a224-3985bcd996fe
2025-07-28 12:27:48,841 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:48,844 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0596, player_name=小龙女的现代生活, requested_object_id=1
2025-07-28 12:27:48,847 - [MoveServiceEventReceiver] - INFO - 移动服务请求 5d60e38c-f7ab-47e9-a224-3985bcd996fe (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:48,848 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小龙女的现代生活(VirtualPlayerID0596)
2025-07-28 12:27:48,851 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:27:48,851 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小龙女的现代生活(VirtualPlayerID0596), 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:27:48,854 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:48,857 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:49,049 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:27:49,051 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:27:49,052 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 云散了(VirtualPlayerID0597) 确保抓中, z_offset_extra=0
2025-07-28 12:27:49,053 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=2f536868-c324-4e99-bf51-b7d6ae9f3292, Player=云散了, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:27:49,054 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:27:49,054 - [GameProcessThread] - INFO - [游戏线程] 玩家 云散了(VirtualPlayerID0597) 抓取指令已发送到移动服务，命令ID: 2f536868-c324-4e99-bf51-b7d6ae9f3292
2025-07-28 12:27:49,055 - [MoveServiceEventReceiver] - INFO - 抓取指令 2f536868-c324-4e99-bf51-b7d6ae9f3292 已被移动服务接受并加入队列
2025-07-28 12:27:49,322 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.035秒 - 玩家: VirtualPlayerID0596, 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:27:49,323 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0596] = 4
2025-07-28 12:27:49,571 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292'}, pending_retry_player: None
2025-07-28 12:27:49,572 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:49,728 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:49,731 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:27:49,733 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:49,739 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:50,078 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292'}, pending_retry_player: None
2025-07-28 12:27:50,080 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:50,581 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292'}, pending_retry_player: None
2025-07-28 12:27:50,584 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:50,891 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已完成游戏，总游戏次数: 4
2025-07-28 12:27:50,891 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 云散了 已退休。
2025-07-28 12:27:50,892 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 新玩家 青竹🎋幽思 已补充到活跃池。
2025-07-28 12:27:50,892 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:27:50,892 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['眼镜的树叶(0次)', '青竹🎋幽思(0次)']。已选择: 眼镜的树叶
2025-07-28 12:27:50,893 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 眼镜的树叶 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:51,098 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292'}, pending_retry_player: None
2025-07-28 12:27:51,104 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:51,615 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292'}, pending_retry_player: None
2025-07-28 12:27:51,617 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:51,775 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863DF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 41
2025-07-28 12:27:51,781 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C863DF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:51,782 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:52,071 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:27:52,074 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=2f536868-c324-4e99-bf51-b7d6ae9f3292
2025-07-28 12:27:52,076 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:27:52,080 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=5
2025-07-28 12:27:52,080 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:27:52,081 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'player_id': 'VirtualPlayerID0597', 'player_name': '云散了', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:27:52,082 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 云散了(VirtualPlayerID0597), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: 2f536868-c324-4e99-bf51-b7d6ae9f3292, 消息: 物品_5
2025-07-28 12:27:52,084 - [StatusUpdateThread] - INFO - 玩家 云散了 本次是第 4 次成功抓取。
2025-07-28 12:27:52,085 - [StatusUpdateThread] - INFO - 为玩家 云散了 生成优惠券提示: 获得满减160元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:52,087 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(4次): 抓中特效4
2025-07-28 12:27:52,090 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 608, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': True}}}
2025-07-28 12:27:52,091 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 608, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:52,134 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:52,136 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:52,653 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:52,659 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:52,905 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:27:52,909 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['青竹🎋幽思(0次)']。已选择: 青竹🎋幽思
2025-07-28 12:27:52,911 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 青竹🎋幽思 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:27:53,175 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:53,178 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:53,510 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.044秒
2025-07-28 12:27:53,511 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:27:53,531 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 9 个玩家, 成功更新 9 条记录.
2025-07-28 12:27:53,682 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:53,684 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:53,792 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:27:53,796 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:27:53,797 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:53,802 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:54,202 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:54,205 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:54,716 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:54,717 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:55,097 - [Thread-20] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 208, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': False}}}
2025-07-28 12:27:55,106 - [Thread-20] - DEBUG - Response received {'d': {'requestId': 208, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:55,223 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:55,224 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:55,727 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:55,735 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:55,822 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC970>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 42
2025-07-28 12:27:55,828 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9BC970>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:27:55,832 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:27:55,836 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:27:55,841 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:27:55,842 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:27:55,844 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:27:55,845 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:27:55,848 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:27:56,135 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:27:56,137 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:27:56,138 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 159.8 秒
2025-07-28 12:27:56,139 - [HealthCheckThread] - WARNING - 已经 159.8 秒没有成功请求，尝试恢复连接...
2025-07-28 12:27:56,140 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:56,152 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:56,248 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0597', 'name': '云散了', 'start_time': **********.0517, 'target_id': '5', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:27:56,249 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:56,540 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '2f536868-c324-4e99-bf51-b7d6ae9f3292', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:27:56,540 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=2f536868-c324-4e99-bf51-b7d6ae9f3292
2025-07-28 12:27:56,542 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:27:56,542 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0597, player_name=云散了, requested_object_id=5
2025-07-28 12:27:56,543 - [MoveServiceEventReceiver] - INFO - 移动服务请求 2f536868-c324-4e99-bf51-b7d6ae9f3292 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:27:56,543 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 云散了(VirtualPlayerID0597)
2025-07-28 12:27:56,543 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:27:56,544 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 云散了(VirtualPlayerID0597), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:27:56,545 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:27:56,545 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:27:56,610 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.039秒 - 玩家: VirtualPlayerID0597, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:27:56,611 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0597] = 4
2025-07-28 12:27:56,760 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:27:56,763 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:27:56,764 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 清风文具店(VirtualPlayerID0598) 确保抓中, z_offset_extra=0
2025-07-28 12:27:56,766 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=839508e2-a342-42d8-b1e7-492ea1e54f0c, Player=清风文具店, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:27:56,767 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:27:56,768 - [GameProcessThread] - INFO - [游戏线程] 玩家 清风文具店(VirtualPlayerID0598) 抓取指令已发送到移动服务，命令ID: 839508e2-a342-42d8-b1e7-492ea1e54f0c
2025-07-28 12:27:56,774 - [MoveServiceEventReceiver] - INFO - 抓取指令 839508e2-a342-42d8-b1e7-492ea1e54f0c 已被移动服务接受并加入队列
2025-07-28 12:27:56,854 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:27:56,859 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:27:56,864 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:27:56,865 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:27:56,866 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:27:56,867 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:27:56,867 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 15
2025-07-28 12:27:56,868 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:27:56,868 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:27:56,879 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:27:56,933 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已完成游戏，总游戏次数: 4
2025-07-28 12:27:56,941 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 清风文具店 已退休。
2025-07-28 12:27:56,943 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 新玩家 我太难了 已补充到活跃池。
2025-07-28 12:27:57,282 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c'}, pending_retry_player: None
2025-07-28 12:27:57,284 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:57,799 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c'}, pending_retry_player: None
2025-07-28 12:27:57,801 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:58,178 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9B4F70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:27:58,182 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:27:58,304 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c'}, pending_retry_player: None
2025-07-28 12:27:58,307 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:58,824 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c'}, pending_retry_player: None
2025-07-28 12:27:58,826 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:58,920 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A49D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 43
2025-07-28 12:27:58,924 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A49D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:27:58,924 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:27:59,328 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c'}, pending_retry_player: None
2025-07-28 12:27:59,328 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:27:59,721 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:27:59,723 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=839508e2-a342-42d8-b1e7-492ea1e54f0c
2025-07-28 12:27:59,724 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:27:59,725 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=2
2025-07-28 12:27:59,726 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:27:59,726 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'player_id': 'VirtualPlayerID0598', 'player_name': '清风文具店', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:27:59,727 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 清风文具店(VirtualPlayerID0598), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: 839508e2-a342-42d8-b1e7-492ea1e54f0c, 消息: 物品_2
2025-07-28 12:27:59,730 - [StatusUpdateThread] - INFO - 玩家 清风文具店 本次是第 4 次成功抓取。
2025-07-28 12:27:59,731 - [StatusUpdateThread] - INFO - 为玩家 清风文具店 生成优惠券提示: 获得满减160元券！
再抓到一次优惠翻倍！
2025-07-28 12:27:59,734 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(4次): 抓中特效4
2025-07-28 12:27:59,736 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 218, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': True}}}
2025-07-28 12:27:59,747 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 218, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:27:59,847 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:27:59,849 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:00,361 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:00,362 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:00,874 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:00,876 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:00,935 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:28:00,940 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:28:00,941 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:28:00,953 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:28:01,392 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:01,394 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:01,909 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:01,910 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:02,424 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:02,427 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:02,765 - [Thread-21] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 319, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': False}}}
2025-07-28 12:28:02,768 - [Thread-21] - DEBUG - Response received {'d': {'requestId': 319, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:28:02,940 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:02,944 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:03,005 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4520>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 44
2025-07-28 12:28:03,011 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4520>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:28:03,012 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:28:03,454 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:03,456 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:03,752 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:28:03,787 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 9 个玩家, 成功更新 9 条记录.
2025-07-28 12:28:03,971 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0598', 'name': '清风文具店', 'start_time': **********.7637324, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:28:03,978 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:04,226 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '839508e2-a342-42d8-b1e7-492ea1e54f0c', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:28:04,227 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=839508e2-a342-42d8-b1e7-492ea1e54f0c
2025-07-28 12:28:04,228 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:28:04,229 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0598, player_name=清风文具店, requested_object_id=2
2025-07-28 12:28:04,229 - [MoveServiceEventReceiver] - INFO - 移动服务请求 839508e2-a342-42d8-b1e7-492ea1e54f0c (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:28:04,229 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 清风文具店(VirtualPlayerID0598)
2025-07-28 12:28:04,230 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:28:04,230 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 清风文具店(VirtualPlayerID0598), 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:28:04,231 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:28:04,232 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:28:04,346 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.044秒 - 玩家: VirtualPlayerID0598, 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:28:04,347 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0598] = 4
2025-07-28 12:28:04,493 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:28:04,494 - [GameProcessThread] - INFO - [游戏线程] 玩家 花千树映雪(VirtualPlayerID0602) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:28:04,494 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 花千树映雪(VirtualPlayerID0602) 确保抓中, z_offset_extra=0
2025-07-28 12:28:04,495 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=721c8b99-da1e-443b-a5e0-e1dba78c1975, Player=花千树映雪, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:28:04,496 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:28:04,496 - [GameProcessThread] - INFO - [游戏线程] 玩家 花千树映雪(VirtualPlayerID0602) 抓取指令已发送到移动服务，命令ID: 721c8b99-da1e-443b-a5e0-e1dba78c1975
2025-07-28 12:28:04,497 - [MoveServiceEventReceiver] - INFO - 抓取指令 721c8b99-da1e-443b-a5e0-e1dba78c1975 已被移动服务接受并加入队列
2025-07-28 12:28:04,981 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花千树映雪 已完成游戏，总游戏次数: 2
2025-07-28 12:28:04,981 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:28:04,982 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['我太难了(0次)', '花千树映雪(2次)']。已选择: 我太难了
2025-07-28 12:28:04,983 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 我太难了 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:28:05,013 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975'}, pending_retry_player: None
2025-07-28 12:28:05,013 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:05,028 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:28:05,029 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:28:05,029 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:28:05,034 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:28:05,521 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975'}, pending_retry_player: None
2025-07-28 12:28:05,523 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:06,026 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975'}, pending_retry_player: None
2025-07-28 12:28:06,027 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:06,531 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975'}, pending_retry_player: None
2025-07-28 12:28:06,533 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:07,000 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:28:07,002 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['花千树映雪(2次)']。已选择: 花千树映雪
2025-07-28 12:28:07,010 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花千树映雪 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:28:07,049 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975'}, pending_retry_player: None
2025-07-28 12:28:07,049 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989880>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 45
2025-07-28 12:28:07,051 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:07,051 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C989880>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:28:07,052 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:28:07,052 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:28:07,053 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:28:07,053 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:28:07,053 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:28:07,054 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:28:07,055 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:28:07,455 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:28:07,465 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=721c8b99-da1e-443b-a5e0-e1dba78c1975
2025-07-28 12:28:07,467 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:28:07,471 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0602, player_name=花千树映雪, requested_object_id=1
2025-07-28 12:28:07,477 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:28:07,478 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'player_id': 'VirtualPlayerID0602', 'player_name': '花千树映雪', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:28:07,478 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 花千树映雪(VirtualPlayerID0602), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 721c8b99-da1e-443b-a5e0-e1dba78c1975, 消息: 物品_1
2025-07-28 12:28:07,479 - [StatusUpdateThread] - INFO - 玩家 花千树映雪 本次是第 2 次成功抓取。
2025-07-28 12:28:07,479 - [StatusUpdateThread] - INFO - 为玩家 花千树映雪 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:28:07,480 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:28:07,480 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 544, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:28:07,481 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 544, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:28:07,567 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:07,570 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:08,070 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:28:08,077 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:28:08,077 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:28:08,078 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:28:08,078 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:28:08,079 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:28:08,079 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 16
2025-07-28 12:28:08,079 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:28:08,080 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:28:08,085 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:28:08,089 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:08,092 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:08,606 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:08,607 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:09,127 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:09,127 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:09,631 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:09,634 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:10,123 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9896A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 46
2025-07-28 12:28:10,126 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9896A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:28:10,127 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:28:10,149 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:10,156 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:10,492 - [Thread-22] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 874, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:28:10,495 - [Thread-22] - DEBUG - Response received {'d': {'requestId': 874, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:28:10,665 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:10,668 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:11,182 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0602', 'name': '花千树映雪', 'start_time': 1753676884.4946814, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '721c8b99-da1e-443b-a5e0-e1dba78c1975', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:28:11,185 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:28:11,616 - [MainThread] - INFO - 收到终止信号，标志置 False，主线程将退出并在 finally 做清理
2025-07-28 12:28:11,617 - [MainThread] - INFO - 进入清理阶段...
2025-07-28 12:28:11,622 - [MainThread] - INFO - [清理] 开始执行清理操作
2025-07-28 12:28:11,622 - [MainThread] - DEBUG - [清理] 设置 stop_flag['running'] = False
2025-07-28 12:28:11,623 - [MainThread] - INFO - [清理] 阶段1: 停止所有外部输入...
2025-07-28 12:28:11,623 - [MainThread] - DEBUG - [清理] 停止消息获取线程...
2025-07-28 12:28:11,623 - [MainThread] - DEBUG - [消息线程] 尝试停止消息线程
2025-07-28 12:28:11,623 - [MainThread] - DEBUG - [消息线程] 等待消息线程结束
2025-07-28 12:28:11,662 - [DetectionReceiver] - INFO - 已断开检测服务器连接
2025-07-28 12:28:11,664 - [DetectionReceiver] - INFO - 检测数据接收器已停止
2025-07-28 12:28:11,758 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已停止。
2025-07-28 12:28:11,760 - [MoveServiceEventReceiver] - INFO - 正在断开与移动服务的连接...
2025-07-28 12:28:11,762 - [MoveServiceEventReceiver] - INFO - 与移动服务的连接已断开。
2025-07-28 12:28:12,136 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:28:12,140 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:28:12,141 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:28:12,150 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:28:13,041 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 管理器线程已停止。
2025-07-28 12:28:13,627 - [MainThread] - WARNING - [消息线程] 消息线程未在2秒内结束
2025-07-28 12:28:13,629 - [MainThread] - DEBUG - [消息线程] 消息队列已清空
2025-07-28 12:28:13,629 - [MainThread] - DEBUG - [清理] 停止移动服务客户端...
2025-07-28 12:28:13,629 - [MainThread] - INFO - 正在停止移动服务客户端...
2025-07-28 12:28:13,630 - [MainThread] - INFO - 正在断开与移动服务的连接...
2025-07-28 12:28:13,630 - [MainThread] - INFO - 与移动服务的连接已断开。
2025-07-28 12:28:13,630 - [MainThread] - INFO - 移动服务客户端已停止。
2025-07-28 12:28:13,630 - [MainThread] - DEBUG - [清理] 断开检测服务器连接...
2025-07-28 12:28:13,631 - [MainThread] - INFO - 已断开检测服务器连接
2025-07-28 12:28:13,631 - [MainThread] - INFO - [清理] 阶段2: 停止独立进程...
2025-07-28 12:28:13,631 - [MainThread] - DEBUG - [清理] 关闭GUI通信队列...
2025-07-28 12:28:13,636 - [MainThread] - DEBUG - [清理] 停止Web显示进程...
2025-07-28 12:28:13,927 - [MainThread] - INFO - [清理] 阶段3: 等待内部线程结束...
2025-07-28 12:28:13,929 - [MainThread] - DEBUG - [清理] 等待主循环监控线程结束…
2025-07-28 12:28:14,001 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:28:14,079 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 10 个玩家, 成功更新 10 条记录.
2025-07-28 12:28:14,194 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 47
2025-07-28 12:28:14,196 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4DC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:28:14,197 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:28:15,939 - [MainThread] - WARNING - [清理] 主循环监控线程未在2秒内结束
2025-07-28 12:28:15,940 - [MainThread] - INFO - [清理] 阶段4: 最终数据同步和资源释放...
2025-07-28 12:28:15,942 - [MainThread] - DEBUG - [清理] 停止数据库同步管理器...
2025-07-28 12:28:15,942 - [MainThread] - INFO - 停止数据库同步线程
2025-07-28 12:28:16,111 - [MainThread] - INFO - 数据库同步线程已停止
2025-07-28 12:28:16,111 - [MainThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetSceneItemList', 'requestId': 588, 'requestData': {'sceneName': '场景1'}}}
2025-07-28 12:28:16,123 - [MainThread] - DEBUG - Response received {'d': {'requestId': 588, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetSceneItemList', 'responseData': {'sceneItems': [{'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 58, 'sceneItemIndex': 0, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '没抓到特效', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'color_source_v3', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 49, 'sceneItemIndex': 1, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '色源', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'vlc_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 40, 'sceneItemIndex': 2, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 8, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 540.0, 'positionY': 1920.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '背景视频', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'image_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 62, 'sceneItemIndex': 3, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '临时图像', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'dshow_input', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 46, 'sceneItemIndex': 4, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1089.0, 'positionX': 1089.0, 'positionY': -16.0, 'rotation': 90.0, 'scaleX': 1.0083333253860474, 'scaleY': 1.0083333253860474, 'sourceHeight': 1080.0, 'sourceWidth': 1920.0, 'width': 1936.0}, 'sourceName': '主摄像头', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 1, 'sceneItemIndex': 5, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 116.22856903076172, 'positionX': 214.0, 'positionY': 44.77142333984375, 'rotation': 0.0, 'scaleX': 1.6139534711837769, 'scaleY': 1.6142857074737549, 'sourceHeight': 72.0, 'sourceWidth': 444.0, 'width': 716.5953369140625}, 'sourceName': '大标题', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 60, 'sceneItemIndex': 6, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 14.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4541666507720947, 'scaleY': 1.4546159505844116, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 53, 'sceneItemIndex': 7, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': -4.0, 'rotation': 0.0, 'scaleX': 1.5, 'scaleY': 1.5003879070281982, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效2', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 56, 'sceneItemIndex': 8, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 21.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4416667222976685, 'scaleY': 1.44142746925354, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效3', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 57, 'sceneItemIndex': 9, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 22.0, 'positionY': -163.0, 'rotation': 0.0, 'scaleX': 1.4375, 'scaleY': 1.4375485181808472, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效4', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 34, 'sceneItemIndex': 10, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 164.0, 'positionX': 454.0, 'positionY': 132.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 164.0, 'sourceWidth': 626.0, 'width': 626.0}, 'sourceName': '说明1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'monitor_capture', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 41, 'sceneItemIndex': 11, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 734.0, 'rotation': 0.0, 'scaleX': 0.6026041507720947, 'scaleY': 0.6027777791023254, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '显示器采集', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'browser_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 50, 'sceneItemIndex': 12, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '浏览器显示', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}]}}, 'op': 7}
2025-07-28 12:28:16,125 - [MainThread] - WARNING - 在场景 '场景1' 中未找到源 '抓中特效'
2025-07-28 12:28:16,125 - [MainThread] - DEBUG - [清理] 强制隐藏 OBS 视频源
2025-07-28 12:28:16,126 - [MainThread] - DEBUG - [清理] 停止健康检查线程...
2025-07-28 12:28:16,126 - [MainThread] - DEBUG - [健康检查] 收到停止信号
2025-07-28 12:28:16,126 - [MainThread] - DEBUG - [健康检查] 等待线程结束
2025-07-28 12:28:16,207 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:28:16,208 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:28:16,209 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:28:16,213 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:28:18,137 - [MainThread] - INFO - 健康检查线程已停止
2025-07-28 12:28:18,143 - [MainThread] - DEBUG - [清理] 已获取队列锁，执行最终数据同步...
2025-07-28 12:28:18,160 - [MainThread] - INFO - 数据库操作: 更新队列完成，耗时 0.017秒
2025-07-28 12:28:18,249 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4970>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 48
2025-07-28 12:28:18,251 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002648C9A4970>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:28:18,251 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:28:18,252 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:28:18,252 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:28:18,253 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:28:18,253 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:28:18,254 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:28:18,254 - [MessagePollThread] - DEBUG - [消息线程] 消息获取线程结束
2025-07-28 12:28:18,254 - [MessagePollThread] - DEBUG - [消息流] 停止健康检查
2025-07-28 12:28:18,255 - [MessagePollThread] - DEBUG - [健康检查] 收到停止信号
2025-07-28 12:28:18,255 - [MessagePollThread] - DEBUG - [健康检查] 等待线程结束
2025-07-28 12:28:20,165 - [MainThread] - WARNING - 获取快照时未能获取 queue_lock，使用上次快照队列数据
2025-07-28 12:28:20,166 - [MainThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:28:20,186 - [MainThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 10 个玩家, 成功更新 10 条记录.
2025-07-28 12:28:20,186 - [MainThread] - INFO - [清理] 最终数据同步已完成
2025-07-28 12:28:20,187 - [MainThread] - DEBUG - [清理] 关闭数据库会话...
2025-07-28 12:28:20,202 - [MainThread] - INFO - 已关闭会话，ID: 1
2025-07-28 12:28:20,202 - [MainThread] - INFO - [清理] 数据库会话已关闭
2025-07-28 12:28:20,202 - [MainThread] - DEBUG - [清理] 队列锁已释放
2025-07-28 12:28:20,203 - [MainThread] - INFO - [清理] 清理操作成功
2025-07-28 12:28:20,259 - [MainThread] - INFO - 程序退出
