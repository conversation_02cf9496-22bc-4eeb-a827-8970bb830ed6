2025-07-28 12:10:00,251 - [MainThread] - INFO - 日志系统初始化完成
2025-07-28 12:10:00,251 - [MainThread] - INFO - 应用程序启动
2025-07-28 12:10:01,032 - [MainThread] - INFO - 信号处理器设置完成（仅置标志）
2025-07-28 12:10:01,032 - [MainThread] - INFO - 信号处理器已设置(使用共享状态字典)
2025-07-28 12:10:03,250 - [MainThread] - INFO - Web显示进程启动成功
2025-07-28 12:10:03,547 - [MainThread] - INFO - GUI 进程已通过 multiprocessing.Process 启动, PID: 1528
2025-07-28 12:10:03,547 - [MainThread] - INFO - 每个会话最大免费游戏次数: 1
2025-07-28 12:10:03,563 - [MainThread] - INFO - 初始化全局 OBSController
2025-07-28 12:10:03,563 - [MainThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-28 12:10:03,563 - [MainThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-28 12:10:07,677 - [MainThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-28 12:10:07,677 - [MainThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-28 12:10:07,677 - [MainThread] - INFO - 检查历史会话记录...
2025-07-28 12:10:07,677 - [MainThread] - INFO - 未找到历史会话记录，将创建新会话
2025-07-28 12:10:07,693 - [MainThread] - INFO - 已创建新会话，ID: 1
2025-07-28 12:10:07,693 - [MainThread] - INFO - 当前使用会话 ID: 1
2025-07-28 12:10:07,693 - [MainThread] - DEBUG - 当前会话ID已设置为: 1
2025-07-28 12:10:07,708 - [MainThread] - INFO - 会话开始时间: 2025-07-28T12:10:07.693022
2025-07-28 12:10:07,708 - [MainThread] - INFO - 使用新创建的会话，初始化空数据结构
2025-07-28 12:10:07,708 - [MainThread] - INFO - 初始化全局数据库同步管理器
2025-07-28 12:10:07,708 - [MainThread] - INFO - 启动数据库同步线程，同步间隔: 10秒
2025-07-28 12:10:07,708 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:10:07,708 - [MainThread] - INFO - 会话初始化完成，新会话，会话ID: 1
2025-07-28 12:10:07,708 - [DBSyncThread] - DEBUG - [全量同步] player_info 为空，跳过 comments_after_game 同步。
2025-07-28 12:10:07,724 - [MainThread] - INFO - 成功连接到移动服务 localhost:5556
2025-07-28 12:10:07,724 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已启动。
2025-07-28 12:10:07,724 - [MainThread] - INFO - 移动服务客户端已启动，连接到: localhost:5556
2025-07-28 12:10:07,724 - [DetectionReceiver] - INFO - 检测数据接收器启动
2025-07-28 12:10:07,724 - [MainThread] - INFO - 检测数据接收器线程已启动
2025-07-28 12:10:07,739 - [MainThread] - INFO - [虚拟玩家] 成功加载 785 个虚拟玩家，起始索引: 576
2025-07-28 12:10:07,739 - [MainThread] - INFO - [虚拟玩家] 管理器启动...
2025-07-28 12:10:07,739 - [MainThread] - INFO - 虚拟玩家管理器已启动
2025-07-28 12:10:07,739 - [MainThread] - INFO - 状态更新处理线程已启动
2025-07-28 12:10:07,739 - [DetectionReceiver] - INFO - 成功连接到检测服务器 localhost:5555
2025-07-28 12:10:07,739 - [DetectionReceiver] - DEBUG - 已发送订阅命令
2025-07-28 12:10:07,739 - [MainThread] - INFO - GUI的FPS发送线程已启动
2025-07-28 12:10:07,739 - [DetectionReceiver] - INFO - 收到订阅确认: Subscription successful for client 1
2025-07-28 12:10:07,739 - [StatusUpdateThread] - INFO - [重试机制] 检测到移动服务端已恢复，若有pending_retry_player将立即重试。
2025-07-28 12:10:07,739 - [MainThread] - INFO - 游戏处理线程已启动
2025-07-28 12:10:07,739 - [MainThread] - INFO - 主循环监控线程已启动
2025-07-28 12:10:07,739 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:10:07,739 - [MainThread] - INFO - [消息线程] 启动消息获取后台线程
2025-07-28 12:10:07,739 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:10:07,739 - [MessagePollThread] - DEBUG - [消息线程] 开始运行消息获取线程
2025-07-28 12:10:07,739 - [MainThread] - INFO - [消息线程] 消息获取线程已启动
2025-07-28 12:10:07,755 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:07,739 - [MessagePollThread] - DEBUG - [消息流] 启动健康检查
2025-07-28 12:10:07,755 - [MainThread] - INFO - 消息获取后台线程已启动，开始接收消息...
2025-07-28 12:10:07,771 - [MainThread] - INFO - 开始处理消息，按Ctrl+C停止...
2025-07-28 12:10:07,771 - [HealthCheckThread] - DEBUG - [健康检查] 任务开始
2025-07-28 12:10:07,771 - [MessagePollThread] - INFO - 健康检查线程已启动，间隔: 30秒
2025-07-28 12:10:07,771 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:10:07,771 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:10:07,771 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 7.8 秒
2025-07-28 12:10:07,771 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:10:07,771 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:10:07,771 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:10:07,771 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:10:07,771 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 1
2025-07-28 12:10:07,771 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:10:07,771 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:07,802 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:07,807 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:07,850 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:07,886 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:07,930 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:07,969 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,013 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,059 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,095 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,135 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,171 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,209 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,250 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,267 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:10:08,267 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:10:08,287 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,322 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,364 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,414 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,461 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,506 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,545 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,590 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,631 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,676 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,715 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,760 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,776 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:10:08,776 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:10:08,805 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,851 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,897 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,946 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:08,994 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,044 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,080 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,124 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,160 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,206 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,251 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,283 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:10:09,283 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:10:09,299 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,335 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,379 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,416 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,459 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,494 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,544 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,585 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,623 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,659 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,704 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,739 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,755 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(7)，添加虚拟玩家。
2025-07-28 12:10:09,756 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小酌几醉(0次)', '十里桃花(0次)', '花未眠(0次)', '奶萌小可爱(0次)', '秦疏月(0次)']。已选择: 小酌几醉
2025-07-28 12:10:09,758 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:09,798 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {}, pending_retry_player: None
2025-07-28 12:10:09,798 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:10:09,798 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小酌几醉(VirtualPlayerID0577) 确保抓中, z_offset_extra=0
2025-07-28 12:10:09,798 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=da7e73ce-21c2-4eec-bc40-ba94febc8eab, Player=小酌几醉, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:10:09,798 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 抓取指令已发送到移动服务，命令ID: da7e73ce-21c2-4eec-bc40-ba94febc8eab
2025-07-28 12:10:09,829 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,844 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC697F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 1
2025-07-28 12:10:09,846 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC697F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:09,847 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:09,860 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:10:09,864 - [MoveServiceEventReceiver] - INFO - 抓取指令 da7e73ce-21c2-4eec-bc40-ba94febc8eab 已被移动服务接受并加入队列
2025-07-28 12:10:09,877 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,917 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:09,961 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,007 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,053 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,099 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,138 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,185 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,229 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,245 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0577 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:10,247 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0577 订单状态，但该玩家在会话 1 中不存在
2025-07-28 12:10:10,276 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,307 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:10,307 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:10:10,323 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,372 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,411 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,456 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,501 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,548 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,592 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,639 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,674 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,722 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,768 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,807 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,823 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:10,824 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:10:10,854 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,900 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,936 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:10,983 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,029 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,069 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,115 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,161 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,206 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,243 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,285 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,323 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,326 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:11,326 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:10:11,366 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,408 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,458 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,502 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,540 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,582 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,618 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,658 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,696 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,741 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:10:11,772 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已完成游戏，总游戏次数: 1
2025-07-28 12:10:11,772 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 下次将以付费状态排队。
2025-07-28 12:10:11,775 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(4)，添加虚拟玩家。
2025-07-28 12:10:11,775 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['十里桃花(0次)', '花未眠(0次)', '奶萌小可爱(0次)', '秦疏月(0次)', '小酌几醉(1次)']。已选择: 十里桃花
2025-07-28 12:10:11,776 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:11,831 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:11,832 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:11,859 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:11,859 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:10:11,859 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:11,868 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:12,340 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:12,342 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:12,632 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:10:12,632 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=da7e73ce-21c2-4eec-bc40-ba94febc8eab
2025-07-28 12:10:12,642 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:10:12,643 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=5
2025-07-28 12:10:12,644 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:10:12,645 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab', 'player_id': 'VirtualPlayerID0577', 'player_name': '小酌几醉', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:10:12,645 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小酌几醉(VirtualPlayerID0577), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: da7e73ce-21c2-4eec-bc40-ba94febc8eab, 消息: 物品_5
2025-07-28 12:10:12,648 - [StatusUpdateThread] - INFO - 玩家 小酌几醉 本次是第 1 次成功抓取。
2025-07-28 12:10:12,655 - [StatusUpdateThread] - INFO - 为玩家 小酌几醉 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:10:12,657 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:10:12,657 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-28 12:10:12,661 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-28 12:10:12,854 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:12,855 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:13,371 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:13,371 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:13,791 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(1) < 目标(4)，添加虚拟玩家。
2025-07-28 12:10:13,791 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['花未眠(0次)', '奶萌小可爱(0次)', '秦疏月(0次)', '小酌几醉(1次)']。已选择: 花未眠
2025-07-28 12:10:13,795 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:13,890 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:13,890 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9EA90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 2
2025-07-28 12:10:13,890 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:13,890 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9EA90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:13,890 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:14,410 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:14,410 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:14,926 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:14,926 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:15,438 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:15,439 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:15,801 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(7)，添加虚拟玩家。
2025-07-28 12:10:15,802 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['奶萌小可爱(0次)', '秦疏月(0次)', '小酌几醉(1次)']。已选择: 奶萌小可爱
2025-07-28 12:10:15,804 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:15,899 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:15,901 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:10:15,901 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:15,901 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:15,946 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:15,947 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:16,454 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab'}, pending_retry_player: None
2025-07-28 12:10:16,455 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:16,735 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-28 12:10:16,736 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-28 12:10:16,893 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:10:16,918 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=da7e73ce-21c2-4eec-bc40-ba94febc8eab
2025-07-28 12:10:16,927 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:10:16,946 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=5
2025-07-28 12:10:16,960 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675809.7980947, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'da7e73ce-21c2-4eec-bc40-ba94febc8eab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:10:16,970 - [MoveServiceEventReceiver] - INFO - 移动服务请求 da7e73ce-21c2-4eec-bc40-ba94febc8eab (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:10:16,971 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:16,977 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小酌几醉(VirtualPlayerID0577)
2025-07-28 12:10:17,002 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:10:17,015 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小酌几醉(VirtualPlayerID0577), 结果: 物品_5, 订单: None
2025-07-28 12:10:17,027 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:10:17,031 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:10:17,403 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.021秒 - 玩家: VirtualPlayerID0577, 结果: 物品_5, 订单: NULL
2025-07-28 12:10:17,403 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0577] = 1
2025-07-28 12:10:17,496 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {}, pending_retry_player: None
2025-07-28 12:10:17,496 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:10:17,496 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 十里桃花(VirtualPlayerID0578) 确保抓中, z_offset_extra=0
2025-07-28 12:10:17,496 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6, Player=十里桃花, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:10:17,496 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 抓取指令已发送到移动服务，命令ID: f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6
2025-07-28 12:10:17,512 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:10:17,523 - [MoveServiceEventReceiver] - INFO - 抓取指令 f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6 已被移动服务接受并加入队列
2025-07-28 12:10:17,809 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已完成游戏，总游戏次数: 1
2025-07-28 12:10:17,809 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 下次将以付费状态排队。
2025-07-28 12:10:17,814 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(6)，添加虚拟玩家。
2025-07-28 12:10:17,817 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['秦疏月(0次)', '小酌几醉(1次)', '十里桃花(1次)']。已选择: 秦疏月
2025-07-28 12:10:17,818 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:17,918 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0578 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:17,921 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0578 订单状态，但该玩家在会话 1 中不存在
2025-07-28 12:10:17,937 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC78400>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 3
2025-07-28 12:10:17,943 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC78400>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:10:17,943 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:10:17,959 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:10:17,963 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:10:17,967 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=0.0s, 心跳间隔=1s
2025-07-28 12:10:17,983 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:10:18,024 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:18,024 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:18,047 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.015秒
2025-07-28 12:10:18,048 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:10:18,053 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-28 12:10:18,532 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:18,534 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:19,002 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:10:19,006 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:10:19,008 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:10:19,010 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:10:19,012 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:10:19,014 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:10:19,015 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 2
2025-07-28 12:10:19,018 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:10:19,026 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:19,034 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:19,051 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:19,053 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:19,570 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:19,571 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:19,837 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(3) < 目标(4)，添加虚拟玩家。
2025-07-28 12:10:19,837 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小酌几醉(1次)', '十里桃花(1次)']。已选择: 小酌几醉
2025-07-28 12:10:19,841 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:20,090 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:20,092 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:20,496 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:10:20,497 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6
2025-07-28 12:10:20,502 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:10:20,505 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0578, player_name=十里桃花, requested_object_id=3
2025-07-28 12:10:20,507 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:10:20,508 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6', 'player_id': 'VirtualPlayerID0578', 'player_name': '十里桃花', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:10:20,508 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 十里桃花(VirtualPlayerID0578), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6, 消息: 物品_3
2025-07-28 12:10:20,509 - [StatusUpdateThread] - INFO - 玩家 十里桃花 本次是第 1 次成功抓取。
2025-07-28 12:10:20,510 - [StatusUpdateThread] - INFO - 为玩家 十里桃花 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:10:20,511 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:10:20,521 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-28 12:10:20,522 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-28 12:10:20,604 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:20,605 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:21,045 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBC6A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 4
2025-07-28 12:10:21,049 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBC6A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:21,050 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:21,109 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:21,110 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:21,628 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:21,628 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:21,847 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-28 12:10:21,847 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['十里桃花(1次)']。已选择: 十里桃花
2025-07-28 12:10:21,850 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:22,147 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:22,148 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:22,661 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:22,662 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:23,055 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:23,057 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:10:23,059 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:23,064 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:23,168 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:23,168 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:23,681 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:23,681 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:23,856 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:10:23,857 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:24,199 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6'}, pending_retry_player: None
2025-07-28 12:10:24,200 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:24,590 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-28 12:10:24,591 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-28 12:10:24,718 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675817.4964314, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:24,722 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:24,954 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:10:24,954 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6
2025-07-28 12:10:24,959 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:10:24,964 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0578, player_name=十里桃花, requested_object_id=3
2025-07-28 12:10:24,973 - [MoveServiceEventReceiver] - INFO - 移动服务请求 f6961ac3-6ee6-490c-a737-0a3c7ce1ccf6 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:10:24,973 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 十里桃花(VirtualPlayerID0578)
2025-07-28 12:10:24,994 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:10:24,995 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 十里桃花(VirtualPlayerID0578), 结果: 物品_3, 订单: None
2025-07-28 12:10:24,997 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:10:24,998 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:10:25,094 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9EBB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 5
2025-07-28 12:10:25,097 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9EBB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:25,101 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:25,157 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.016秒 - 玩家: VirtualPlayerID0578, 结果: 物品_3, 订单: NULL
2025-07-28 12:10:25,161 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0578] = 1
2025-07-28 12:10:25,234 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:10:25,235 - [GameProcessThread] - INFO - [游戏线程] 玩家 花未眠(VirtualPlayerID0579) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:10:25,244 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 花未眠(VirtualPlayerID0579) 确保抓中, z_offset_extra=0
2025-07-28 12:10:25,247 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=cd0530dd-b93f-448a-a571-56aafaf2b71f, Player=花未眠, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:10:25,247 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:10:25,251 - [GameProcessThread] - INFO - [游戏线程] 玩家 花未眠(VirtualPlayerID0579) 抓取指令已发送到移动服务，命令ID: cd0530dd-b93f-448a-a571-56aafaf2b71f
2025-07-28 12:10:25,257 - [MoveServiceEventReceiver] - INFO - 抓取指令 cd0530dd-b93f-448a-a571-56aafaf2b71f 已被移动服务接受并加入队列
2025-07-28 12:10:25,687 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0579 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:25,690 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0579 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:25,767 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f'}, pending_retry_player: None
2025-07-28 12:10:25,768 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:25,861 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 已完成游戏，总游戏次数: 1
2025-07-28 12:10:25,861 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 下次将以付费状态排队。
2025-07-28 12:10:25,862 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:10:25,862 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['花未眠(1次)']。已选择: 花未眠
2025-07-28 12:10:25,863 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:26,287 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f'}, pending_retry_player: None
2025-07-28 12:10:26,288 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:26,792 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f'}, pending_retry_player: None
2025-07-28 12:10:26,792 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:27,107 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:27,109 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:10:27,110 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:27,116 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:27,298 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f'}, pending_retry_player: None
2025-07-28 12:10:27,299 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:27,814 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f'}, pending_retry_player: None
2025-07-28 12:10:27,815 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:28,211 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:10:28,212 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=cd0530dd-b93f-448a-a571-56aafaf2b71f
2025-07-28 12:10:28,220 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:10:28,222 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0579, player_name=花未眠, requested_object_id=4
2025-07-28 12:10:28,223 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:10:28,224 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'player_id': 'VirtualPlayerID0579', 'player_name': '花未眠', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:10:28,224 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 花未眠(VirtualPlayerID0579), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: cd0530dd-b93f-448a-a571-56aafaf2b71f, 消息: 物品_4
2025-07-28 12:10:28,226 - [StatusUpdateThread] - INFO - 玩家 花未眠 本次是第 1 次成功抓取。
2025-07-28 12:10:28,226 - [StatusUpdateThread] - INFO - 为玩家 花未眠 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:10:28,228 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:10:28,228 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-28 12:10:28,228 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-28 12:10:28,249 - [StatusUpdateThread] - INFO - Successfully identified ReqClient with the server using RPC version:1
2025-07-28 12:10:28,250 - [StatusUpdateThread] - INFO - 成功连接到OBS WebSocket
2025-07-28 12:10:28,266 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetCurrentProgramScene', 'requestId': 111}}
2025-07-28 12:10:28,276 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 111, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetCurrentProgramScene', 'responseData': {'currentProgramSceneName': '场景1'}}, 'op': 7}
2025-07-28 12:10:28,278 - [StatusUpdateThread] - INFO - 当前场景: 场景1
2025-07-28 12:10:28,278 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetSceneItemList', 'requestId': 257, 'requestData': {'sceneName': '场景1'}}}
2025-07-28 12:10:28,280 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.022秒
2025-07-28 12:10:28,280 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:10:28,286 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 257, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetSceneItemList', 'responseData': {'sceneItems': [{'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 58, 'sceneItemIndex': 0, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '没抓到特效', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'color_source_v3', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 49, 'sceneItemIndex': 1, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '色源', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'vlc_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 40, 'sceneItemIndex': 2, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 8, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 540.0, 'positionY': 1920.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '背景视频', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'image_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 62, 'sceneItemIndex': 3, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '临时图像', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'dshow_input', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 46, 'sceneItemIndex': 4, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1089.0, 'positionX': 1089.0, 'positionY': -16.0, 'rotation': 90.0, 'scaleX': 1.0083333253860474, 'scaleY': 1.0083333253860474, 'sourceHeight': 1080.0, 'sourceWidth': 1920.0, 'width': 1936.0}, 'sourceName': '主摄像头', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 1, 'sceneItemIndex': 5, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 116.22856903076172, 'positionX': 214.0, 'positionY': 44.77142333984375, 'rotation': 0.0, 'scaleX': 1.6139534711837769, 'scaleY': 1.6142857074737549, 'sourceHeight': 72.0, 'sourceWidth': 444.0, 'width': 716.5953369140625}, 'sourceName': '大标题', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 60, 'sceneItemIndex': 6, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 14.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4541666507720947, 'scaleY': 1.4546159505844116, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 53, 'sceneItemIndex': 7, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': -4.0, 'rotation': 0.0, 'scaleX': 1.5, 'scaleY': 1.5003879070281982, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效2', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 56, 'sceneItemIndex': 8, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 21.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4416667222976685, 'scaleY': 1.44142746925354, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效3', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 57, 'sceneItemIndex': 9, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 22.0, 'positionY': -163.0, 'rotation': 0.0, 'scaleX': 1.4375, 'scaleY': 1.4375485181808472, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效4', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 34, 'sceneItemIndex': 10, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 164.0, 'positionX': 454.0, 'positionY': 132.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 164.0, 'sourceWidth': 626.0, 'width': 626.0}, 'sourceName': '说明1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'monitor_capture', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 41, 'sceneItemIndex': 11, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 734.0, 'rotation': 0.0, 'scaleX': 0.6026041507720947, 'scaleY': 0.6027777791023254, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '显示器采集', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'browser_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 50, 'sceneItemIndex': 12, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '浏览器显示', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}]}}, 'op': 7}
2025-07-28 12:10:28,288 - [StatusUpdateThread] - INFO - 完成源缓存，已缓存 5 个源的场景项ID
2025-07-28 12:10:28,298 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 926, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:10:28,313 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 926, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:28,314 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 6 个玩家, 成功更新 6 条记录.
2025-07-28 12:10:28,330 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:28,332 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:28,849 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:28,857 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:29,149 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E9A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 6
2025-07-28 12:10:29,158 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E9A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:10:29,164 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:10:29,193 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:10:29,211 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:10:29,226 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.3s, 心跳间隔=1s
2025-07-28 12:10:29,264 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:10:29,292 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:10:29,302 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:10:29,371 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:29,372 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:29,916 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:29,916 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:30,339 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:10:30,350 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:10:30,352 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:10:30,353 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:10:30,354 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:10:30,355 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:10:30,361 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 3
2025-07-28 12:10:30,362 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:10:30,363 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:30,370 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:30,436 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:30,442 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:30,953 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:30,955 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:31,329 - [Thread-1] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 606, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:10:31,330 - [Thread-1] - DEBUG - Response received {'d': {'requestId': 606, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:31,475 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:31,475 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:31,896 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:10:31,896 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:31,992 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:31,992 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:32,387 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF42B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 7
2025-07-28 12:10:32,390 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF42B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:32,390 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:32,498 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675825.235929, 'target_id': '4', 'target_object': 'Hello Kitty', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:32,498 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:32,667 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cd0530dd-b93f-448a-a571-56aafaf2b71f', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:10:32,672 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=cd0530dd-b93f-448a-a571-56aafaf2b71f
2025-07-28 12:10:32,675 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:10:32,676 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0579, player_name=花未眠, requested_object_id=4
2025-07-28 12:10:32,677 - [MoveServiceEventReceiver] - INFO - 移动服务请求 cd0530dd-b93f-448a-a571-56aafaf2b71f (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:10:32,677 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 花未眠(VirtualPlayerID0579)
2025-07-28 12:10:32,678 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:10:32,678 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 花未眠(VirtualPlayerID0579), 结果: 物品_4, 订单: None
2025-07-28 12:10:32,679 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:10:32,679 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:10:32,931 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.030秒 - 玩家: VirtualPlayerID0579, 结果: 物品_4, 订单: NULL
2025-07-28 12:10:32,932 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0579] = 1
2025-07-28 12:10:33,012 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:10:33,012 - [GameProcessThread] - INFO - [游戏线程] 玩家 奶萌小可爱(VirtualPlayerID0580) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:10:33,015 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 奶萌小可爱(VirtualPlayerID0580) 确保抓中, z_offset_extra=0
2025-07-28 12:10:33,016 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=d14bf86c-333a-4133-98d0-2f7d5eaa838a, Player=奶萌小可爱, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:10:33,016 - [GameProcessThread] - INFO - [游戏线程] 玩家 奶萌小可爱(VirtualPlayerID0580) 抓取指令已发送到移动服务，命令ID: d14bf86c-333a-4133-98d0-2f7d5eaa838a
2025-07-28 12:10:33,019 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:10:33,020 - [MoveServiceEventReceiver] - INFO - 抓取指令 d14bf86c-333a-4133-98d0-2f7d5eaa838a 已被移动服务接受并加入队列
2025-07-28 12:10:33,468 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0580 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:33,476 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0580 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:33,530 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a'}, pending_retry_player: None
2025-07-28 12:10:33,530 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:33,911 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 已完成游戏，总游戏次数: 1
2025-07-28 12:10:33,911 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 下次将以付费状态排队。
2025-07-28 12:10:33,912 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:10:33,913 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['奶萌小可爱(1次)']。已选择: 奶萌小可爱
2025-07-28 12:10:33,914 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:34,038 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a'}, pending_retry_player: None
2025-07-28 12:10:34,040 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:34,396 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:34,400 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:10:34,407 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:34,412 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:34,570 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a'}, pending_retry_player: None
2025-07-28 12:10:34,576 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:35,086 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a'}, pending_retry_player: None
2025-07-28 12:10:35,086 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:35,602 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a'}, pending_retry_player: None
2025-07-28 12:10:35,602 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:36,031 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:10:36,032 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=d14bf86c-333a-4133-98d0-2f7d5eaa838a
2025-07-28 12:10:36,036 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:10:36,036 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0580, player_name=奶萌小可爱, requested_object_id=3
2025-07-28 12:10:36,039 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:10:36,048 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'player_id': 'VirtualPlayerID0580', 'player_name': '奶萌小可爱', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:10:36,048 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 奶萌小可爱(VirtualPlayerID0580), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: d14bf86c-333a-4133-98d0-2f7d5eaa838a, 消息: 物品_3
2025-07-28 12:10:36,051 - [StatusUpdateThread] - INFO - 玩家 奶萌小可爱 本次是第 1 次成功抓取。
2025-07-28 12:10:36,052 - [StatusUpdateThread] - INFO - 为玩家 奶萌小可爱 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:10:36,053 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:10:36,053 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 582, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:10:36,054 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 582, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:36,126 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:36,126 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:36,444 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF49D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 8
2025-07-28 12:10:36,447 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF49D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:36,451 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:36,646 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:36,646 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:37,162 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:37,163 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:37,669 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:37,671 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:37,793 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:10:37,797 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:10:37,807 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 37.8 秒
2025-07-28 12:10:37,808 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:10:38,184 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:38,185 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:38,464 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:38,471 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:10:38,473 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:38,477 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:38,570 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:10:38,583 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:10:38,705 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:38,707 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:39,067 - [Thread-2] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 641, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:10:39,071 - [Thread-2] - DEBUG - Response received {'d': {'requestId': 641, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:39,211 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:39,212 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:39,725 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:39,725 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:40,246 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': **********.0124083, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:40,246 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:40,494 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E910>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 9
2025-07-28 12:10:40,499 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E910>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:10:40,516 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:10:40,521 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:10:40,522 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:10:40,524 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.3s, 心跳间隔=1s
2025-07-28 12:10:40,525 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:10:40,529 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:10:40,532 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:10:40,558 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'd14bf86c-333a-4133-98d0-2f7d5eaa838a', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:10:40,559 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=d14bf86c-333a-4133-98d0-2f7d5eaa838a
2025-07-28 12:10:40,563 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:10:40,566 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0580, player_name=奶萌小可爱, requested_object_id=3
2025-07-28 12:10:40,569 - [MoveServiceEventReceiver] - INFO - 移动服务请求 d14bf86c-333a-4133-98d0-2f7d5eaa838a (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:10:40,569 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 奶萌小可爱(VirtualPlayerID0580)
2025-07-28 12:10:40,580 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:10:40,581 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 奶萌小可爱(VirtualPlayerID0580), 结果: 物品_3, 订单: None
2025-07-28 12:10:40,582 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:10:40,584 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:10:40,643 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.024秒 - 玩家: VirtualPlayerID0580, 结果: 物品_3, 订单: NULL
2025-07-28 12:10:40,644 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0580] = 1
2025-07-28 12:10:40,758 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:10:40,758 - [GameProcessThread] - INFO - [游戏线程] 玩家 秦疏月(VirtualPlayerID0581) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:10:40,760 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 秦疏月(VirtualPlayerID0581) 确保抓中, z_offset_extra=0
2025-07-28 12:10:40,761 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=48ac7b6d-e4d6-4748-9309-0c0678e718c1, Player=秦疏月, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:10:40,761 - [GameProcessThread] - INFO - [游戏线程] 玩家 秦疏月(VirtualPlayerID0581) 抓取指令已发送到移动服务，命令ID: 48ac7b6d-e4d6-4748-9309-0c0678e718c1
2025-07-28 12:10:40,763 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:10:40,765 - [MoveServiceEventReceiver] - INFO - 抓取指令 48ac7b6d-e4d6-4748-9309-0c0678e718c1 已被移动服务接受并加入队列
2025-07-28 12:10:41,166 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0581 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:41,175 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0581 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:10:41,278 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1'}, pending_retry_player: None
2025-07-28 12:10:41,278 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:41,546 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:10:41,549 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:10:41,550 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:10:41,553 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:10:41,556 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:10:41,559 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:10:41,561 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 4
2025-07-28 12:10:41,562 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:10:41,567 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:41,576 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:41,798 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1'}, pending_retry_player: None
2025-07-28 12:10:41,799 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:41,958 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 已完成游戏，总游戏次数: 1
2025-07-28 12:10:41,964 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 下次将以付费状态排队。
2025-07-28 12:10:42,319 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1'}, pending_retry_player: None
2025-07-28 12:10:42,319 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:42,843 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1'}, pending_retry_player: None
2025-07-28 12:10:42,844 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:43,360 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1'}, pending_retry_player: None
2025-07-28 12:10:43,360 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:43,626 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC78D30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 10
2025-07-28 12:10:43,628 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC78D30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:43,630 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:43,787 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:10:43,787 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=48ac7b6d-e4d6-4748-9309-0c0678e718c1
2025-07-28 12:10:43,791 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:10:43,792 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0581, player_name=秦疏月, requested_object_id=3
2025-07-28 12:10:43,794 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:10:43,796 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'player_id': 'VirtualPlayerID0581', 'player_name': '秦疏月', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:10:43,796 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 秦疏月(VirtualPlayerID0581), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 48ac7b6d-e4d6-4748-9309-0c0678e718c1, 消息: 物品_3
2025-07-28 12:10:43,798 - [StatusUpdateThread] - INFO - 玩家 秦疏月 本次是第 1 次成功抓取。
2025-07-28 12:10:43,799 - [StatusUpdateThread] - INFO - 为玩家 秦疏月 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:10:43,811 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:10:43,818 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 994, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:10:43,820 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 994, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:43,882 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:43,883 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:44,398 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:44,399 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:44,915 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:44,916 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:45,431 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:45,431 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:45,649 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:45,652 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:10:45,654 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:45,661 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:45,948 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:45,949 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:46,467 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:46,467 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:46,827 - [Thread-3] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 864, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:10:46,828 - [Thread-3] - DEBUG - Response received {'d': {'requestId': 864, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:46,984 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:46,984 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:47,490 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:47,492 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:47,695 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4D00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 11
2025-07-28 12:10:47,698 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4D00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:47,699 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:48,009 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675840.7588754, 'target_id': '3', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:10:48,014 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:48,309 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '48ac7b6d-e4d6-4748-9309-0c0678e718c1', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:10:48,309 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=48ac7b6d-e4d6-4748-9309-0c0678e718c1
2025-07-28 12:10:48,313 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:10:48,313 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0581, player_name=秦疏月, requested_object_id=3
2025-07-28 12:10:48,315 - [MoveServiceEventReceiver] - INFO - 移动服务请求 48ac7b6d-e4d6-4748-9309-0c0678e718c1 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:10:48,315 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 秦疏月(VirtualPlayerID0581)
2025-07-28 12:10:48,317 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:10:48,317 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 秦疏月(VirtualPlayerID0581), 结果: 物品_3, 订单: None
2025-07-28 12:10:48,319 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:10:48,319 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:10:48,528 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:10:48,528 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:10:48,532 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小酌几醉(VirtualPlayerID0577) 确保抓中, z_offset_extra=0
2025-07-28 12:10:48,533 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=7962953b-f62e-42a3-979e-245b2bc81b2d, Player=小酌几醉, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:10:48,534 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 抓取指令已发送到移动服务，命令ID: 7962953b-f62e-42a3-979e-245b2bc81b2d
2025-07-28 12:10:48,534 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:10:48,539 - [MoveServiceEventReceiver] - INFO - 抓取指令 7962953b-f62e-42a3-979e-245b2bc81b2d 已被移动服务接受并加入队列
2025-07-28 12:10:48,793 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.013秒 - 玩家: VirtualPlayerID0581, 结果: 物品_3, 订单: NULL
2025-07-28 12:10:48,793 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0581] = 1
2025-07-28 12:10:48,809 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:10:48,815 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:10:49,047 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d'}, pending_retry_player: None
2025-07-28 12:10:49,047 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:49,553 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d'}, pending_retry_player: None
2025-07-28 12:10:49,554 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:49,710 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:49,712 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:10:49,714 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:49,719 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:50,009 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已完成游戏，总游戏次数: 2
2025-07-28 12:10:50,056 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d'}, pending_retry_player: None
2025-07-28 12:10:50,056 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:50,560 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d'}, pending_retry_player: None
2025-07-28 12:10:50,560 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:51,084 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d'}, pending_retry_player: None
2025-07-28 12:10:51,084 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:51,572 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:10:51,575 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=7962953b-f62e-42a3-979e-245b2bc81b2d
2025-07-28 12:10:51,585 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:10:51,589 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=4
2025-07-28 12:10:51,592 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:10:51,593 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'player_id': 'VirtualPlayerID0577', 'player_name': '小酌几醉', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:10:51,593 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小酌几醉(VirtualPlayerID0577), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: 7962953b-f62e-42a3-979e-245b2bc81b2d, 消息: 物品_4
2025-07-28 12:10:51,594 - [StatusUpdateThread] - INFO - 玩家 小酌几醉 本次是第 2 次成功抓取。
2025-07-28 12:10:51,595 - [StatusUpdateThread] - INFO - 为玩家 小酌几醉 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:10:51,596 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:10:51,596 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 775, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:10:51,599 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 775, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:51,603 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:51,608 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:51,743 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4E80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 12
2025-07-28 12:10:51,751 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4E80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:10:51,754 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:10:51,760 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:10:51,762 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:10:51,765 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:10:51,766 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:10:51,768 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:10:51,769 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:10:52,122 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:52,122 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:52,641 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:52,646 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:52,781 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:10:52,785 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:10:52,786 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:10:52,788 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:10:52,789 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:10:52,791 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:10:52,792 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 5
2025-07-28 12:10:52,793 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:10:52,795 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:52,805 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:53,162 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:53,162 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:53,677 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:53,678 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:54,024 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:10:54,025 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['秦疏月(1次)', '小酌几醉(2次)']。已选择: 秦疏月
2025-07-28 12:10:54,030 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:10:54,197 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:54,198 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:54,612 - [Thread-4] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 638, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:10:54,615 - [Thread-4] - DEBUG - Response received {'d': {'requestId': 638, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:54,704 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:54,706 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:54,844 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDACB20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 13
2025-07-28 12:10:54,849 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDACB20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:54,851 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:55,219 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:55,220 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:55,737 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675848.5286548, 'target_id': '4', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:10:55,739 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:56,037 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '7962953b-f62e-42a3-979e-245b2bc81b2d', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:10:56,038 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=7962953b-f62e-42a3-979e-245b2bc81b2d
2025-07-28 12:10:56,048 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:10:56,055 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=4
2025-07-28 12:10:56,062 - [MoveServiceEventReceiver] - INFO - 移动服务请求 7962953b-f62e-42a3-979e-245b2bc81b2d (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:10:56,062 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小酌几醉(VirtualPlayerID0577)
2025-07-28 12:10:56,066 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:10:56,069 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小酌几醉(VirtualPlayerID0577), 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:10:56,073 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:10:56,073 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:10:56,259 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:10:56,260 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:10:56,262 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 十里桃花(VirtualPlayerID0578) 确保抓中, z_offset_extra=0
2025-07-28 12:10:56,262 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=b65d452d-4036-4e93-91ed-cefa22e69b01, Player=十里桃花, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:10:56,263 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 抓取指令已发送到移动服务，命令ID: b65d452d-4036-4e93-91ed-cefa22e69b01
2025-07-28 12:10:56,265 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:10:56,265 - [MoveServiceEventReceiver] - INFO - 抓取指令 b65d452d-4036-4e93-91ed-cefa22e69b01 已被移动服务接受并加入队列
2025-07-28 12:10:56,418 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.021秒 - 玩家: VirtualPlayerID0577, 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:10:56,419 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0577] = 2
2025-07-28 12:10:56,779 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01'}, pending_retry_player: None
2025-07-28 12:10:56,780 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:56,857 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:10:56,859 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:10:56,860 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:10:56,866 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:10:57,285 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01'}, pending_retry_player: None
2025-07-28 12:10:57,285 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:57,804 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01'}, pending_retry_player: None
2025-07-28 12:10:57,806 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:58,054 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已完成游戏，总游戏次数: 2
2025-07-28 12:10:58,323 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01'}, pending_retry_player: None
2025-07-28 12:10:58,323 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:58,831 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01'}, pending_retry_player: None
2025-07-28 12:10:58,831 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:58,893 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC78280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 14
2025-07-28 12:10:58,896 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC78280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:10:58,908 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:10:58,990 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.017秒
2025-07-28 12:10:58,990 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:10:59,020 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:10:59,224 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:10:59,225 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=b65d452d-4036-4e93-91ed-cefa22e69b01
2025-07-28 12:10:59,228 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:10:59,229 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0578, player_name=十里桃花, requested_object_id=5
2025-07-28 12:10:59,230 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:10:59,231 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'player_id': 'VirtualPlayerID0578', 'player_name': '十里桃花', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:10:59,231 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 十里桃花(VirtualPlayerID0578), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: b65d452d-4036-4e93-91ed-cefa22e69b01, 消息: 物品_5
2025-07-28 12:10:59,233 - [StatusUpdateThread] - INFO - 玩家 十里桃花 本次是第 2 次成功抓取。
2025-07-28 12:10:59,233 - [StatusUpdateThread] - INFO - 为玩家 十里桃花 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:10:59,235 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:10:59,235 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 534, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:10:59,237 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 534, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:10:59,350 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:10:59,350 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:10:59,856 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:10:59,857 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:00,377 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:00,380 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:00,896 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:00,897 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:00,927 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:00,929 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:11:00,930 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:00,933 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:01,414 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:01,415 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:01,920 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:01,921 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:02,253 - [Thread-5] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 110, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:11:02,254 - [Thread-5] - DEBUG - Response received {'d': {'requestId': 110, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:02,425 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:02,426 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:02,944 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:02,945 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:02,959 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBC610>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 15
2025-07-28 12:11:02,960 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBC610>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:02,962 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:11:02,963 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:11:02,965 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:11:02,967 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:11:02,970 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:11:02,972 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:11:02,976 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:11:03,460 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675856.2605994, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:03,461 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:03,762 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'b65d452d-4036-4e93-91ed-cefa22e69b01', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:03,763 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=b65d452d-4036-4e93-91ed-cefa22e69b01
2025-07-28 12:11:03,766 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:03,766 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0578, player_name=十里桃花, requested_object_id=5
2025-07-28 12:11:03,767 - [MoveServiceEventReceiver] - INFO - 移动服务请求 b65d452d-4036-4e93-91ed-cefa22e69b01 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:03,767 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 十里桃花(VirtualPlayerID0578)
2025-07-28 12:11:03,767 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:11:03,768 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 十里桃花(VirtualPlayerID0578), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:11:03,769 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:03,769 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:03,967 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:11:03,968 - [GameProcessThread] - INFO - [游戏线程] 玩家 小熊软糖罐🐻(VirtualPlayerID0582) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:11:03,970 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小熊软糖罐🐻(VirtualPlayerID0582) 确保抓中, z_offset_extra=0
2025-07-28 12:11:03,971 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=e1808c0f-49d0-4e41-bf55-c27604484dd0, Player=小熊软糖罐🐻, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:11:03,971 - [GameProcessThread] - INFO - [游戏线程] 玩家 小熊软糖罐🐻(VirtualPlayerID0582) 抓取指令已发送到移动服务，命令ID: e1808c0f-49d0-4e41-bf55-c27604484dd0
2025-07-28 12:11:03,972 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:11:03,973 - [MoveServiceEventReceiver] - INFO - 抓取指令 e1808c0f-49d0-4e41-bf55-c27604484dd0 已被移动服务接受并加入队列
2025-07-28 12:11:03,982 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:11:03,995 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:11:04,002 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:11:04,005 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:11:04,006 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:11:04,010 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:11:04,012 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 6
2025-07-28 12:11:04,017 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:11:04,018 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:04,020 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:04,076 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 已完成游戏，总游戏次数: 1
2025-07-28 12:11:04,076 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 下次将以付费状态排队。
2025-07-28 12:11:04,080 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(6)，添加虚拟玩家。
2025-07-28 12:11:04,080 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小熊软糖罐🐻(1次)', '小酌几醉(2次)', '十里桃花(2次)']。已选择: 小熊软糖罐🐻
2025-07-28 12:11:04,081 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:04,090 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.014秒 - 玩家: VirtualPlayerID0578, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:11:04,090 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0578] = 2
2025-07-28 12:11:04,091 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0582 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:11:04,093 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0582 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:11:04,483 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0'}, pending_retry_player: None
2025-07-28 12:11:04,484 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:04,991 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0'}, pending_retry_player: None
2025-07-28 12:11:04,991 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:05,496 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0'}, pending_retry_player: None
2025-07-28 12:11:05,497 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:06,017 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0'}, pending_retry_player: None
2025-07-28 12:11:06,018 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:06,051 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBCD00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 16
2025-07-28 12:11:06,054 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBCD00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:06,055 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:06,535 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0'}, pending_retry_player: None
2025-07-28 12:11:06,536 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:06,929 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:11:06,929 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=e1808c0f-49d0-4e41-bf55-c27604484dd0
2025-07-28 12:11:06,936 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:11:06,940 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0582, player_name=小熊软糖罐🐻, requested_object_id=1
2025-07-28 12:11:06,942 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:11:06,945 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'player_id': 'VirtualPlayerID0582', 'player_name': '小熊软糖罐🐻', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:11:06,947 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小熊软糖罐🐻(VirtualPlayerID0582), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: e1808c0f-49d0-4e41-bf55-c27604484dd0, 消息: 物品_1
2025-07-28 12:11:06,947 - [StatusUpdateThread] - INFO - 玩家 小熊软糖罐🐻 本次是第 1 次成功抓取。
2025-07-28 12:11:06,948 - [StatusUpdateThread] - INFO - 为玩家 小熊软糖罐🐻 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:11:06,949 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:11:06,950 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 788, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:11:06,951 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 788, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:07,053 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:07,053 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:07,572 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:07,573 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:07,842 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:11:07,846 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:11:07,846 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 67.9 秒
2025-07-28 12:11:07,848 - [HealthCheckThread] - WARNING - 已经 67.9 秒没有成功请求，尝试恢复连接...
2025-07-28 12:11:07,850 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:11:07,857 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:08,060 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:08,062 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:11:08,063 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:08,068 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:08,093 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:08,093 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:08,108 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:11:08,110 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小酌几醉(2次)', '十里桃花(2次)']。已选择: 小酌几醉
2025-07-28 12:11:08,111 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:08,599 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:08,600 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:09,114 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:09,115 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:09,161 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:11:09,185 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:11:09,635 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:09,640 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:09,898 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF46D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:09,905 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:11:09,966 - [Thread-6] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 466, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:11:09,968 - [Thread-6] - DEBUG - Response received {'d': {'requestId': 466, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:10,108 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69D00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 17
2025-07-28 12:11:10,113 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69D00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:10,114 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:10,157 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:10,158 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:10,677 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:10,678 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:11,197 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.9686487, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:11,198 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:11,466 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'e1808c0f-49d0-4e41-bf55-c27604484dd0', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:11,467 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=e1808c0f-49d0-4e41-bf55-c27604484dd0
2025-07-28 12:11:11,474 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:11,475 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0582, player_name=小熊软糖罐🐻, requested_object_id=1
2025-07-28 12:11:11,475 - [MoveServiceEventReceiver] - INFO - 移动服务请求 e1808c0f-49d0-4e41-bf55-c27604484dd0 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:11,475 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小熊软糖罐🐻(VirtualPlayerID0582)
2025-07-28 12:11:11,483 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:11:11,484 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小熊软糖罐🐻(VirtualPlayerID0582), 结果: 物品_1, 订单: None
2025-07-28 12:11:11,485 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:11,486 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:11,716 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:11:11,717 - [GameProcessThread] - INFO - [游戏线程] 玩家 花未眠(VirtualPlayerID0579) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:11:11,718 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 花未眠(VirtualPlayerID0579) 确保抓中, z_offset_extra=0
2025-07-28 12:11:11,720 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=9101b6b3-4e7b-45fd-b115-66250c9438b7, Player=花未眠, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:11:11,720 - [GameProcessThread] - INFO - [游戏线程] 玩家 花未眠(VirtualPlayerID0579) 抓取指令已发送到移动服务，命令ID: 9101b6b3-4e7b-45fd-b115-66250c9438b7
2025-07-28 12:11:11,720 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:11:11,722 - [MoveServiceEventReceiver] - INFO - 抓取指令 9101b6b3-4e7b-45fd-b115-66250c9438b7 已被移动服务接受并加入队列
2025-07-28 12:11:11,731 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.016秒 - 玩家: VirtualPlayerID0582, 结果: 物品_1, 订单: NULL
2025-07-28 12:11:11,736 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0582] = 1
2025-07-28 12:11:12,126 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 已完成游戏，总游戏次数: 2
2025-07-28 12:11:12,126 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:12,129 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:11:12,132 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:12,137 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:12,238 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7'}, pending_retry_player: None
2025-07-28 12:11:12,238 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:12,755 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7'}, pending_retry_player: None
2025-07-28 12:11:12,756 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:13,263 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7'}, pending_retry_player: None
2025-07-28 12:11:13,263 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:13,782 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7'}, pending_retry_player: None
2025-07-28 12:11:13,783 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:14,161 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDACB50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 18
2025-07-28 12:11:14,167 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDACB50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:14,169 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:11:14,170 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:11:14,171 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:11:14,171 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:11:14,172 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:11:14,173 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:11:14,174 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:11:14,301 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7'}, pending_retry_player: None
2025-07-28 12:11:14,301 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:14,744 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:11:14,745 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=9101b6b3-4e7b-45fd-b115-66250c9438b7
2025-07-28 12:11:14,746 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:11:14,747 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0579, player_name=花未眠, requested_object_id=1
2025-07-28 12:11:14,747 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:11:14,747 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'player_id': 'VirtualPlayerID0579', 'player_name': '花未眠', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:11:14,748 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 花未眠(VirtualPlayerID0579), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 9101b6b3-4e7b-45fd-b115-66250c9438b7, 消息: 物品_1
2025-07-28 12:11:14,748 - [StatusUpdateThread] - INFO - 玩家 花未眠 本次是第 2 次成功抓取。
2025-07-28 12:11:14,749 - [StatusUpdateThread] - INFO - 为玩家 花未眠 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:11:14,749 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:11:14,749 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 371, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:11:14,751 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 371, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:14,821 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:14,822 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:15,184 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:11:15,186 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:11:15,188 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:11:15,188 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:11:15,190 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:11:15,191 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:11:15,193 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 7
2025-07-28 12:11:15,194 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:11:15,196 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:15,205 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:15,342 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:15,342 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:15,862 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:15,862 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:16,365 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:16,365 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:16,887 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:16,887 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:17,234 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69940>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 19
2025-07-28 12:11:17,238 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69940>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:17,240 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:17,393 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:17,394 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:17,756 - [Thread-7] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 688, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:11:17,758 - [Thread-7] - DEBUG - Response received {'d': {'requestId': 688, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:17,900 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:17,901 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:18,415 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:18,415 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:18,936 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': 1753675871.71729, 'target_id': '1', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:18,939 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:19,218 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '9101b6b3-4e7b-45fd-b115-66250c9438b7', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:19,219 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=9101b6b3-4e7b-45fd-b115-66250c9438b7
2025-07-28 12:11:19,223 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:19,224 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0579, player_name=花未眠, requested_object_id=1
2025-07-28 12:11:19,225 - [MoveServiceEventReceiver] - INFO - 移动服务请求 9101b6b3-4e7b-45fd-b115-66250c9438b7 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:19,225 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 花未眠(VirtualPlayerID0579)
2025-07-28 12:11:19,226 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:11:19,227 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 花未眠(VirtualPlayerID0579), 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:11:19,228 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:19,228 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:19,249 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:19,252 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:11:19,253 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:19,256 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:19,344 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.016秒 - 玩家: VirtualPlayerID0579, 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:11:19,344 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0579] = 2
2025-07-28 12:11:19,351 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:11:19,357 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:11:19,456 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:11:19,456 - [GameProcessThread] - INFO - [游戏线程] 玩家 氧气柠檬(VirtualPlayerID0583) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:11:19,459 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 氧气柠檬(VirtualPlayerID0583) 确保抓中, z_offset_extra=0
2025-07-28 12:11:19,461 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=498c7cc4-5329-4dc8-a610-4cae6a18e239, Player=氧气柠檬, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:11:19,462 - [GameProcessThread] - INFO - [游戏线程] 玩家 氧气柠檬(VirtualPlayerID0583) 抓取指令已发送到移动服务，命令ID: 498c7cc4-5329-4dc8-a610-4cae6a18e239
2025-07-28 12:11:19,464 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:11:19,465 - [MoveServiceEventReceiver] - INFO - 抓取指令 498c7cc4-5329-4dc8-a610-4cae6a18e239 已被移动服务接受并加入队列
2025-07-28 12:11:19,866 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0583 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:11:19,869 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0583 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:11:19,976 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239'}, pending_retry_player: None
2025-07-28 12:11:19,976 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:20,165 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 已完成游戏，总游戏次数: 1
2025-07-28 12:11:20,165 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 下次将以付费状态排队。
2025-07-28 12:11:20,172 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-28 12:11:20,172 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['氧气柠檬(1次)', '十里桃花(2次)', '花未眠(2次)']。已选择: 氧气柠檬
2025-07-28 12:11:20,173 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:20,484 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239'}, pending_retry_player: None
2025-07-28 12:11:20,484 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:20,992 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239'}, pending_retry_player: None
2025-07-28 12:11:20,993 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:21,296 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBC730>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 20
2025-07-28 12:11:21,300 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBC730>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:21,301 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:21,512 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239'}, pending_retry_player: None
2025-07-28 12:11:21,513 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:22,027 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239'}, pending_retry_player: None
2025-07-28 12:11:22,028 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:22,183 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:11:22,183 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['十里桃花(2次)', '花未眠(2次)']。已选择: 十里桃花
2025-07-28 12:11:22,185 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:22,500 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:11:22,501 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=498c7cc4-5329-4dc8-a610-4cae6a18e239
2025-07-28 12:11:22,503 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:11:22,503 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0583, player_name=氧气柠檬, requested_object_id=1
2025-07-28 12:11:22,504 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:11:22,504 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'player_id': 'VirtualPlayerID0583', 'player_name': '氧气柠檬', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:11:22,504 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 氧气柠檬(VirtualPlayerID0583), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 498c7cc4-5329-4dc8-a610-4cae6a18e239, 消息: 物品_1
2025-07-28 12:11:22,505 - [StatusUpdateThread] - INFO - 玩家 氧气柠檬 本次是第 1 次成功抓取。
2025-07-28 12:11:22,506 - [StatusUpdateThread] - INFO - 为玩家 氧气柠檬 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:11:22,507 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:11:22,507 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 891, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:11:22,509 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 891, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:22,547 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:22,549 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:23,052 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:23,056 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:23,317 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:23,321 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:11:23,327 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:23,340 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:23,568 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:23,568 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:24,086 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:24,086 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:24,607 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:24,607 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:25,126 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:25,127 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:25,381 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 21
2025-07-28 12:11:25,386 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:25,389 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:11:25,390 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:11:25,391 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:11:25,392 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:11:25,402 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:11:25,406 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:11:25,411 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:11:25,520 - [Thread-8] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 290, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:11:25,522 - [Thread-8] - DEBUG - Response received {'d': {'requestId': 290, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:25,644 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:25,644 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:26,165 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:26,165 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:26,418 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:11:26,420 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:11:26,421 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:11:26,421 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:11:26,422 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:11:26,423 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:11:26,424 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 8
2025-07-28 12:11:26,425 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:11:26,425 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:26,428 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:26,672 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675879.456208, 'target_id': '1', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:11:26,673 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:27,035 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '498c7cc4-5329-4dc8-a610-4cae6a18e239', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:27,039 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=498c7cc4-5329-4dc8-a610-4cae6a18e239
2025-07-28 12:11:27,041 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:27,042 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0583, player_name=氧气柠檬, requested_object_id=1
2025-07-28 12:11:27,043 - [MoveServiceEventReceiver] - INFO - 移动服务请求 498c7cc4-5329-4dc8-a610-4cae6a18e239 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:27,043 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 氧气柠檬(VirtualPlayerID0583)
2025-07-28 12:11:27,044 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:11:27,044 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 氧气柠檬(VirtualPlayerID0583), 结果: 物品_1, 订单: None
2025-07-28 12:11:27,045 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:27,045 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:27,192 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:11:27,192 - [GameProcessThread] - INFO - [游戏线程] 玩家 奶萌小可爱(VirtualPlayerID0580) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:11:27,194 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 奶萌小可爱(VirtualPlayerID0580) 确保抓中, z_offset_extra=0
2025-07-28 12:11:27,195 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=7303bbec-afdc-4ea3-8de7-70df897a189c, Player=奶萌小可爱, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:11:27,195 - [GameProcessThread] - INFO - [游戏线程] 玩家 奶萌小可爱(VirtualPlayerID0580) 抓取指令已发送到移动服务，命令ID: 7303bbec-afdc-4ea3-8de7-70df897a189c
2025-07-28 12:11:27,197 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:11:27,198 - [MoveServiceEventReceiver] - INFO - 抓取指令 7303bbec-afdc-4ea3-8de7-70df897a189c 已被移动服务接受并加入队列
2025-07-28 12:11:27,479 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.019秒 - 玩家: VirtualPlayerID0583, 结果: 物品_1, 订单: NULL
2025-07-28 12:11:27,479 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0583] = 1
2025-07-28 12:11:27,711 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c'}, pending_retry_player: None
2025-07-28 12:11:27,711 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:28,217 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 已完成游戏，总游戏次数: 2
2025-07-28 12:11:28,217 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:11:28,218 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['花未眠(2次)', '奶萌小可爱(2次)']。已选择: 花未眠
2025-07-28 12:11:28,219 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:28,233 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c'}, pending_retry_player: None
2025-07-28 12:11:28,234 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:28,451 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC7F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 22
2025-07-28 12:11:28,454 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC7F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:28,455 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:28,750 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c'}, pending_retry_player: None
2025-07-28 12:11:28,750 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:29,257 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c'}, pending_retry_player: None
2025-07-28 12:11:29,258 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:29,524 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:11:29,561 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:11:29,760 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c'}, pending_retry_player: None
2025-07-28 12:11:29,760 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:30,215 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:11:30,216 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=7303bbec-afdc-4ea3-8de7-70df897a189c
2025-07-28 12:11:30,218 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:11:30,219 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0580, player_name=奶萌小可爱, requested_object_id=5
2025-07-28 12:11:30,220 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:11:30,222 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'player_id': 'VirtualPlayerID0580', 'player_name': '奶萌小可爱', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:11:30,222 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 奶萌小可爱(VirtualPlayerID0580), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: 7303bbec-afdc-4ea3-8de7-70df897a189c, 消息: 物品_5
2025-07-28 12:11:30,223 - [StatusUpdateThread] - INFO - 玩家 奶萌小可爱 本次是第 2 次成功抓取。
2025-07-28 12:11:30,227 - [StatusUpdateThread] - INFO - 为玩家 奶萌小可爱 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:11:30,229 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:11:30,231 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:11:30,238 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['奶萌小可爱(2次)']。已选择: 奶萌小可爱
2025-07-28 12:11:30,241 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 87, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:11:30,243 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:30,245 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 87, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:30,278 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:30,278 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:30,465 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:30,469 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:11:30,470 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:30,473 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:30,797 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:30,797 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:31,315 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:31,315 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:31,835 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:31,837 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:32,350 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:32,353 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:32,509 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 23
2025-07-28 12:11:32,512 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:32,513 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:32,867 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:32,867 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:33,262 - [Thread-9] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 704, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:11:33,265 - [Thread-9] - DEBUG - Response received {'d': {'requestId': 704, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:33,387 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:33,387 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:33,892 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:33,893 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:34,401 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675887.192133, 'target_id': '5', 'target_object': '小黄鸭', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:34,403 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:34,526 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:34,529 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:11:34,532 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:34,542 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:34,747 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '7303bbec-afdc-4ea3-8de7-70df897a189c', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:34,747 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=7303bbec-afdc-4ea3-8de7-70df897a189c
2025-07-28 12:11:34,750 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:34,751 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0580, player_name=奶萌小可爱, requested_object_id=5
2025-07-28 12:11:34,752 - [MoveServiceEventReceiver] - INFO - 移动服务请求 7303bbec-afdc-4ea3-8de7-70df897a189c (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:34,752 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 奶萌小可爱(VirtualPlayerID0580)
2025-07-28 12:11:34,756 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:11:34,756 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 奶萌小可爱(VirtualPlayerID0580), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:11:34,757 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:34,757 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:34,920 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:11:34,921 - [GameProcessThread] - INFO - [游戏线程] 玩家 秦疏月(VirtualPlayerID0581) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:11:34,922 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 秦疏月(VirtualPlayerID0581) 确保抓中, z_offset_extra=0
2025-07-28 12:11:34,923 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=eb4daac7-805c-4ed1-9171-5416d8eb76ea, Player=秦疏月, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:11:34,923 - [GameProcessThread] - INFO - [游戏线程] 玩家 秦疏月(VirtualPlayerID0581) 抓取指令已发送到移动服务，命令ID: eb4daac7-805c-4ed1-9171-5416d8eb76ea
2025-07-28 12:11:34,926 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:11:34,926 - [MoveServiceEventReceiver] - INFO - 抓取指令 eb4daac7-805c-4ed1-9171-5416d8eb76ea 已被移动服务接受并加入队列
2025-07-28 12:11:35,138 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.013秒 - 玩家: VirtualPlayerID0580, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:11:35,143 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0580] = 2
2025-07-28 12:11:35,425 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea'}, pending_retry_player: None
2025-07-28 12:11:35,425 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:35,932 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea'}, pending_retry_player: None
2025-07-28 12:11:35,933 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:36,263 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 已完成游戏，总游戏次数: 2
2025-07-28 12:11:36,449 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea'}, pending_retry_player: None
2025-07-28 12:11:36,449 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:36,579 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF48B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 24
2025-07-28 12:11:36,582 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF48B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:36,583 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:11:36,585 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:11:36,586 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:11:36,587 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:11:36,588 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:11:36,590 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:11:36,592 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:11:36,954 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea'}, pending_retry_player: None
2025-07-28 12:11:36,955 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:37,473 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea'}, pending_retry_player: None
2025-07-28 12:11:37,475 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:37,599 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:11:37,602 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:11:37,605 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:11:37,607 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:11:37,609 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:11:37,611 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:11:37,612 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 9
2025-07-28 12:11:37,614 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:11:37,620 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:37,623 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:37,951 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:11:37,952 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=eb4daac7-805c-4ed1-9171-5416d8eb76ea
2025-07-28 12:11:37,955 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:11:37,956 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0581, player_name=秦疏月, requested_object_id=4
2025-07-28 12:11:37,956 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:11:37,957 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'player_id': 'VirtualPlayerID0581', 'player_name': '秦疏月', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:11:37,957 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 秦疏月(VirtualPlayerID0581), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: eb4daac7-805c-4ed1-9171-5416d8eb76ea, 消息: 物品_4
2025-07-28 12:11:37,958 - [StatusUpdateThread] - INFO - 玩家 秦疏月 本次是第 2 次成功抓取。
2025-07-28 12:11:37,958 - [StatusUpdateThread] - INFO - 为玩家 秦疏月 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:11:37,959 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:11:37,959 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 1, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:11:37,960 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 1, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:37,981 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:37,983 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:38,265 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:11:38,265 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['秦疏月(2次)']。已选择: 秦疏月
2025-07-28 12:11:38,272 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:38,487 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:38,488 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:39,006 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:39,009 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:39,525 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:39,526 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:39,652 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E430>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 25
2025-07-28 12:11:39,656 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E430>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:39,657 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:39,729 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:11:39,737 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:11:39,919 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:11:39,923 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:11:39,923 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 99.9 秒
2025-07-28 12:11:39,924 - [HealthCheckThread] - WARNING - 已经 99.9 秒没有成功请求，尝试恢复连接...
2025-07-28 12:11:39,925 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:11:39,929 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:40,047 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:40,047 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:40,567 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:40,568 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:40,975 - [Thread-10] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 538, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:11:40,978 - [Thread-10] - DEBUG - Response received {'d': {'requestId': 538, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:41,086 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:41,087 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:41,602 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:41,603 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:41,665 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:41,667 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:11:41,669 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:41,674 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:41,965 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBCF10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:41,969 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:11:42,122 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': **********.9210944, 'target_id': '4', 'target_object': '机器人模型', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:11:42,124 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:42,424 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'eb4daac7-805c-4ed1-9171-5416d8eb76ea', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:42,424 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=eb4daac7-805c-4ed1-9171-5416d8eb76ea
2025-07-28 12:11:42,427 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:42,427 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0581, player_name=秦疏月, requested_object_id=4
2025-07-28 12:11:42,429 - [MoveServiceEventReceiver] - INFO - 移动服务请求 eb4daac7-805c-4ed1-9171-5416d8eb76ea (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:42,429 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 秦疏月(VirtualPlayerID0581)
2025-07-28 12:11:42,430 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:11:42,430 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 秦疏月(VirtualPlayerID0581), 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:11:42,431 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:42,431 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:42,643 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:11:42,644 - [GameProcessThread] - INFO - [游戏线程] 玩家 小熊软糖罐🐻(VirtualPlayerID0582) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:11:42,647 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小熊软糖罐🐻(VirtualPlayerID0582) 确保抓中, z_offset_extra=0
2025-07-28 12:11:42,647 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=b1b56f02-6684-4a95-b081-e853e2f2d628, Player=小熊软糖罐🐻, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:11:42,648 - [GameProcessThread] - INFO - [游戏线程] 玩家 小熊软糖罐🐻(VirtualPlayerID0582) 抓取指令已发送到移动服务，命令ID: b1b56f02-6684-4a95-b081-e853e2f2d628
2025-07-28 12:11:42,650 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:11:42,650 - [MoveServiceEventReceiver] - INFO - 抓取指令 b1b56f02-6684-4a95-b081-e853e2f2d628 已被移动服务接受并加入队列
2025-07-28 12:11:42,787 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.016秒 - 玩家: VirtualPlayerID0581, 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:11:42,788 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0581] = 2
2025-07-28 12:11:43,162 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628'}, pending_retry_player: None
2025-07-28 12:11:43,162 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:43,683 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628'}, pending_retry_player: None
2025-07-28 12:11:43,683 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:43,699 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 26
2025-07-28 12:11:43,700 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:43,701 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:44,204 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628'}, pending_retry_player: None
2025-07-28 12:11:44,208 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:44,297 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 已完成游戏，总游戏次数: 2
2025-07-28 12:11:44,725 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628'}, pending_retry_player: None
2025-07-28 12:11:44,725 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:45,243 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628'}, pending_retry_player: None
2025-07-28 12:11:45,244 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:45,612 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:11:45,613 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=b1b56f02-6684-4a95-b081-e853e2f2d628
2025-07-28 12:11:45,615 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:11:45,616 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0582, player_name=小熊软糖罐🐻, requested_object_id=5
2025-07-28 12:11:45,617 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:11:45,617 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'player_id': 'VirtualPlayerID0582', 'player_name': '小熊软糖罐🐻', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:11:45,618 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小熊软糖罐🐻(VirtualPlayerID0582), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: b1b56f02-6684-4a95-b081-e853e2f2d628, 消息: 物品_5
2025-07-28 12:11:45,619 - [StatusUpdateThread] - INFO - 玩家 小熊软糖罐🐻 本次是第 2 次成功抓取。
2025-07-28 12:11:45,620 - [StatusUpdateThread] - INFO - 为玩家 小熊软糖罐🐻 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:11:45,621 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:11:45,622 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 136, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:11:45,623 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 136, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:45,718 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:45,721 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:11:45,722 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:45,724 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:45,766 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:45,767 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:46,286 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:46,286 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:46,801 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:46,804 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:47,317 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:47,317 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:47,758 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC694F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 27
2025-07-28 12:11:47,764 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC694F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:47,765 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:11:47,766 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:11:47,769 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:11:47,770 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:11:47,779 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:11:47,784 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:11:47,786 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:11:47,837 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:47,839 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:48,355 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:48,356 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:48,651 - [Thread-11] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 18, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:11:48,652 - [Thread-11] - DEBUG - Response received {'d': {'requestId': 18, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:48,793 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:11:48,796 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:11:48,799 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:11:48,800 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:11:48,802 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:11:48,804 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:11:48,805 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 10
2025-07-28 12:11:48,807 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:11:48,821 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:48,825 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:48,873 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:48,874 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:49,393 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:49,393 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:49,896 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.017秒
2025-07-28 12:11:49,898 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:11:49,907 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:11:49,911 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': 1753675902.6440747, 'target_id': '5', 'target_object': '独角兽', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:11:49,912 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:50,132 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'b1b56f02-6684-4a95-b081-e853e2f2d628', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:50,133 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=b1b56f02-6684-4a95-b081-e853e2f2d628
2025-07-28 12:11:50,137 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:50,138 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0582, player_name=小熊软糖罐🐻, requested_object_id=5
2025-07-28 12:11:50,139 - [MoveServiceEventReceiver] - INFO - 移动服务请求 b1b56f02-6684-4a95-b081-e853e2f2d628 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:50,139 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小熊软糖罐🐻(VirtualPlayerID0582)
2025-07-28 12:11:50,140 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:11:50,141 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小熊软糖罐🐻(VirtualPlayerID0582), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:11:50,142 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:50,142 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:50,428 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:11:50,428 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.017秒 - 玩家: VirtualPlayerID0582, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:11:50,428 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:11:50,431 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0582] = 2
2025-07-28 12:11:50,431 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小酌几醉(VirtualPlayerID0577) 确保抓中, z_offset_extra=0
2025-07-28 12:11:50,433 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=2ebc8148-c377-4084-8a19-c40f8e7113bd, Player=小酌几醉, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:11:50,434 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 抓取指令已发送到移动服务，命令ID: 2ebc8148-c377-4084-8a19-c40f8e7113bd
2025-07-28 12:11:50,438 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:11:50,439 - [MoveServiceEventReceiver] - INFO - 抓取指令 2ebc8148-c377-4084-8a19-c40f8e7113bd 已被移动服务接受并加入队列
2025-07-28 12:11:50,852 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4130>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 28
2025-07-28 12:11:50,855 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4130>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:50,856 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:50,949 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd'}, pending_retry_player: None
2025-07-28 12:11:50,950 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:51,456 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd'}, pending_retry_player: None
2025-07-28 12:11:51,456 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:51,961 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd'}, pending_retry_player: None
2025-07-28 12:11:51,962 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:52,324 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已完成游戏，总游戏次数: 3
2025-07-28 12:11:52,324 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:11:52,329 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小熊软糖罐🐻(2次)', '小酌几醉(3次)']。已选择: 小熊软糖罐🐻
2025-07-28 12:11:52,329 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:11:52,466 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd'}, pending_retry_player: None
2025-07-28 12:11:52,466 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:52,863 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:52,866 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:11:52,868 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:52,872 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:52,976 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd'}, pending_retry_player: None
2025-07-28 12:11:52,977 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:53,431 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:11:53,432 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=2ebc8148-c377-4084-8a19-c40f8e7113bd
2025-07-28 12:11:53,436 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:11:53,437 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=2
2025-07-28 12:11:53,439 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:11:53,440 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'player_id': 'VirtualPlayerID0577', 'player_name': '小酌几醉', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:11:53,440 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小酌几醉(VirtualPlayerID0577), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: 2ebc8148-c377-4084-8a19-c40f8e7113bd, 消息: 物品_2
2025-07-28 12:11:53,441 - [StatusUpdateThread] - INFO - 玩家 小酌几醉 本次是第 3 次成功抓取。
2025-07-28 12:11:53,441 - [StatusUpdateThread] - INFO - 为玩家 小酌几醉 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:11:53,442 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:11:53,443 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 643, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:11:53,444 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 643, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:53,495 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:53,498 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:54,016 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:54,017 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:54,532 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:54,533 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:54,897 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF43D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 29
2025-07-28 12:11:54,901 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF43D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:11:54,907 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:11:55,039 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:55,040 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:55,560 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:55,560 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:56,078 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:56,079 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:56,457 - [Thread-12] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 113, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:11:56,458 - [Thread-12] - DEBUG - Response received {'d': {'requestId': 113, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:11:56,584 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:56,584 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:56,925 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:11:56,928 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:11:56,929 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:11:56,932 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:11:57,096 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:57,097 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:57,617 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675910.4287863, 'target_id': '2', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:11:57,617 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:57,897 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '2ebc8148-c377-4084-8a19-c40f8e7113bd', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:11:57,897 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=2ebc8148-c377-4084-8a19-c40f8e7113bd
2025-07-28 12:11:57,902 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:11:57,905 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=2
2025-07-28 12:11:57,907 - [MoveServiceEventReceiver] - INFO - 移动服务请求 2ebc8148-c377-4084-8a19-c40f8e7113bd (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:11:57,907 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小酌几醉(VirtualPlayerID0577)
2025-07-28 12:11:57,909 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:11:57,910 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小酌几醉(VirtualPlayerID0577), 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:11:57,911 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:11:57,912 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:11:58,037 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.015秒 - 玩家: VirtualPlayerID0577, 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:11:58,040 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0577] = 3
2025-07-28 12:11:58,132 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:11:58,133 - [GameProcessThread] - INFO - [游戏线程] 玩家 氧气柠檬(VirtualPlayerID0583) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:11:58,137 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 氧气柠檬(VirtualPlayerID0583) 确保抓中, z_offset_extra=0
2025-07-28 12:11:58,138 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=4e9a7f28-e99d-4cef-9cbb-7c39516838f7, Player=氧气柠檬, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:11:58,139 - [GameProcessThread] - INFO - [游戏线程] 玩家 氧气柠檬(VirtualPlayerID0583) 抓取指令已发送到移动服务，命令ID: 4e9a7f28-e99d-4cef-9cbb-7c39516838f7
2025-07-28 12:11:58,142 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:11:58,143 - [MoveServiceEventReceiver] - INFO - 抓取指令 4e9a7f28-e99d-4cef-9cbb-7c39516838f7 已被移动服务接受并加入队列
2025-07-28 12:11:58,369 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 已完成游戏，总游戏次数: 2
2025-07-28 12:11:58,655 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7'}, pending_retry_player: None
2025-07-28 12:11:58,655 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:58,972 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC850>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 30
2025-07-28 12:11:58,978 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC850>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:11:58,979 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:11:58,980 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:11:58,981 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:11:58,981 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:11:58,982 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:11:58,983 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:11:58,984 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:11:59,160 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7'}, pending_retry_player: None
2025-07-28 12:11:59,160 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:59,669 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7'}, pending_retry_player: None
2025-07-28 12:11:59,675 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:11:59,997 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:12:00,003 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:12:00,007 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:12:00,009 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:00,012 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:12:00,015 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:12:00,017 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 11
2025-07-28 12:12:00,020 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:12:00,021 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:00,023 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:00,077 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:12:00,099 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:12:00,189 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7'}, pending_retry_player: None
2025-07-28 12:12:00,193 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:00,376 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:12:00,376 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['氧气柠檬(2次)', '小酌几醉(3次)']。已选择: 氧气柠檬
2025-07-28 12:12:00,379 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:00,712 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7'}, pending_retry_player: None
2025-07-28 12:12:00,712 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:01,177 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:12:01,177 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=4e9a7f28-e99d-4cef-9cbb-7c39516838f7
2025-07-28 12:12:01,179 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:12:01,183 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0583, player_name=氧气柠檬, requested_object_id=4
2025-07-28 12:12:01,185 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:12:01,188 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'player_id': 'VirtualPlayerID0583', 'player_name': '氧气柠檬', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:12:01,188 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 氧气柠檬(VirtualPlayerID0583), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: 4e9a7f28-e99d-4cef-9cbb-7c39516838f7, 消息: 物品_4
2025-07-28 12:12:01,194 - [StatusUpdateThread] - INFO - 玩家 氧气柠檬 本次是第 2 次成功抓取。
2025-07-28 12:12:01,199 - [StatusUpdateThread] - INFO - 为玩家 氧气柠檬 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:01,201 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:12:01,204 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 455, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:12:01,206 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 455, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:01,223 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:01,227 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:01,743 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:01,746 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:02,044 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC220>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 31
2025-07-28 12:12:02,047 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC220>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:02,048 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:02,262 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:02,263 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:02,779 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:02,780 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:03,294 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:03,295 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:03,815 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:03,816 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:04,065 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:04,075 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:12:04,084 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:04,090 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:04,221 - [Thread-13] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 810, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:12:04,223 - [Thread-13] - DEBUG - Response received {'d': {'requestId': 810, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:04,333 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:04,334 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:04,394 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:12:04,395 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小酌几醉(3次)']。已选择: 小酌几醉
2025-07-28 12:12:04,398 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已加入队列末尾。优先级: 2.10 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:04,853 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:04,853 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:05,372 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': 1753675918.1338494, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:12:05,375 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:05,704 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '4e9a7f28-e99d-4cef-9cbb-7c39516838f7', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:05,704 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=4e9a7f28-e99d-4cef-9cbb-7c39516838f7
2025-07-28 12:12:05,716 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:05,720 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0583, player_name=氧气柠檬, requested_object_id=4
2025-07-28 12:12:05,724 - [MoveServiceEventReceiver] - INFO - 移动服务请求 4e9a7f28-e99d-4cef-9cbb-7c39516838f7 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:05,724 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 氧气柠檬(VirtualPlayerID0583)
2025-07-28 12:12:05,725 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:12:05,726 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 氧气柠檬(VirtualPlayerID0583), 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:12:05,727 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:05,727 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:12:05,893 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:12:05,893 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:12:05,897 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 十里桃花(VirtualPlayerID0578) 确保抓中, z_offset_extra=0
2025-07-28 12:12:05,898 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=ebfbd97b-bd21-4910-8da2-4f1b4bb394d3, Player=十里桃花, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:12:05,900 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:12:05,904 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 抓取指令已发送到移动服务，命令ID: ebfbd97b-bd21-4910-8da2-4f1b4bb394d3
2025-07-28 12:12:05,906 - [MoveServiceEventReceiver] - INFO - 抓取指令 ebfbd97b-bd21-4910-8da2-4f1b4bb394d3 已被移动服务接受并加入队列
2025-07-28 12:12:06,129 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E1F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 32
2025-07-28 12:12:06,132 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E1F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:06,133 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:06,174 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.015秒 - 玩家: VirtualPlayerID0583, 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:12:06,175 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0583] = 2
2025-07-28 12:12:06,414 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已完成游戏，总游戏次数: 3
2025-07-28 12:12:06,414 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:12:06,415 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3'}, pending_retry_player: None
2025-07-28 12:12:06,416 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['十里桃花(3次)']。已选择: 十里桃花
2025-07-28 12:12:06,417 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:06,418 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:06,932 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3'}, pending_retry_player: None
2025-07-28 12:12:06,933 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:07,438 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3'}, pending_retry_player: None
2025-07-28 12:12:07,440 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:07,960 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3'}, pending_retry_player: None
2025-07-28 12:12:07,960 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:08,148 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:08,150 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:12:08,151 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:08,154 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:08,465 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3'}, pending_retry_player: None
2025-07-28 12:12:08,466 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:08,943 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:12:08,943 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=ebfbd97b-bd21-4910-8da2-4f1b4bb394d3
2025-07-28 12:12:08,945 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:12:08,945 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0578, player_name=十里桃花, requested_object_id=1
2025-07-28 12:12:08,946 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:12:08,946 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'player_id': 'VirtualPlayerID0578', 'player_name': '十里桃花', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:12:08,946 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 十里桃花(VirtualPlayerID0578), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: ebfbd97b-bd21-4910-8da2-4f1b4bb394d3, 消息: 物品_1
2025-07-28 12:12:08,947 - [StatusUpdateThread] - INFO - 玩家 十里桃花 本次是第 3 次成功抓取。
2025-07-28 12:12:08,948 - [StatusUpdateThread] - INFO - 为玩家 十里桃花 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:08,948 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:12:08,949 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 992, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:12:08,950 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 992, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:08,983 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:08,984 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:09,501 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:09,505 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:10,020 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:10,024 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:10,211 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 33
2025-07-28 12:12:10,216 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:12:10,217 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:12:10,220 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:12:10,221 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:12:10,222 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:12:10,223 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:12:10,224 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:12:10,225 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:12:10,227 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:12:10,249 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:12:10,541 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:10,541 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:11,056 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:11,057 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:11,243 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:12:11,247 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:12:11,249 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:12:11,252 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:11,253 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:12:11,254 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:12:11,256 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 12
2025-07-28 12:12:11,258 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:12:11,260 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:11,270 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:11,575 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:11,577 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:11,955 - [Thread-14] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 787, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:12:11,957 - [Thread-14] - DEBUG - Response received {'d': {'requestId': 787, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:11,983 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:12:11,987 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:12:11,988 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 132.0 秒
2025-07-28 12:12:11,989 - [HealthCheckThread] - WARNING - 已经 132.0 秒没有成功请求，尝试恢复连接...
2025-07-28 12:12:11,990 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:11,996 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:12,092 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:12,092 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:12,611 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:12,611 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:13,128 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': **********.8932104, 'target_id': '1', 'target_object': '变形金刚', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:12:13,129 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:13,301 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDFD550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 34
2025-07-28 12:12:13,304 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDFD550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:13,305 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:13,459 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'ebfbd97b-bd21-4910-8da2-4f1b4bb394d3', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:13,460 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=ebfbd97b-bd21-4910-8da2-4f1b4bb394d3
2025-07-28 12:12:13,462 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:13,463 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0578, player_name=十里桃花, requested_object_id=1
2025-07-28 12:12:13,464 - [MoveServiceEventReceiver] - INFO - 移动服务请求 ebfbd97b-bd21-4910-8da2-4f1b4bb394d3 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:13,464 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 十里桃花(VirtualPlayerID0578)
2025-07-28 12:12:13,465 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:12:13,466 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 十里桃花(VirtualPlayerID0578), 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:12:13,467 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:13,467 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:12:13,649 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:12:13,650 - [GameProcessThread] - INFO - [游戏线程] 玩家 花未眠(VirtualPlayerID0579) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:12:13,652 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 花未眠(VirtualPlayerID0579) 确保抓中, z_offset_extra=0
2025-07-28 12:12:13,653 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=8acc834f-c064-4abd-b357-eafb56491e81, Player=花未眠, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:12:13,653 - [GameProcessThread] - INFO - [游戏线程] 玩家 花未眠(VirtualPlayerID0579) 抓取指令已发送到移动服务，命令ID: 8acc834f-c064-4abd-b357-eafb56491e81
2025-07-28 12:12:13,656 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:12:13,657 - [MoveServiceEventReceiver] - INFO - 抓取指令 8acc834f-c064-4abd-b357-eafb56491e81 已被移动服务接受并加入队列
2025-07-28 12:12:13,802 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.029秒 - 玩家: VirtualPlayerID0578, 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:12:13,807 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0578] = 3
2025-07-28 12:12:14,011 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBC1F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:12:14,013 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:12:14,154 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81'}, pending_retry_player: None
2025-07-28 12:12:14,155 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:14,455 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 已完成游戏，总游戏次数: 3
2025-07-28 12:12:14,455 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:12:14,457 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['花未眠(3次)']。已选择: 花未眠
2025-07-28 12:12:14,458 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 花未眠 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:14,661 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81'}, pending_retry_player: None
2025-07-28 12:12:14,661 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:15,178 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81'}, pending_retry_player: None
2025-07-28 12:12:15,179 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:15,320 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:15,324 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:12:15,325 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:15,328 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:15,683 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81'}, pending_retry_player: None
2025-07-28 12:12:15,683 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:16,203 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81'}, pending_retry_player: None
2025-07-28 12:12:16,207 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:16,677 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:12:16,677 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=8acc834f-c064-4abd-b357-eafb56491e81
2025-07-28 12:12:16,680 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:12:16,680 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0579, player_name=花未眠, requested_object_id=3
2025-07-28 12:12:16,681 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:12:16,681 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'player_id': 'VirtualPlayerID0579', 'player_name': '花未眠', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:12:16,681 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 花未眠(VirtualPlayerID0579), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 8acc834f-c064-4abd-b357-eafb56491e81, 消息: 物品_3
2025-07-28 12:12:16,683 - [StatusUpdateThread] - INFO - 玩家 花未眠 本次是第 3 次成功抓取。
2025-07-28 12:12:16,684 - [StatusUpdateThread] - INFO - 为玩家 花未眠 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:16,685 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:12:16,685 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 972, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:12:16,686 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 972, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:16,721 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:16,722 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:17,240 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:17,243 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:17,350 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC430>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 35
2025-07-28 12:12:17,354 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC430>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:17,355 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:17,761 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:17,762 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:18,267 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:18,268 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:18,787 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:18,788 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:19,308 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:19,309 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:19,369 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:19,373 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:12:19,374 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:19,379 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:19,700 - [Thread-15] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 230, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:12:19,703 - [Thread-15] - DEBUG - Response received {'d': {'requestId': 230, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:19,827 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:19,827 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:20,346 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:20,346 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:20,390 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:12:20,396 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:12:20,864 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0579', 'name': '花未眠', 'start_time': **********.6500459, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:20,865 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:21,148 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '8acc834f-c064-4abd-b357-eafb56491e81', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:21,148 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=8acc834f-c064-4abd-b357-eafb56491e81
2025-07-28 12:12:21,157 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:21,165 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0579, player_name=花未眠, requested_object_id=3
2025-07-28 12:12:21,169 - [MoveServiceEventReceiver] - INFO - 移动服务请求 8acc834f-c064-4abd-b357-eafb56491e81 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:21,169 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 花未眠(VirtualPlayerID0579)
2025-07-28 12:12:21,171 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:12:21,171 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 花未眠(VirtualPlayerID0579), 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:12:21,173 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:21,173 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:12:21,382 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:12:21,382 - [GameProcessThread] - INFO - [游戏线程] 玩家 奶萌小可爱(VirtualPlayerID0580) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:12:21,389 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 奶萌小可爱(VirtualPlayerID0580) 确保抓中, z_offset_extra=0
2025-07-28 12:12:21,390 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=fefab293-2ef3-43ae-a346-888984d04673, Player=奶萌小可爱, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:12:21,391 - [GameProcessThread] - INFO - [游戏线程] 玩家 奶萌小可爱(VirtualPlayerID0580) 抓取指令已发送到移动服务，命令ID: fefab293-2ef3-43ae-a346-888984d04673
2025-07-28 12:12:21,394 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:12:21,398 - [MoveServiceEventReceiver] - INFO - 抓取指令 fefab293-2ef3-43ae-a346-888984d04673 已被移动服务接受并加入队列
2025-07-28 12:12:21,413 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 36
2025-07-28 12:12:21,414 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:12:21,418 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:12:21,422 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:12:21,427 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.015秒 - 玩家: VirtualPlayerID0579, 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:12:21,422 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:12:21,436 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0579] = 3
2025-07-28 12:12:21,440 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:12:21,442 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:12:21,443 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:12:21,445 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:12:21,903 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673'}, pending_retry_player: None
2025-07-28 12:12:21,905 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:22,410 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673'}, pending_retry_player: None
2025-07-28 12:12:22,410 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:22,458 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:12:22,459 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:12:22,460 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:12:22,461 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:22,462 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:12:22,463 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:12:22,464 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 13
2025-07-28 12:12:22,465 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:12:22,468 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:22,480 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:22,505 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 已完成游戏，总游戏次数: 3
2025-07-28 12:12:22,917 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673'}, pending_retry_player: None
2025-07-28 12:12:22,918 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:23,423 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673'}, pending_retry_player: None
2025-07-28 12:12:23,424 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:23,927 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673'}, pending_retry_player: None
2025-07-28 12:12:23,927 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:24,433 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673'}, pending_retry_player: None
2025-07-28 12:12:24,434 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:12:24,434 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:24,438 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=fefab293-2ef3-43ae-a346-888984d04673
2025-07-28 12:12:24,441 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:12:24,441 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0580, player_name=奶萌小可爱, requested_object_id=3
2025-07-28 12:12:24,441 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:12:24,442 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'player_id': 'VirtualPlayerID0580', 'player_name': '奶萌小可爱', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:12:24,442 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 奶萌小可爱(VirtualPlayerID0580), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: fefab293-2ef3-43ae-a346-888984d04673, 消息: 物品_3
2025-07-28 12:12:24,443 - [StatusUpdateThread] - INFO - 玩家 奶萌小可爱 本次是第 3 次成功抓取。
2025-07-28 12:12:24,443 - [StatusUpdateThread] - INFO - 为玩家 奶萌小可爱 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:24,444 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:12:24,444 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 587, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:12:24,445 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 587, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:24,523 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 37
2025-07-28 12:12:24,526 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:24,526 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:24,955 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:24,958 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:25,475 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:25,476 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:25,991 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:25,992 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:26,499 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:26,499 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:26,514 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:12:26,516 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['奶萌小可爱(3次)']。已选择: 奶萌小可爱
2025-07-28 12:12:26,517 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 奶萌小可爱 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:26,530 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:26,533 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:12:26,535 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:26,542 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:27,019 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:27,023 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:27,458 - [Thread-16] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 643, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:12:27,460 - [Thread-16] - DEBUG - Response received {'d': {'requestId': 643, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:27,534 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:27,534 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:28,051 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:28,051 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:28,571 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0580', 'name': '奶萌小可爱', 'start_time': 1753675941.3829887, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:28,573 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 38
2025-07-28 12:12:28,576 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:28,577 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:28,578 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:28,963 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'fefab293-2ef3-43ae-a346-888984d04673', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:28,964 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=fefab293-2ef3-43ae-a346-888984d04673
2025-07-28 12:12:28,970 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:28,971 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0580, player_name=奶萌小可爱, requested_object_id=3
2025-07-28 12:12:28,972 - [MoveServiceEventReceiver] - INFO - 移动服务请求 fefab293-2ef3-43ae-a346-888984d04673 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:28,972 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 奶萌小可爱(VirtualPlayerID0580)
2025-07-28 12:12:28,973 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:12:28,973 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 奶萌小可爱(VirtualPlayerID0580), 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:12:28,974 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:28,975 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:12:29,075 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.019秒 - 玩家: VirtualPlayerID0580, 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:12:29,080 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0580] = 3
2025-07-28 12:12:29,088 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:12:29,091 - [GameProcessThread] - INFO - [游戏线程] 玩家 秦疏月(VirtualPlayerID0581) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:12:29,093 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 秦疏月(VirtualPlayerID0581) 确保抓中, z_offset_extra=0
2025-07-28 12:12:29,094 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=d1b5ad95-a497-418f-bacb-ab46e78c19ab, Player=秦疏月, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:12:29,094 - [GameProcessThread] - INFO - [游戏线程] 玩家 秦疏月(VirtualPlayerID0581) 抓取指令已发送到移动服务，命令ID: d1b5ad95-a497-418f-bacb-ab46e78c19ab
2025-07-28 12:12:29,096 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:12:29,098 - [MoveServiceEventReceiver] - INFO - 抓取指令 d1b5ad95-a497-418f-bacb-ab46e78c19ab 已被移动服务接受并加入队列
2025-07-28 12:12:29,609 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab'}, pending_retry_player: None
2025-07-28 12:12:29,609 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:30,116 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab'}, pending_retry_player: None
2025-07-28 12:12:30,117 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:30,523 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 已完成游戏，总游戏次数: 3
2025-07-28 12:12:30,587 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:30,589 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:12:30,590 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:30,595 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:30,604 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:12:30,622 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:12:30,637 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab'}, pending_retry_player: None
2025-07-28 12:12:30,637 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:31,152 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab'}, pending_retry_player: None
2025-07-28 12:12:31,153 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:31,659 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab'}, pending_retry_player: None
2025-07-28 12:12:31,660 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:32,111 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:12:32,111 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=d1b5ad95-a497-418f-bacb-ab46e78c19ab
2025-07-28 12:12:32,113 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:12:32,113 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0581, player_name=秦疏月, requested_object_id=5
2025-07-28 12:12:32,114 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:12:32,114 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'player_id': 'VirtualPlayerID0581', 'player_name': '秦疏月', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:12:32,114 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 秦疏月(VirtualPlayerID0581), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: d1b5ad95-a497-418f-bacb-ab46e78c19ab, 消息: 物品_5
2025-07-28 12:12:32,115 - [StatusUpdateThread] - INFO - 玩家 秦疏月 本次是第 3 次成功抓取。
2025-07-28 12:12:32,116 - [StatusUpdateThread] - INFO - 为玩家 秦疏月 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:32,117 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:12:32,117 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 457, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:12:32,118 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 457, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:32,173 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:32,176 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:32,629 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4070>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 39
2025-07-28 12:12:32,635 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4070>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:12:32,638 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:12:32,641 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:12:32,642 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:12:32,647 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:12:32,654 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:12:32,655 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:12:32,658 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:12:32,692 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:32,693 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:33,197 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:33,197 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:33,669 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:12:33,676 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:12:33,681 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:12:33,687 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:33,690 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:12:33,692 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:12:33,694 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 14
2025-07-28 12:12:33,696 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:12:33,699 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:33,704 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:33,718 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:33,719 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:34,223 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:34,223 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:34,744 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:34,744 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:35,140 - [Thread-17] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 721, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:12:35,143 - [Thread-17] - DEBUG - Response received {'d': {'requestId': 721, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:35,263 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:35,264 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:35,749 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9EF70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 40
2025-07-28 12:12:35,753 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9EF70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:35,754 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:35,781 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:35,782 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:36,301 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0581', 'name': '秦疏月', 'start_time': 1753675949.0910356, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:36,302 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:36,584 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'd1b5ad95-a497-418f-bacb-ab46e78c19ab', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:36,584 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=d1b5ad95-a497-418f-bacb-ab46e78c19ab
2025-07-28 12:12:36,587 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:36,589 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0581, player_name=秦疏月, requested_object_id=5
2025-07-28 12:12:36,590 - [MoveServiceEventReceiver] - INFO - 移动服务请求 d1b5ad95-a497-418f-bacb-ab46e78c19ab (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:36,590 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 秦疏月(VirtualPlayerID0581)
2025-07-28 12:12:36,591 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:12:36,591 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 秦疏月(VirtualPlayerID0581), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:12:36,592 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:36,593 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:12:36,711 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.017秒 - 玩家: VirtualPlayerID0581, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:12:36,712 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0581] = 3
2025-07-28 12:12:36,819 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:12:36,820 - [GameProcessThread] - INFO - [游戏线程] 玩家 小熊软糖罐🐻(VirtualPlayerID0582) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:12:36,823 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小熊软糖罐🐻(VirtualPlayerID0582) 确保抓中, z_offset_extra=0
2025-07-28 12:12:36,824 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=62780409-03ee-4858-a566-c61f3bf1af9b, Player=小熊软糖罐🐻, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:12:36,824 - [GameProcessThread] - INFO - [游戏线程] 玩家 小熊软糖罐🐻(VirtualPlayerID0582) 抓取指令已发送到移动服务，命令ID: 62780409-03ee-4858-a566-c61f3bf1af9b
2025-07-28 12:12:36,827 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:12:36,828 - [MoveServiceEventReceiver] - INFO - 抓取指令 62780409-03ee-4858-a566-c61f3bf1af9b 已被移动服务接受并加入队列
2025-07-28 12:12:37,327 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b'}, pending_retry_player: None
2025-07-28 12:12:37,327 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:37,769 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:37,777 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:12:37,782 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:37,788 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:37,833 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b'}, pending_retry_player: None
2025-07-28 12:12:37,834 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:38,355 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b'}, pending_retry_player: None
2025-07-28 12:12:38,355 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:38,561 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 已完成游戏，总游戏次数: 3
2025-07-28 12:12:38,561 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:12:38,564 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['秦疏月(3次)', '小熊软糖罐🐻(3次)']。已选择: 秦疏月
2025-07-28 12:12:38,565 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 秦疏月 已加入队列末尾。优先级: 2.10 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:38,876 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b'}, pending_retry_player: None
2025-07-28 12:12:38,879 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:39,398 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b'}, pending_retry_player: None
2025-07-28 12:12:39,399 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:39,820 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC7C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 41
2025-07-28 12:12:39,824 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC7C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:39,826 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:39,872 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:12:39,873 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=62780409-03ee-4858-a566-c61f3bf1af9b
2025-07-28 12:12:39,876 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:12:39,879 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0582, player_name=小熊软糖罐🐻, requested_object_id=2
2025-07-28 12:12:39,880 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:12:39,881 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'player_id': 'VirtualPlayerID0582', 'player_name': '小熊软糖罐🐻', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:12:39,881 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小熊软糖罐🐻(VirtualPlayerID0582), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: 62780409-03ee-4858-a566-c61f3bf1af9b, 消息: 物品_2
2025-07-28 12:12:39,883 - [StatusUpdateThread] - INFO - 玩家 小熊软糖罐🐻 本次是第 3 次成功抓取。
2025-07-28 12:12:39,883 - [StatusUpdateThread] - INFO - 为玩家 小熊软糖罐🐻 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:39,884 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:12:39,884 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 272, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:12:39,884 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 272, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:39,919 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:39,920 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:40,423 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:40,423 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:40,768 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.014秒
2025-07-28 12:12:40,775 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:12:40,802 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:12:40,941 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:40,944 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:41,464 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:41,465 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:41,840 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:41,846 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:12:41,849 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:41,858 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:41,981 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:41,982 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:42,502 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:42,505 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:42,894 - [Thread-18] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 140, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:12:42,895 - [Thread-18] - DEBUG - Response received {'d': {'requestId': 140, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:43,021 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:43,021 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:43,542 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:43,544 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:43,890 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69D30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 42
2025-07-28 12:12:43,893 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69D30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:12:43,895 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:12:43,895 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:12:43,896 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:12:43,897 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:12:43,898 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:12:43,899 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:12:43,900 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:12:44,029 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:12:44,030 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:12:44,035 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 164.0 秒
2025-07-28 12:12:44,036 - [HealthCheckThread] - WARNING - 已经 164.0 秒没有成功请求，尝试恢复连接...
2025-07-28 12:12:44,037 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:44,041 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:44,062 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0582', 'name': '小熊软糖罐🐻', 'start_time': **********.8209903, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:12:44,062 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:44,408 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '62780409-03ee-4858-a566-c61f3bf1af9b', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:44,408 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=62780409-03ee-4858-a566-c61f3bf1af9b
2025-07-28 12:12:44,411 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:44,412 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0582, player_name=小熊软糖罐🐻, requested_object_id=2
2025-07-28 12:12:44,413 - [MoveServiceEventReceiver] - INFO - 移动服务请求 62780409-03ee-4858-a566-c61f3bf1af9b (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:44,413 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小熊软糖罐🐻(VirtualPlayerID0582)
2025-07-28 12:12:44,413 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:12:44,414 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小熊软糖罐🐻(VirtualPlayerID0582), 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:12:44,416 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:44,416 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:12:44,579 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:12:44,579 - [GameProcessThread] - INFO - [游戏线程] 玩家 氧气柠檬(VirtualPlayerID0583) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:12:44,581 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 氧气柠檬(VirtualPlayerID0583) 确保抓中, z_offset_extra=0
2025-07-28 12:12:44,582 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=687dfff3-54e2-4114-bfe6-c5ded37c4b50, Player=氧气柠檬, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:12:44,582 - [GameProcessThread] - INFO - [游戏线程] 玩家 氧气柠檬(VirtualPlayerID0583) 抓取指令已发送到移动服务，命令ID: 687dfff3-54e2-4114-bfe6-c5ded37c4b50
2025-07-28 12:12:44,586 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:12:44,586 - [MoveServiceEventReceiver] - INFO - 抓取指令 687dfff3-54e2-4114-bfe6-c5ded37c4b50 已被移动服务接受并加入队列
2025-07-28 12:12:44,595 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 已完成游戏，总游戏次数: 3
2025-07-28 12:12:44,601 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:12:44,608 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小熊软糖罐🐻(3次)', '氧气柠檬(3次)']。已选择: 小熊软糖罐🐻
2025-07-28 12:12:44,611 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小熊软糖罐🐻 已加入队列末尾。优先级: 2.10 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:44,863 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.016秒 - 玩家: VirtualPlayerID0582, 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:12:44,863 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0582] = 3
2025-07-28 12:12:44,910 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:12:44,912 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:12:44,913 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:12:44,914 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:44,916 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:12:44,917 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:12:44,918 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 15
2025-07-28 12:12:44,923 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:12:44,929 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:44,937 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:45,099 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50'}, pending_retry_player: None
2025-07-28 12:12:45,099 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:45,608 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50'}, pending_retry_player: None
2025-07-28 12:12:45,610 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:46,070 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDBCE20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:12:46,079 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:12:46,129 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50'}, pending_retry_player: None
2025-07-28 12:12:46,130 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:46,636 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50'}, pending_retry_player: None
2025-07-28 12:12:46,637 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:46,966 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDFD730>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 43
2025-07-28 12:12:46,969 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDFD730>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:46,973 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:47,156 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50'}, pending_retry_player: None
2025-07-28 12:12:47,156 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:47,611 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:12:47,612 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=687dfff3-54e2-4114-bfe6-c5ded37c4b50
2025-07-28 12:12:47,616 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:12:47,617 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0583, player_name=氧气柠檬, requested_object_id=3
2025-07-28 12:12:47,618 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:12:47,618 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'player_id': 'VirtualPlayerID0583', 'player_name': '氧气柠檬', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:12:47,618 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 氧气柠檬(VirtualPlayerID0583), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 687dfff3-54e2-4114-bfe6-c5ded37c4b50, 消息: 物品_3
2025-07-28 12:12:47,619 - [StatusUpdateThread] - INFO - 玩家 氧气柠檬 本次是第 3 次成功抓取。
2025-07-28 12:12:47,620 - [StatusUpdateThread] - INFO - 为玩家 氧气柠檬 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:47,621 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:12:47,622 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 392, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:12:47,626 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 392, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:47,675 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:47,676 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:48,193 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:48,193 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:48,713 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:48,713 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:48,979 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:48,983 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:12:48,984 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:48,988 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:49,232 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:49,232 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:49,749 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:49,749 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:50,252 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:50,257 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:50,647 - [Thread-19] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 98, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:12:50,648 - [Thread-19] - DEBUG - Response received {'d': {'requestId': 98, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:50,771 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:50,776 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:50,945 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:12:50,968 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:12:51,009 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69880>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 44
2025-07-28 12:12:51,012 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFC69880>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:51,014 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:51,295 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:51,295 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:51,814 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0583', 'name': '氧气柠檬', 'start_time': **********.579815, 'target_id': '3', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:12:51,814 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:52,146 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '687dfff3-54e2-4114-bfe6-c5ded37c4b50', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:52,146 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=687dfff3-54e2-4114-bfe6-c5ded37c4b50
2025-07-28 12:12:52,148 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:52,149 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0583, player_name=氧气柠檬, requested_object_id=3
2025-07-28 12:12:52,150 - [MoveServiceEventReceiver] - INFO - 移动服务请求 687dfff3-54e2-4114-bfe6-c5ded37c4b50 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:52,150 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 氧气柠檬(VirtualPlayerID0583)
2025-07-28 12:12:52,151 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:12:52,152 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 氧气柠檬(VirtualPlayerID0583), 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:12:52,152 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:52,152 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:12:52,319 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:12:52,320 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:12:52,329 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小酌几醉(VirtualPlayerID0577) 确保抓中, z_offset_extra=0
2025-07-28 12:12:52,331 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=cf1a0b32-34d6-4f2f-b963-4db7c0f85c96, Player=小酌几醉, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:12:52,331 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:12:52,333 - [GameProcessThread] - INFO - [游戏线程] 玩家 小酌几醉(VirtualPlayerID0577) 抓取指令已发送到移动服务，命令ID: cf1a0b32-34d6-4f2f-b963-4db7c0f85c96
2025-07-28 12:12:52,335 - [MoveServiceEventReceiver] - INFO - 抓取指令 cf1a0b32-34d6-4f2f-b963-4db7c0f85c96 已被移动服务接受并加入队列
2025-07-28 12:12:52,511 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.019秒 - 玩家: VirtualPlayerID0583, 结果: 物品_3, 订单: virtual_paid
2025-07-28 12:12:52,511 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0583] = 3
2025-07-28 12:12:52,651 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已完成游戏，总游戏次数: 4
2025-07-28 12:12:52,651 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小酌几醉 已退休。
2025-07-28 12:12:52,653 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 新玩家 别在我眼前犯贱 已补充到活跃池。
2025-07-28 12:12:52,653 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:12:52,654 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['别在我眼前犯贱(0次)', '氧气柠檬(3次)']。已选择: 别在我眼前犯贱
2025-07-28 12:12:52,655 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 别在我眼前犯贱 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:52,855 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96'}, pending_retry_player: None
2025-07-28 12:12:52,855 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:53,028 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:12:53,030 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:12:53,032 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:53,036 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:53,361 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96'}, pending_retry_player: None
2025-07-28 12:12:53,362 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:53,866 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96'}, pending_retry_player: None
2025-07-28 12:12:53,867 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:54,371 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96'}, pending_retry_player: None
2025-07-28 12:12:54,371 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:54,669 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:12:54,674 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['氧气柠檬(3次)']。已选择: 氧气柠檬
2025-07-28 12:12:54,676 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 氧气柠檬 已加入队列末尾。优先级: 2.10 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:12:54,892 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96'}, pending_retry_player: None
2025-07-28 12:12:54,893 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:55,064 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC130>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 45
2025-07-28 12:12:55,068 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDAC130>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:12:55,070 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:12:55,072 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:12:55,073 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:12:55,074 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:12:55,075 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:12:55,076 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:12:55,077 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:12:55,367 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:12:55,368 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=cf1a0b32-34d6-4f2f-b963-4db7c0f85c96
2025-07-28 12:12:55,373 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:12:55,379 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=5
2025-07-28 12:12:55,379 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:12:55,382 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'player_id': 'VirtualPlayerID0577', 'player_name': '小酌几醉', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:12:55,382 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 小酌几醉(VirtualPlayerID0577), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: cf1a0b32-34d6-4f2f-b963-4db7c0f85c96, 消息: 物品_5
2025-07-28 12:12:55,386 - [StatusUpdateThread] - INFO - 玩家 小酌几醉 本次是第 4 次成功抓取。
2025-07-28 12:12:55,389 - [StatusUpdateThread] - INFO - 为玩家 小酌几醉 生成优惠券提示: 获得满减160元券！
再抓到一次优惠翻倍！
2025-07-28 12:12:55,392 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(4次): 抓中特效4
2025-07-28 12:12:55,396 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 872, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': True}}}
2025-07-28 12:12:55,397 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96'}, pending_retry_player: None
2025-07-28 12:12:55,402 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:55,402 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 872, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:55,911 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:55,912 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:56,084 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:12:56,089 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:12:56,103 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:12:56,105 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:12:56,107 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:12:56,109 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:12:56,110 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 16
2025-07-28 12:12:56,112 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:12:56,114 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:12:56,118 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:12:56,431 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:56,432 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:56,951 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:56,952 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:57,473 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:57,476 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:57,989 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:57,990 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:58,149 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E670>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 46
2025-07-28 12:12:58,155 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFD9E670>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:12:58,157 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:12:58,415 - [Thread-20] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 130, 'requestData': {'sceneName': '场景1', 'sceneItemId': 57, 'sceneItemEnabled': False}}}
2025-07-28 12:12:58,417 - [Thread-20] - DEBUG - Response received {'d': {'requestId': 130, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:12:58,495 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:58,496 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:59,002 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:59,004 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:59,520 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0577', 'name': '小酌几醉', 'start_time': 1753675972.320722, 'target_id': '5', 'target_object': '皮卡丘', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:12:59,520 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:12:59,898 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'cf1a0b32-34d6-4f2f-b963-4db7c0f85c96', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:12:59,899 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=cf1a0b32-34d6-4f2f-b963-4db7c0f85c96
2025-07-28 12:12:59,903 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:12:59,918 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0577, player_name=小酌几醉, requested_object_id=5
2025-07-28 12:12:59,928 - [MoveServiceEventReceiver] - INFO - 移动服务请求 cf1a0b32-34d6-4f2f-b963-4db7c0f85c96 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:12:59,928 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 小酌几醉(VirtualPlayerID0577)
2025-07-28 12:12:59,932 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:12:59,934 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 小酌几醉(VirtualPlayerID0577), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:12:59,936 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:12:59,939 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:13:00,023 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:13:00,024 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:13:00,025 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 十里桃花(VirtualPlayerID0578) 确保抓中, z_offset_extra=0
2025-07-28 12:13:00,026 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=5bca95f2-5576-44ac-824a-0f07157b79b9, Player=十里桃花, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:13:00,026 - [GameProcessThread] - INFO - [游戏线程] 玩家 十里桃花(VirtualPlayerID0578) 抓取指令已发送到移动服务，命令ID: 5bca95f2-5576-44ac-824a-0f07157b79b9
2025-07-28 12:13:00,029 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '5bca95f2-5576-44ac-824a-0f07157b79b9', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:13:00,030 - [MoveServiceEventReceiver] - INFO - 抓取指令 5bca95f2-5576-44ac-824a-0f07157b79b9 已被移动服务接受并加入队列
2025-07-28 12:13:00,123 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.021秒 - 玩家: VirtualPlayerID0577, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:13:00,123 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0577] = 4
2025-07-28 12:13:00,166 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:13:00,168 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:13:00,170 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:13:00,173 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:13:00,545 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675980.0247886, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5bca95f2-5576-44ac-824a-0f07157b79b9'}, pending_retry_player: None
2025-07-28 12:13:00,546 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:13:00,716 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已完成游戏，总游戏次数: 4
2025-07-28 12:13:00,716 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 十里桃花 已退休。
2025-07-28 12:13:00,718 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 新玩家 坐上王座 已补充到活跃池。
2025-07-28 12:13:01,063 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675980.0247886, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5bca95f2-5576-44ac-824a-0f07157b79b9'}, pending_retry_player: None
2025-07-28 12:13:01,064 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:13:01,173 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:13:01,195 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 8 个玩家, 成功更新 8 条记录.
2025-07-28 12:13:01,570 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675980.0247886, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5bca95f2-5576-44ac-824a-0f07157b79b9'}, pending_retry_player: None
2025-07-28 12:13:01,571 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:13:02,089 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675980.0247886, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5bca95f2-5576-44ac-824a-0f07157b79b9'}, pending_retry_player: None
2025-07-28 12:13:02,089 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:13:02,199 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF42E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 47
2025-07-28 12:13:02,205 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF42E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:13:02,206 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:13:02,607 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0578', 'name': '十里桃花', 'start_time': 1753675980.0247886, 'target_id': '1', 'target_object': '卡通公仔', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '5bca95f2-5576-44ac-824a-0f07157b79b9'}, pending_retry_player: None
2025-07-28 12:13:02,607 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:13:02,655 - [MainThread] - INFO - 收到终止信号，标志置 False，主线程将退出并在 finally 做清理
2025-07-28 12:13:02,656 - [MainThread] - INFO - 进入清理阶段...
2025-07-28 12:13:02,659 - [MainThread] - INFO - [清理] 开始执行清理操作
2025-07-28 12:13:02,659 - [MainThread] - DEBUG - [清理] 设置 stop_flag['running'] = False
2025-07-28 12:13:02,659 - [MainThread] - INFO - [清理] 阶段1: 停止所有外部输入...
2025-07-28 12:13:02,660 - [MainThread] - DEBUG - [清理] 停止消息获取线程...
2025-07-28 12:13:02,660 - [MainThread] - DEBUG - [消息线程] 尝试停止消息线程
2025-07-28 12:13:02,660 - [MainThread] - DEBUG - [消息线程] 等待消息线程结束
2025-07-28 12:13:02,674 - [DetectionReceiver] - INFO - 已断开检测服务器连接
2025-07-28 12:13:02,678 - [DetectionReceiver] - INFO - 检测数据接收器已停止
2025-07-28 12:13:02,732 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 管理器线程已停止。
2025-07-28 12:13:03,048 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5bca95f2-5576-44ac-824a-0f07157b79b9', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:13:03,049 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=5bca95f2-5576-44ac-824a-0f07157b79b9
2025-07-28 12:13:03,051 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:13:03,051 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0578, player_name=十里桃花, requested_object_id=1
2025-07-28 12:13:03,052 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:13:03,052 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '5bca95f2-5576-44ac-824a-0f07157b79b9', 'player_id': 'VirtualPlayerID0578', 'player_name': '十里桃花', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:13:03,052 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已停止。
2025-07-28 12:13:03,053 - [MoveServiceEventReceiver] - INFO - 正在断开与移动服务的连接...
2025-07-28 12:13:03,053 - [MoveServiceEventReceiver] - INFO - 与移动服务的连接已断开。
2025-07-28 12:13:04,222 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:13:04,223 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:13:04,224 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:13:04,226 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:13:04,666 - [MainThread] - WARNING - [消息线程] 消息线程未在2秒内结束
2025-07-28 12:13:04,669 - [MainThread] - DEBUG - [消息线程] 消息队列已清空
2025-07-28 12:13:04,670 - [MainThread] - DEBUG - [清理] 停止移动服务客户端...
2025-07-28 12:13:04,671 - [MainThread] - INFO - 正在停止移动服务客户端...
2025-07-28 12:13:04,671 - [MainThread] - INFO - 正在断开与移动服务的连接...
2025-07-28 12:13:04,672 - [MainThread] - INFO - 与移动服务的连接已断开。
2025-07-28 12:13:04,672 - [MainThread] - INFO - 移动服务客户端已停止。
2025-07-28 12:13:04,673 - [MainThread] - DEBUG - [清理] 断开检测服务器连接...
2025-07-28 12:13:04,674 - [MainThread] - INFO - 已断开检测服务器连接
2025-07-28 12:13:04,674 - [MainThread] - INFO - [清理] 阶段2: 停止独立进程...
2025-07-28 12:13:04,675 - [MainThread] - DEBUG - [清理] 关闭GUI通信队列...
2025-07-28 12:13:04,676 - [MainThread] - DEBUG - [清理] 停止Web显示进程...
2025-07-28 12:13:04,918 - [MainThread] - INFO - [清理] 阶段3: 等待内部线程结束...
2025-07-28 12:13:04,918 - [MainThread] - DEBUG - [清理] 等待主循环监控线程结束…
2025-07-28 12:13:06,255 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4C40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 48
2025-07-28 12:13:06,257 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EFFFDF4C40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:13:06,257 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:13:06,258 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:13:06,259 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:13:06,259 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:13:06,260 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:13:06,261 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:13:06,261 - [MessagePollThread] - DEBUG - [消息线程] 消息获取线程结束
2025-07-28 12:13:06,262 - [MessagePollThread] - DEBUG - [消息流] 停止健康检查
2025-07-28 12:13:06,263 - [MessagePollThread] - DEBUG - [健康检查] 收到停止信号
2025-07-28 12:13:06,263 - [MessagePollThread] - DEBUG - [健康检查] 等待线程结束
2025-07-28 12:13:06,923 - [MainThread] - WARNING - [清理] 主循环监控线程未在2秒内结束
2025-07-28 12:13:06,923 - [MainThread] - INFO - [清理] 阶段4: 最终数据同步和资源释放...
2025-07-28 12:13:06,924 - [MainThread] - DEBUG - [清理] 停止数据库同步管理器...
2025-07-28 12:13:06,925 - [MainThread] - INFO - 停止数据库同步线程
2025-07-28 12:13:07,286 - [MainThread] - INFO - 数据库同步线程已停止
2025-07-28 12:13:07,286 - [MainThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetSceneItemList', 'requestId': 558, 'requestData': {'sceneName': '场景1'}}}
2025-07-28 12:13:07,292 - [MainThread] - DEBUG - Response received {'d': {'requestId': 558, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetSceneItemList', 'responseData': {'sceneItems': [{'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 58, 'sceneItemIndex': 0, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '没抓到特效', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'color_source_v3', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 49, 'sceneItemIndex': 1, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '色源', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'vlc_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 40, 'sceneItemIndex': 2, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 8, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 540.0, 'positionY': 1920.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '背景视频', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'image_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 62, 'sceneItemIndex': 3, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '临时图像', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'dshow_input', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 46, 'sceneItemIndex': 4, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1089.0, 'positionX': 1089.0, 'positionY': -16.0, 'rotation': 90.0, 'scaleX': 1.0083333253860474, 'scaleY': 1.0083333253860474, 'sourceHeight': 1080.0, 'sourceWidth': 1920.0, 'width': 1936.0}, 'sourceName': '主摄像头', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 1, 'sceneItemIndex': 5, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 116.22856903076172, 'positionX': 214.0, 'positionY': 44.77142333984375, 'rotation': 0.0, 'scaleX': 1.6139534711837769, 'scaleY': 1.6142857074737549, 'sourceHeight': 72.0, 'sourceWidth': 444.0, 'width': 716.5953369140625}, 'sourceName': '大标题', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 60, 'sceneItemIndex': 6, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 14.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4541666507720947, 'scaleY': 1.4546159505844116, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 53, 'sceneItemIndex': 7, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': -4.0, 'rotation': 0.0, 'scaleX': 1.5, 'scaleY': 1.5003879070281982, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效2', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 56, 'sceneItemIndex': 8, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 21.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4416667222976685, 'scaleY': 1.44142746925354, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效3', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 57, 'sceneItemIndex': 9, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 22.0, 'positionY': -163.0, 'rotation': 0.0, 'scaleX': 1.4375, 'scaleY': 1.4375485181808472, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效4', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 34, 'sceneItemIndex': 10, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 164.0, 'positionX': 454.0, 'positionY': 132.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 164.0, 'sourceWidth': 626.0, 'width': 626.0}, 'sourceName': '说明1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'monitor_capture', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 41, 'sceneItemIndex': 11, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 734.0, 'rotation': 0.0, 'scaleX': 0.6026041507720947, 'scaleY': 0.6027777791023254, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '显示器采集', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'browser_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 50, 'sceneItemIndex': 12, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '浏览器显示', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}]}}, 'op': 7}
2025-07-28 12:13:07,293 - [MainThread] - WARNING - 在场景 '场景1' 中未找到源 '抓中特效'
2025-07-28 12:13:07,293 - [MainThread] - DEBUG - [清理] 强制隐藏 OBS 视频源
2025-07-28 12:13:07,294 - [MainThread] - DEBUG - [清理] 停止健康检查线程...
2025-07-28 12:13:07,294 - [MainThread] - DEBUG - [健康检查] 收到停止信号
2025-07-28 12:13:07,295 - [MainThread] - DEBUG - [健康检查] 等待线程结束
2025-07-28 12:13:08,269 - [MessagePollThread] - INFO - 健康检查线程已停止
2025-07-28 12:13:09,302 - [MainThread] - INFO - 健康检查线程已停止
2025-07-28 12:13:09,309 - [MainThread] - DEBUG - [清理] 已获取队列锁，执行最终数据同步...
2025-07-28 12:13:09,324 - [MainThread] - INFO - 数据库操作: 更新队列完成，耗时 0.014秒
2025-07-28 12:13:11,328 - [MainThread] - WARNING - 获取快照时未能获取 queue_lock，使用上次快照队列数据
2025-07-28 12:13:11,328 - [MainThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:13:11,337 - [MainThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 8 个玩家, 成功更新 8 条记录.
2025-07-28 12:13:11,340 - [MainThread] - INFO - [清理] 最终数据同步已完成
2025-07-28 12:13:11,342 - [MainThread] - DEBUG - [清理] 关闭数据库会话...
2025-07-28 12:13:11,354 - [MainThread] - INFO - 已关闭会话，ID: 1
2025-07-28 12:13:11,354 - [MainThread] - INFO - [清理] 数据库会话已关闭
2025-07-28 12:13:11,354 - [MainThread] - DEBUG - [清理] 队列锁已释放
2025-07-28 12:13:11,355 - [MainThread] - INFO - [清理] 清理操作成功
2025-07-28 12:13:11,419 - [MainThread] - INFO - 程序退出
