2025-07-28 12:37:13,323 - [MainThread] - INFO - 日志系统初始化完成
2025-07-28 12:37:13,323 - [MainThread] - INFO - 应用程序启动
2025-07-28 12:37:14,085 - [MainThread] - INFO - 信号处理器设置完成（仅置标志）
2025-07-28 12:37:14,085 - [MainThread] - INFO - 信号处理器已设置(使用共享状态字典)
2025-07-28 12:37:16,272 - [MainThread] - INFO - Web显示进程启动成功
2025-07-28 12:37:16,444 - [MainThread] - INFO - GUI 进程已通过 multiprocessing.Process 启动, PID: 17196
2025-07-28 12:37:16,445 - [MainThread] - INFO - 每个会话最大免费游戏次数: 1
2025-07-28 12:37:16,447 - [MainThread] - INFO - 初始化全局 OBSController
2025-07-28 12:37:16,449 - [MainThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-28 12:37:16,449 - [MainThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-28 12:37:16,480 - [MainThread] - INFO - Successfully identified ReqClient with the server using RPC version:1
2025-07-28 12:37:16,480 - [MainThread] - INFO - 成功连接到OBS WebSocket
2025-07-28 12:37:16,481 - [MainThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetCurrentProgramScene', 'requestId': 582}}
2025-07-28 12:37:16,482 - [MainThread] - DEBUG - Response received {'d': {'requestId': 582, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetCurrentProgramScene', 'responseData': {'currentProgramSceneName': '场景1'}}, 'op': 7}
2025-07-28 12:37:16,484 - [MainThread] - INFO - 当前场景: 场景1
2025-07-28 12:37:16,484 - [MainThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetSceneItemList', 'requestId': 672, 'requestData': {'sceneName': '场景1'}}}
2025-07-28 12:37:16,496 - [MainThread] - DEBUG - Response received {'d': {'requestId': 672, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetSceneItemList', 'responseData': {'sceneItems': [{'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 58, 'sceneItemIndex': 0, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '没抓到特效', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'color_source_v3', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 49, 'sceneItemIndex': 1, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '色源', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'vlc_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 40, 'sceneItemIndex': 2, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 8, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 540.0, 'positionY': 1920.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '背景视频', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'image_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 62, 'sceneItemIndex': 3, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '临时图像', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'dshow_input', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 46, 'sceneItemIndex': 4, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1089.0, 'positionX': 1089.0, 'positionY': -16.0, 'rotation': 90.0, 'scaleX': 1.0083333253860474, 'scaleY': 1.0083333253860474, 'sourceHeight': 1080.0, 'sourceWidth': 1920.0, 'width': 1936.0}, 'sourceName': '主摄像头', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 1, 'sceneItemIndex': 5, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 116.22856903076172, 'positionX': 214.0, 'positionY': 44.77142333984375, 'rotation': 0.0, 'scaleX': 1.6139534711837769, 'scaleY': 1.6142857074737549, 'sourceHeight': 72.0, 'sourceWidth': 444.0, 'width': 716.5953369140625}, 'sourceName': '大标题', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 60, 'sceneItemIndex': 6, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 14.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4541666507720947, 'scaleY': 1.4546159505844116, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 53, 'sceneItemIndex': 7, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': -4.0, 'rotation': 0.0, 'scaleX': 1.5, 'scaleY': 1.5003879070281982, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效2', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 56, 'sceneItemIndex': 8, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 21.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4416667222976685, 'scaleY': 1.44142746925354, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效3', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 57, 'sceneItemIndex': 9, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 22.0, 'positionY': -163.0, 'rotation': 0.0, 'scaleX': 1.4375, 'scaleY': 1.4375485181808472, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效4', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 34, 'sceneItemIndex': 10, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 164.0, 'positionX': 454.0, 'positionY': 132.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 164.0, 'sourceWidth': 626.0, 'width': 626.0}, 'sourceName': '说明1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'monitor_capture', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 41, 'sceneItemIndex': 11, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 734.0, 'rotation': 0.0, 'scaleX': 0.6026041507720947, 'scaleY': 0.6027777791023254, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '显示器采集', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'browser_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 50, 'sceneItemIndex': 12, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '浏览器显示', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}]}}, 'op': 7}
2025-07-28 12:37:16,498 - [MainThread] - INFO - 完成源缓存，已缓存 5 个源的场景项ID
2025-07-28 12:37:16,498 - [MainThread] - INFO - 检查历史会话记录...
2025-07-28 12:37:16,500 - [MainThread] - INFO - 未找到历史会话记录，将创建新会话
2025-07-28 12:37:16,526 - [MainThread] - INFO - 已创建新会话，ID: 1
2025-07-28 12:37:16,528 - [MainThread] - INFO - 当前使用会话 ID: 1
2025-07-28 12:37:16,530 - [MainThread] - DEBUG - 当前会话ID已设置为: 1
2025-07-28 12:37:16,542 - [MainThread] - INFO - 会话开始时间: 2025-07-28T12:37:16.501336
2025-07-28 12:37:16,542 - [MainThread] - INFO - 使用新创建的会话，初始化空数据结构
2025-07-28 12:37:16,543 - [MainThread] - INFO - 初始化全局数据库同步管理器
2025-07-28 12:37:16,544 - [MainThread] - INFO - 启动数据库同步线程，同步间隔: 10秒
2025-07-28 12:37:16,544 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:37:16,545 - [MainThread] - INFO - 会话初始化完成，新会话，会话ID: 1
2025-07-28 12:37:16,545 - [DBSyncThread] - DEBUG - [全量同步] player_info 为空，跳过 comments_after_game 同步。
2025-07-28 12:37:16,570 - [MainThread] - INFO - 成功连接到移动服务 localhost:5556
2025-07-28 12:37:16,577 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已启动。
2025-07-28 12:37:16,578 - [MainThread] - INFO - 移动服务客户端已启动，连接到: localhost:5556
2025-07-28 12:37:16,583 - [DetectionReceiver] - INFO - 检测数据接收器启动
2025-07-28 12:37:16,584 - [MainThread] - INFO - 检测数据接收器线程已启动
2025-07-28 12:37:16,596 - [MainThread] - INFO - [虚拟玩家] 成功加载 785 个虚拟玩家，起始索引: 484
2025-07-28 12:37:16,597 - [MainThread] - INFO - [虚拟玩家] 管理器启动...
2025-07-28 12:37:16,598 - [MainThread] - INFO - 虚拟玩家管理器已启动
2025-07-28 12:37:16,599 - [MainThread] - INFO - 状态更新处理线程已启动
2025-07-28 12:37:16,602 - [StatusUpdateThread] - INFO - [重试机制] 检测到移动服务端已恢复，若有pending_retry_player将立即重试。
2025-07-28 12:37:16,607 - [MainThread] - INFO - GUI的FPS发送线程已启动
2025-07-28 12:37:16,608 - [MainThread] - INFO - 游戏处理线程已启动
2025-07-28 12:37:16,609 - [MainThread] - INFO - 主循环监控线程已启动
2025-07-28 12:37:16,609 - [MainThread] - INFO - [消息线程] 启动消息获取后台线程
2025-07-28 12:37:16,610 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:37:16,610 - [MessagePollThread] - DEBUG - [消息线程] 开始运行消息获取线程
2025-07-28 12:37:16,611 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:37:16,610 - [MainThread] - INFO - [消息线程] 消息获取线程已启动
2025-07-28 12:37:16,611 - [MessagePollThread] - DEBUG - [消息流] 启动健康检查
2025-07-28 12:37:16,612 - [MainThread] - INFO - 消息获取后台线程已启动，开始接收消息...
2025-07-28 12:37:16,612 - [MainThread] - INFO - 开始处理消息，按Ctrl+C停止...
2025-07-28 12:37:16,612 - [HealthCheckThread] - DEBUG - [健康检查] 任务开始
2025-07-28 12:37:16,613 - [MessagePollThread] - INFO - 健康检查线程已启动，间隔: 30秒
2025-07-28 12:37:16,613 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:37:16,614 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:37:16,614 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 3.5 秒
2025-07-28 12:37:16,615 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:37:16,615 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:37:16,615 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:37:16,616 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:37:16,616 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 1
2025-07-28 12:37:16,618 - [DetectionReceiver] - INFO - 成功连接到检测服务器 localhost:5555
2025-07-28 12:37:16,617 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:37:16,623 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:16,624 - [DetectionReceiver] - DEBUG - 已发送订阅命令
2025-07-28 12:37:16,629 - [DetectionReceiver] - INFO - 收到订阅确认: Subscription successful for client 1
2025-07-28 12:37:16,662 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:16,665 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:16,716 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:16,763 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:16,811 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:16,854 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:16,903 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:16,951 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:16,999 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,045 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,096 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,125 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:37:17,127 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:37:17,144 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,188 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,236 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,284 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,330 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,379 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,426 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,474 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,516 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,565 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,614 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,642 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:37:17,642 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:37:17,661 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,705 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,755 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,803 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,849 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,896 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,944 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:17,989 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,038 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,082 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,131 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,159 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-28 12:37:18,161 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:37:18,176 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,222 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,270 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,317 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,362 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,408 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,457 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,505 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,550 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,596 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,609 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(3)，添加虚拟玩家。
2025-07-28 12:37:18,611 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['星坠潭(0次)', '🌈追梦少年(0次)', '微笑与不微笑(0次)', '念旧人(0次)', '郭靖的现代传奇(0次)']。已选择: 星坠潭
2025-07-28 12:37:18,618 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 星坠潭 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:18,673 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {}, pending_retry_player: None
2025-07-28 12:37:18,675 - [GameProcessThread] - INFO - [游戏线程] 玩家 星坠潭(VirtualPlayerID0485) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:37:18,676 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 星坠潭(VirtualPlayerID0485) 确保抓中, z_offset_extra=0
2025-07-28 12:37:18,679 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=62242f60-65a5-4c6a-8c79-9674270a6967, Player=星坠潭, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:37:18,682 - [GameProcessThread] - INFO - [游戏线程] 玩家 星坠潭(VirtualPlayerID0485) 抓取指令已发送到移动服务，命令ID: 62242f60-65a5-4c6a-8c79-9674270a6967
2025-07-28 12:37:18,690 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,705 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD7F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 1
2025-07-28 12:37:18,710 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD7F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:18,711 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:18,734 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:37:18,737 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,740 - [MoveServiceEventReceiver] - INFO - 抓取指令 62242f60-65a5-4c6a-8c79-9674270a6967 已被移动服务接受并加入队列
2025-07-28 12:37:18,784 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,831 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,876 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,925 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:18,972 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,019 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,066 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,080 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0485 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:19,083 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0485 订单状态，但该玩家在会话 1 中不存在
2025-07-28 12:37:19,114 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,158 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,190 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967'}, pending_retry_player: None
2025-07-28 12:37:19,192 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:37:19,207 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,250 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,298 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,344 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,390 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,440 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,484 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,535 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,579 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,627 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,673 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,704 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967'}, pending_retry_player: None
2025-07-28 12:37:19,709 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:37:19,722 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,767 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,815 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,863 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,909 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:19,956 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,005 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,051 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,097 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,142 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,189 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,221 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967'}, pending_retry_player: None
2025-07-28 12:37:20,222 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-28 12:37:20,237 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,284 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,332 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,377 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,425 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,472 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,520 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,567 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,614 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-28 12:37:20,628 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 星坠潭 已完成游戏，总游戏次数: 1
2025-07-28 12:37:20,629 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 星坠潭 下次将以付费状态排队。
2025-07-28 12:37:20,629 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(7)，添加虚拟玩家。
2025-07-28 12:37:20,630 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['🌈追梦少年(0次)', '微笑与不微笑(0次)', '念旧人(0次)', '郭靖的现代传奇(0次)', '星坠潭(1次)']。已选择: 🌈追梦少年
2025-07-28 12:37:20,630 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 🌈追梦少年 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:20,723 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:20,724 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:37:20,724 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:20,729 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:20,739 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967'}, pending_retry_player: None
2025-07-28 12:37:20,741 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:21,257 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967'}, pending_retry_player: None
2025-07-28 12:37:21,259 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:21,617 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:37:21,619 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=62242f60-65a5-4c6a-8c79-9674270a6967
2025-07-28 12:37:21,624 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:37:21,625 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0485, player_name=星坠潭, requested_object_id=1
2025-07-28 12:37:21,626 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:37:21,626 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'player_id': 'VirtualPlayerID0485', 'player_name': '星坠潭', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:37:21,626 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 星坠潭(VirtualPlayerID0485), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 62242f60-65a5-4c6a-8c79-9674270a6967, 消息: 物品_1
2025-07-28 12:37:21,628 - [StatusUpdateThread] - INFO - 玩家 星坠潭 本次是第 1 次成功抓取。
2025-07-28 12:37:21,628 - [StatusUpdateThread] - INFO - 为玩家 星坠潭 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:37:21,630 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:37:21,631 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 91, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:37:21,639 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 91, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:21,773 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:21,777 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:22,284 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:22,286 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:22,645 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(1) < 目标(7)，添加虚拟玩家。
2025-07-28 12:37:22,645 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['微笑与不微笑(0次)', '念旧人(0次)', '郭靖的现代传奇(0次)', '星坠潭(1次)']。已选择: 微笑与不微笑
2025-07-28 12:37:22,647 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 微笑与不微笑 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:22,773 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090ADC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 2
2025-07-28 12:37:22,781 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090ADC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:22,782 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:22,802 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:22,807 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:23,324 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:23,326 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:23,840 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:23,842 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:24,360 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:24,362 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:24,656 - [Thread-1] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 396, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:37:24,657 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(3)，添加虚拟玩家。
2025-07-28 12:37:24,667 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['念旧人(0次)', '郭靖的现代传奇(0次)', '星坠潭(1次)']。已选择: 念旧人
2025-07-28 12:37:24,668 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 念旧人 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:24,669 - [Thread-1] - DEBUG - Response received {'d': {'requestId': 396, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:24,796 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:24,803 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:37:24,807 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:24,816 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:24,877 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:24,879 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:25,394 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:25,396 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:25,912 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677438.6759753, 'target_id': '1', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:25,915 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:26,070 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '62242f60-65a5-4c6a-8c79-9674270a6967', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:37:26,076 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=62242f60-65a5-4c6a-8c79-9674270a6967
2025-07-28 12:37:26,080 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:37:26,082 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0485, player_name=星坠潭, requested_object_id=1
2025-07-28 12:37:26,083 - [MoveServiceEventReceiver] - INFO - 移动服务请求 62242f60-65a5-4c6a-8c79-9674270a6967 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:37:26,085 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 星坠潭(VirtualPlayerID0485)
2025-07-28 12:37:26,090 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:37:26,091 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 星坠潭(VirtualPlayerID0485), 结果: 物品_1, 订单: None
2025-07-28 12:37:26,093 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:37:26,094 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:37:26,190 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.041秒 - 玩家: VirtualPlayerID0485, 结果: 物品_1, 订单: NULL
2025-07-28 12:37:26,191 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0485] = 1
2025-07-28 12:37:26,432 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {}, pending_retry_player: None
2025-07-28 12:37:26,434 - [GameProcessThread] - INFO - [游戏线程] 玩家 🌈追梦少年(VirtualPlayerID0486) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:37:26,436 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 🌈追梦少年(VirtualPlayerID0486) 确保抓中, z_offset_extra=0
2025-07-28 12:37:26,442 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=115a030c-d177-4a9f-baa6-bc272f9019b1, Player=🌈追梦少年, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:37:26,445 - [GameProcessThread] - INFO - [游戏线程] 玩家 🌈追梦少年(VirtualPlayerID0486) 抓取指令已发送到移动服务，命令ID: 115a030c-d177-4a9f-baa6-bc272f9019b1
2025-07-28 12:37:26,447 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:37:26,458 - [MoveServiceEventReceiver] - INFO - 抓取指令 115a030c-d177-4a9f-baa6-bc272f9019b1 已被移动服务接受并加入队列
2025-07-28 12:37:26,682 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 🌈追梦少年 已完成游戏，总游戏次数: 1
2025-07-28 12:37:26,685 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 🌈追梦少年 下次将以付费状态排队。
2025-07-28 12:37:26,687 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(6)，添加虚拟玩家。
2025-07-28 12:37:26,690 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['郭靖的现代传奇(0次)', '星坠潭(1次)', '🌈追梦少年(1次)']。已选择: 郭靖的现代传奇
2025-07-28 12:37:26,691 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 郭靖的现代传奇 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:26,698 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0486 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:26,711 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0486 订单状态，但该玩家在会话 1 中不存在
2025-07-28 12:37:26,857 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6BB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 3
2025-07-28 12:37:26,860 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6BB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:37:26,861 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:37:26,862 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:37:26,863 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:37:26,863 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=0.0s, 心跳间隔=1s
2025-07-28 12:37:26,864 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:37:26,868 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.027秒
2025-07-28 12:37:26,868 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:37:26,874 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-28 12:37:26,951 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1'}, pending_retry_player: None
2025-07-28 12:37:26,952 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:27,468 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1'}, pending_retry_player: None
2025-07-28 12:37:27,468 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:27,878 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:37:27,881 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:37:27,882 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:37:27,883 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:37:27,884 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:37:27,885 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:37:27,885 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 2
2025-07-28 12:37:27,886 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:37:27,887 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:27,900 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:27,973 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1'}, pending_retry_player: None
2025-07-28 12:37:27,976 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:28,491 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1'}, pending_retry_player: None
2025-07-28 12:37:28,492 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:28,708 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(3) < 目标(4)，添加虚拟玩家。
2025-07-28 12:37:28,710 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['星坠潭(1次)', '🌈追梦少年(1次)']。已选择: 星坠潭
2025-07-28 12:37:28,712 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 星坠潭 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:29,005 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1'}, pending_retry_player: None
2025-07-28 12:37:29,009 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:29,455 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:37:29,457 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=115a030c-d177-4a9f-baa6-bc272f9019b1
2025-07-28 12:37:29,458 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:37:29,458 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0486, player_name=🌈追梦少年, requested_object_id=2
2025-07-28 12:37:29,459 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:37:29,459 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'player_id': 'VirtualPlayerID0486', 'player_name': '🌈追梦少年', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:37:29,459 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 🌈追梦少年(VirtualPlayerID0486), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: 115a030c-d177-4a9f-baa6-bc272f9019b1, 消息: 物品_2
2025-07-28 12:37:29,460 - [StatusUpdateThread] - INFO - 玩家 🌈追梦少年 本次是第 1 次成功抓取。
2025-07-28 12:37:29,460 - [StatusUpdateThread] - INFO - 为玩家 🌈追梦少年 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:37:29,461 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:37:29,461 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 565, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:37:29,462 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 565, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:29,518 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:29,521 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:29,948 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AEB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 4
2025-07-28 12:37:29,954 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AEB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:29,955 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:30,039 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:30,044 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:30,559 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:30,561 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:30,729 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-28 12:37:30,736 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['🌈追梦少年(1次)']。已选择: 🌈追梦少年
2025-07-28 12:37:30,740 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 🌈追梦少年 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:31,076 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:31,078 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:31,598 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:31,598 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:31,960 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:31,963 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:37:31,965 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:31,969 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:32,104 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:32,112 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:32,467 - [Thread-2] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 490, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:37:32,472 - [Thread-2] - DEBUG - Response received {'d': {'requestId': 490, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:32,625 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:32,633 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:33,153 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:33,155 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:33,670 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677446.4343288, 'target_id': '2', 'target_object': '独角兽', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:37:33,676 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:33,922 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '115a030c-d177-4a9f-baa6-bc272f9019b1', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:37:33,922 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=115a030c-d177-4a9f-baa6-bc272f9019b1
2025-07-28 12:37:33,924 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:37:33,924 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0486, player_name=🌈追梦少年, requested_object_id=2
2025-07-28 12:37:33,924 - [MoveServiceEventReceiver] - INFO - 移动服务请求 115a030c-d177-4a9f-baa6-bc272f9019b1 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:37:33,925 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 🌈追梦少年(VirtualPlayerID0486)
2025-07-28 12:37:33,925 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:37:33,925 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 🌈追梦少年(VirtualPlayerID0486), 结果: 物品_2, 订单: None
2025-07-28 12:37:33,926 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:37:33,927 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:37:34,018 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809239D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 5
2025-07-28 12:37:34,021 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809239D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:34,022 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:34,048 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.047秒 - 玩家: VirtualPlayerID0486, 结果: 物品_2, 订单: NULL
2025-07-28 12:37:34,049 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0486] = 1
2025-07-28 12:37:34,192 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: None
2025-07-28 12:37:34,195 - [GameProcessThread] - INFO - [游戏线程] 玩家 微笑与不微笑(VirtualPlayerID0487) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:37:34,197 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 微笑与不微笑(VirtualPlayerID0487) 确保抓中, z_offset_extra=0
2025-07-28 12:37:34,204 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=a4ea60ed-061f-4263-ac8a-cbe091fa0424, Player=微笑与不微笑, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:37:34,206 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:37:34,207 - [GameProcessThread] - INFO - [游戏线程] 玩家 微笑与不微笑(VirtualPlayerID0487) 抓取指令已发送到移动服务，命令ID: a4ea60ed-061f-4263-ac8a-cbe091fa0424
2025-07-28 12:37:34,211 - [MoveServiceEventReceiver] - INFO - 抓取指令 a4ea60ed-061f-4263-ac8a-cbe091fa0424 已被移动服务接受并加入队列
2025-07-28 12:37:34,585 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0487 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:34,590 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0487 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:34,712 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424'}, pending_retry_player: None
2025-07-28 12:37:34,713 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:34,760 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 微笑与不微笑 已完成游戏，总游戏次数: 1
2025-07-28 12:37:34,762 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 微笑与不微笑 下次将以付费状态排队。
2025-07-28 12:37:34,762 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(6)，添加虚拟玩家。
2025-07-28 12:37:34,763 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['微笑与不微笑(1次)']。已选择: 微笑与不微笑
2025-07-28 12:37:34,765 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 微笑与不微笑 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:35,217 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424'}, pending_retry_player: None
2025-07-28 12:37:35,220 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:35,739 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424'}, pending_retry_player: None
2025-07-28 12:37:35,747 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:36,038 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:36,044 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:37:36,045 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:36,057 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:36,259 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424'}, pending_retry_player: None
2025-07-28 12:37:36,264 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:36,779 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:37:36,783 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424'}, pending_retry_player: None
2025-07-28 12:37:36,787 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 凌霜月 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:36,794 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:37,177 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:37:37,178 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=a4ea60ed-061f-4263-ac8a-cbe091fa0424
2025-07-28 12:37:37,179 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:37:37,179 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0487, player_name=微笑与不微笑, requested_object_id=5
2025-07-28 12:37:37,180 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:37:37,180 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'player_id': 'VirtualPlayerID0487', 'player_name': '微笑与不微笑', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:37:37,181 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 微笑与不微笑(VirtualPlayerID0487), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: a4ea60ed-061f-4263-ac8a-cbe091fa0424, 消息: 物品_5
2025-07-28 12:37:37,183 - [StatusUpdateThread] - INFO - 玩家 微笑与不微笑 本次是第 1 次成功抓取。
2025-07-28 12:37:37,184 - [StatusUpdateThread] - INFO - 为玩家 微笑与不微笑 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:37:37,186 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:37:37,187 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 393, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:37:37,190 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 393, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:37,195 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.033秒
2025-07-28 12:37:37,197 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:37:37,232 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 6 个玩家, 成功更新 6 条记录.
2025-07-28 12:37:37,313 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:37,314 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:37,831 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:37,832 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:38,106 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934130>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 6
2025-07-28 12:37:38,113 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934130>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:37:38,118 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:37:38,120 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:37:38,122 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:37:38,122 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.3s, 心跳间隔=1s
2025-07-28 12:37:38,123 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:37:38,124 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:37:38,126 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:37:38,339 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:38,348 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:38,797 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:37:38,798 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 子葵 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:38,861 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:38,867 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:39,130 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:37:39,133 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:37:39,134 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:37:39,135 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:37:39,136 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:37:39,137 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:37:39,138 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 3
2025-07-28 12:37:39,139 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:37:39,139 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:39,154 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:39,384 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:39,389 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:39,901 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:39,908 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:40,200 - [Thread-3] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 897, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:37:40,206 - [Thread-3] - DEBUG - Response received {'d': {'requestId': 897, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:40,421 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:40,426 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:40,941 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:40,944 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:41,193 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6E20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 7
2025-07-28 12:37:41,196 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6E20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:41,197 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:41,460 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': 1753677454.1941476, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:37:41,466 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:41,648 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'a4ea60ed-061f-4263-ac8a-cbe091fa0424', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:37:41,654 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=a4ea60ed-061f-4263-ac8a-cbe091fa0424
2025-07-28 12:37:41,656 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:37:41,658 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0487, player_name=微笑与不微笑, requested_object_id=5
2025-07-28 12:37:41,661 - [MoveServiceEventReceiver] - INFO - 移动服务请求 a4ea60ed-061f-4263-ac8a-cbe091fa0424 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:37:41,662 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 微笑与不微笑(VirtualPlayerID0487)
2025-07-28 12:37:41,666 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:37:41,667 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 微笑与不微笑(VirtualPlayerID0487), 结果: 物品_5, 订单: None
2025-07-28 12:37:41,671 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:37:41,672 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:37:41,817 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.044秒 - 玩家: VirtualPlayerID0487, 结果: 物品_5, 订单: NULL
2025-07-28 12:37:41,818 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0487] = 1
2025-07-28 12:37:41,977 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:37:41,985 - [GameProcessThread] - INFO - [游戏线程] 玩家 念旧人(VirtualPlayerID0488) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:37:41,988 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 念旧人(VirtualPlayerID0488) 确保抓中, z_offset_extra=0
2025-07-28 12:37:41,992 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=1b99de59-be8f-4ae9-bb14-ea27464ee670, Player=念旧人, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:37:41,994 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:37:41,995 - [GameProcessThread] - INFO - [游戏线程] 玩家 念旧人(VirtualPlayerID0488) 抓取指令已发送到移动服务，命令ID: 1b99de59-be8f-4ae9-bb14-ea27464ee670
2025-07-28 12:37:42,000 - [MoveServiceEventReceiver] - INFO - 抓取指令 1b99de59-be8f-4ae9-bb14-ea27464ee670 已被移动服务接受并加入队列
2025-07-28 12:37:42,354 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0488 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:42,361 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0488 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:42,513 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670'}, pending_retry_player: None
2025-07-28 12:37:42,513 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:42,829 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 念旧人 已完成游戏，总游戏次数: 1
2025-07-28 12:37:42,829 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 念旧人 下次将以付费状态排队。
2025-07-28 12:37:43,018 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670'}, pending_retry_player: None
2025-07-28 12:37:43,020 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:43,206 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:43,213 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:37:43,215 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:43,226 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:43,537 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670'}, pending_retry_player: None
2025-07-28 12:37:43,544 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:44,060 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670'}, pending_retry_player: None
2025-07-28 12:37:44,061 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:44,567 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670'}, pending_retry_player: None
2025-07-28 12:37:44,569 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:45,023 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:37:45,025 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=1b99de59-be8f-4ae9-bb14-ea27464ee670
2025-07-28 12:37:45,026 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:37:45,026 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0488, player_name=念旧人, requested_object_id=1
2025-07-28 12:37:45,027 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:37:45,028 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'player_id': 'VirtualPlayerID0488', 'player_name': '念旧人', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:37:45,028 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 念旧人(VirtualPlayerID0488), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 1b99de59-be8f-4ae9-bb14-ea27464ee670, 消息: 物品_1
2025-07-28 12:37:45,031 - [StatusUpdateThread] - INFO - 玩家 念旧人 本次是第 1 次成功抓取。
2025-07-28 12:37:45,031 - [StatusUpdateThread] - INFO - 为玩家 念旧人 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:37:45,033 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:37:45,033 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 272, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:37:45,035 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 272, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:45,085 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:45,087 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:45,258 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934A30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 8
2025-07-28 12:37:45,259 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934A30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:45,260 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:45,589 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:45,589 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:46,094 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:46,101 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:46,614 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:46,621 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:46,629 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:37:46,632 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:37:46,634 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 33.5 秒
2025-07-28 12:37:46,636 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:37:47,135 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:47,145 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:47,274 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:47,283 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:37:47,287 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:47,301 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:47,465 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:37:47,474 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:37:47,650 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:47,656 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:48,043 - [Thread-4] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 38, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:37:48,047 - [Thread-4] - DEBUG - Response received {'d': {'requestId': 38, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:48,169 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:48,170 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:48,690 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:48,691 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:49,197 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': **********.9858313, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:37:49,199 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:49,340 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AEB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 9
2025-07-28 12:37:49,349 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AEB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:37:49,350 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:37:49,351 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:37:49,352 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:37:49,355 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.3s, 心跳间隔=1s
2025-07-28 12:37:49,361 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:37:49,365 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:37:49,369 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:37:49,561 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '1b99de59-be8f-4ae9-bb14-ea27464ee670', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:37:49,563 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=1b99de59-be8f-4ae9-bb14-ea27464ee670
2025-07-28 12:37:49,565 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:37:49,566 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0488, player_name=念旧人, requested_object_id=1
2025-07-28 12:37:49,566 - [MoveServiceEventReceiver] - INFO - 移动服务请求 1b99de59-be8f-4ae9-bb14-ea27464ee670 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:37:49,567 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 念旧人(VirtualPlayerID0488)
2025-07-28 12:37:49,570 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:37:49,570 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 念旧人(VirtualPlayerID0488), 结果: 物品_1, 订单: None
2025-07-28 12:37:49,571 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:37:49,571 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:37:49,715 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:37:49,717 - [GameProcessThread] - INFO - [游戏线程] 玩家 郭靖的现代传奇(VirtualPlayerID0489) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:37:49,718 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 郭靖的现代传奇(VirtualPlayerID0489) 确保抓中, z_offset_extra=0
2025-07-28 12:37:49,722 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=5f72e691-e887-4f42-a96e-feecf3554ea4, Player=郭靖的现代传奇, Target=3, Z_Offset_Extra=0.0
2025-07-28 12:37:49,724 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-28 12:37:49,726 - [GameProcessThread] - INFO - [游戏线程] 玩家 郭靖的现代传奇(VirtualPlayerID0489) 抓取指令已发送到移动服务，命令ID: 5f72e691-e887-4f42-a96e-feecf3554ea4
2025-07-28 12:37:49,729 - [MoveServiceEventReceiver] - INFO - 抓取指令 5f72e691-e887-4f42-a96e-feecf3554ea4 已被移动服务接受并加入队列
2025-07-28 12:37:50,018 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.018秒 - 玩家: VirtualPlayerID0488, 结果: 物品_1, 订单: NULL
2025-07-28 12:37:50,019 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0488] = 1
2025-07-28 12:37:50,036 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0489 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:50,043 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0489 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:37:50,236 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4'}, pending_retry_player: None
2025-07-28 12:37:50,241 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:50,377 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:37:50,378 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:37:50,378 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:37:50,379 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:37:50,379 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:37:50,379 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:37:50,380 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 4
2025-07-28 12:37:50,380 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:37:50,380 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:50,384 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:50,758 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4'}, pending_retry_player: None
2025-07-28 12:37:50,758 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:50,852 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 郭靖的现代传奇 已完成游戏，总游戏次数: 1
2025-07-28 12:37:50,852 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 郭靖的现代传奇 下次将以付费状态排队。
2025-07-28 12:37:50,853 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:37:50,853 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['念旧人(1次)', '郭靖的现代传奇(1次)']。已选择: 念旧人
2025-07-28 12:37:50,854 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 念旧人 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:51,265 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4'}, pending_retry_player: None
2025-07-28 12:37:51,267 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:51,784 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4'}, pending_retry_player: None
2025-07-28 12:37:51,785 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:52,287 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4'}, pending_retry_player: None
2025-07-28 12:37:52,290 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:52,431 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD670>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 10
2025-07-28 12:37:52,439 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD670>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:52,446 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:52,746 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-28 12:37:52,748 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=5f72e691-e887-4f42-a96e-feecf3554ea4
2025-07-28 12:37:52,749 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-28 12:37:52,749 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0489, player_name=郭靖的现代传奇, requested_object_id=3
2025-07-28 12:37:52,750 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-28 12:37:52,751 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'player_id': 'VirtualPlayerID0489', 'player_name': '郭靖的现代传奇', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-28 12:37:52,752 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 郭靖的现代传奇(VirtualPlayerID0489), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 5f72e691-e887-4f42-a96e-feecf3554ea4, 消息: 物品_3
2025-07-28 12:37:52,754 - [StatusUpdateThread] - INFO - 玩家 郭靖的现代传奇 本次是第 1 次成功抓取。
2025-07-28 12:37:52,755 - [StatusUpdateThread] - INFO - 为玩家 郭靖的现代传奇 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:37:52,760 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:37:52,768 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 16, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:37:52,772 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 16, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:52,793 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:52,795 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:53,310 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:53,312 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:53,831 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:53,833 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:54,348 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:54,351 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:54,457 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:54,463 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:37:54,466 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:54,473 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:54,868 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:54,872 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:54,885 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:37:54,887 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['郭靖的现代传奇(1次)']。已选择: 郭靖的现代传奇
2025-07-28 12:37:54,890 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 郭靖的现代传奇 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:37:55,385 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:55,387 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:55,776 - [Thread-5] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 15, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:37:55,786 - [Thread-5] - DEBUG - Response received {'d': {'requestId': 15, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:37:55,902 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:55,903 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:56,420 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:56,422 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:56,501 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809237C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 11
2025-07-28 12:37:56,511 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809237C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:37:56,513 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:37:56,926 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': 1753677469.717771, 'target_id': '3', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'game_result': '物品_3'}, pending_retry_player: None
2025-07-28 12:37:56,928 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:57,275 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5f72e691-e887-4f42-a96e-feecf3554ea4', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:37:57,277 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=5f72e691-e887-4f42-a96e-feecf3554ea4
2025-07-28 12:37:57,278 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:37:57,278 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0489, player_name=郭靖的现代传奇, requested_object_id=3
2025-07-28 12:37:57,280 - [MoveServiceEventReceiver] - INFO - 移动服务请求 5f72e691-e887-4f42-a96e-feecf3554ea4 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:37:57,281 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 郭靖的现代传奇(VirtualPlayerID0489)
2025-07-28 12:37:57,284 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-28 12:37:57,284 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 郭靖的现代传奇(VirtualPlayerID0489), 结果: 物品_3, 订单: None
2025-07-28 12:37:57,287 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:37:57,291 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:37:57,445 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:37:57,449 - [GameProcessThread] - INFO - [游戏线程] 玩家 星坠潭(VirtualPlayerID0485) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:37:57,450 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 星坠潭(VirtualPlayerID0485) 确保抓中, z_offset_extra=0
2025-07-28 12:37:57,455 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74, Player=星坠潭, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:37:57,455 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:37:57,456 - [GameProcessThread] - INFO - [游戏线程] 玩家 星坠潭(VirtualPlayerID0485) 抓取指令已发送到移动服务，命令ID: c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74
2025-07-28 12:37:57,456 - [MoveServiceEventReceiver] - INFO - 抓取指令 c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74 已被移动服务接受并加入队列
2025-07-28 12:37:57,658 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.026秒 - 玩家: VirtualPlayerID0489, 结果: 物品_3, 订单: NULL
2025-07-28 12:37:57,658 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0489] = 1
2025-07-28 12:37:57,676 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:37:57,708 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:37:57,962 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74'}, pending_retry_player: None
2025-07-28 12:37:57,963 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:58,466 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74'}, pending_retry_player: None
2025-07-28 12:37:58,468 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:58,528 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:37:58,532 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:37:58,533 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:37:58,542 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:37:58,922 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 星坠潭 已完成游戏，总游戏次数: 2
2025-07-28 12:37:58,985 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74'}, pending_retry_player: None
2025-07-28 12:37:58,986 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:37:59,505 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74'}, pending_retry_player: None
2025-07-28 12:37:59,514 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:00,027 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74'}, pending_retry_player: None
2025-07-28 12:38:00,033 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:00,420 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:38:00,421 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74
2025-07-28 12:38:00,422 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:38:00,423 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0485, player_name=星坠潭, requested_object_id=1
2025-07-28 12:38:00,424 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:38:00,424 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'player_id': 'VirtualPlayerID0485', 'player_name': '星坠潭', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:38:00,425 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 星坠潭(VirtualPlayerID0485), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74, 消息: 物品_1
2025-07-28 12:38:00,428 - [StatusUpdateThread] - INFO - 玩家 星坠潭 本次是第 2 次成功抓取。
2025-07-28 12:38:00,429 - [StatusUpdateThread] - INFO - 为玩家 星坠潭 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:00,432 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:38:00,433 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 517, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:38:00,440 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 517, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:00,544 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:00,549 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:00,582 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6F40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 12
2025-07-28 12:38:00,584 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6F40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:00,585 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:38:00,586 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:38:00,587 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:38:00,588 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:38:00,588 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:38:00,596 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:38:00,599 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:38:01,061 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:01,068 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:01,576 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:01,578 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:01,608 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:38:01,614 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:38:01,615 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:38:01,615 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:01,616 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:38:01,617 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:38:01,617 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 5
2025-07-28 12:38:01,618 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:38:01,623 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:01,648 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:02,095 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:02,101 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:02,615 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:02,623 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:03,132 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:03,139 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:03,444 - [Thread-6] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 674, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:38:03,450 - [Thread-6] - DEBUG - Response received {'d': {'requestId': 674, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:03,663 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:03,665 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:03,676 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6E50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 13
2025-07-28 12:38:03,677 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6E50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:03,677 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:04,176 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:04,179 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:04,693 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677477.4487202, 'target_id': '1', 'target_object': 'Hello Kitty', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:04,693 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:04,868 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:38:04,872 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74
2025-07-28 12:38:04,876 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:38:04,876 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0485, player_name=星坠潭, requested_object_id=1
2025-07-28 12:38:04,876 - [MoveServiceEventReceiver] - INFO - 移动服务请求 c3d5bb9d-9400-4d8d-a2e9-c1e5723b3d74 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:38:04,876 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 星坠潭(VirtualPlayerID0485)
2025-07-28 12:38:04,877 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_1'，准备结束游戏。
2025-07-28 12:38:04,877 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 星坠潭(VirtualPlayerID0485), 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:38:04,877 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:38:04,878 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:38:05,201 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:38:05,208 - [GameProcessThread] - INFO - [游戏线程] 玩家 🌈追梦少年(VirtualPlayerID0486) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:38:05,211 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 🌈追梦少年(VirtualPlayerID0486) 确保抓中, z_offset_extra=0
2025-07-28 12:38:05,214 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=39dd44c4-2497-4373-9e76-5ee8e4d2d00e, Player=🌈追梦少年, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:38:05,214 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:38:05,215 - [GameProcessThread] - INFO - [游戏线程] 玩家 🌈追梦少年(VirtualPlayerID0486) 抓取指令已发送到移动服务，命令ID: 39dd44c4-2497-4373-9e76-5ee8e4d2d00e
2025-07-28 12:38:05,226 - [MoveServiceEventReceiver] - INFO - 抓取指令 39dd44c4-2497-4373-9e76-5ee8e4d2d00e 已被移动服务接受并加入队列
2025-07-28 12:38:05,351 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.042秒 - 玩家: VirtualPlayerID0485, 结果: 物品_1, 订单: virtual_paid
2025-07-28 12:38:05,352 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0485] = 2
2025-07-28 12:38:05,691 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:05,692 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:38:05,692 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:05,697 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:05,740 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e'}, pending_retry_player: None
2025-07-28 12:38:05,742 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:06,260 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e'}, pending_retry_player: None
2025-07-28 12:38:06,260 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:06,781 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e'}, pending_retry_player: None
2025-07-28 12:38:06,782 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:06,969 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 🌈追梦少年 已完成游戏，总游戏次数: 2
2025-07-28 12:38:06,972 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:38:06,979 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['星坠潭(2次)', '🌈追梦少年(2次)']。已选择: 星坠潭
2025-07-28 12:38:06,980 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 星坠潭 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:38:07,298 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e'}, pending_retry_player: None
2025-07-28 12:38:07,299 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:07,724 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809235E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 14
2025-07-28 12:38:07,728 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809235E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:07,729 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:07,817 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e'}, pending_retry_player: None
2025-07-28 12:38:07,819 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:07,942 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.046秒
2025-07-28 12:38:07,945 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:38:07,986 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:38:08,174 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:38:08,181 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=39dd44c4-2497-4373-9e76-5ee8e4d2d00e
2025-07-28 12:38:08,185 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:38:08,188 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0486, player_name=🌈追梦少年, requested_object_id=4
2025-07-28 12:38:08,191 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:38:08,194 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'player_id': 'VirtualPlayerID0486', 'player_name': '🌈追梦少年', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:38:08,195 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 🌈追梦少年(VirtualPlayerID0486), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: 39dd44c4-2497-4373-9e76-5ee8e4d2d00e, 消息: 物品_4
2025-07-28 12:38:08,198 - [StatusUpdateThread] - INFO - 玩家 🌈追梦少年 本次是第 2 次成功抓取。
2025-07-28 12:38:08,199 - [StatusUpdateThread] - INFO - 为玩家 🌈追梦少年 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:08,204 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:38:08,205 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 871, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:38:08,220 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 871, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:08,331 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:08,333 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:08,848 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:08,851 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:09,368 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:09,378 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:09,731 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:09,739 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:38:09,743 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:09,761 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:09,889 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:09,892 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:10,410 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:10,412 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:10,925 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:10,926 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:11,224 - [Thread-7] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 811, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:38:11,231 - [Thread-7] - DEBUG - Response received {'d': {'requestId': 811, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:11,443 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:11,445 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:11,833 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD220>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 15
2025-07-28 12:38:11,837 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD220>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:11,837 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:38:11,838 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:38:11,838 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:38:11,838 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:38:11,839 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:38:11,839 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:38:11,839 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:38:11,960 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:11,962 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:12,481 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0486', 'name': '🌈追梦少年', 'start_time': 1753677485.2085428, 'target_id': '4', 'target_object': '泰迪熊', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:12,482 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:12,703 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '39dd44c4-2497-4373-9e76-5ee8e4d2d00e', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:38:12,712 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=39dd44c4-2497-4373-9e76-5ee8e4d2d00e
2025-07-28 12:38:12,713 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:38:12,713 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0486, player_name=🌈追梦少年, requested_object_id=4
2025-07-28 12:38:12,714 - [MoveServiceEventReceiver] - INFO - 移动服务请求 39dd44c4-2497-4373-9e76-5ee8e4d2d00e (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:38:12,715 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 🌈追梦少年(VirtualPlayerID0486)
2025-07-28 12:38:12,715 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:38:12,716 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 🌈追梦少年(VirtualPlayerID0486), 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:38:12,717 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:38:12,717 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:38:12,842 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:38:12,851 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:38:12,854 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:38:12,855 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:12,856 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:38:12,856 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:38:12,857 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 6
2025-07-28 12:38:12,857 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:38:12,857 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:12,874 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:13,002 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:38:13,005 - [GameProcessThread] - INFO - [游戏线程] 玩家 微笑与不微笑(VirtualPlayerID0487) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:38:13,008 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 微笑与不微笑(VirtualPlayerID0487) 确保抓中, z_offset_extra=0
2025-07-28 12:38:13,013 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=2f084ae5-f96a-4f36-a74e-610f0767140e, Player=微笑与不微笑, Target=4, Z_Offset_Extra=0.0
2025-07-28 12:38:13,015 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-28 12:38:13,018 - [GameProcessThread] - INFO - [游戏线程] 玩家 微笑与不微笑(VirtualPlayerID0487) 抓取指令已发送到移动服务，命令ID: 2f084ae5-f96a-4f36-a74e-610f0767140e
2025-07-28 12:38:13,020 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 微笑与不微笑 已完成游戏，总游戏次数: 2
2025-07-28 12:38:13,023 - [MoveServiceEventReceiver] - INFO - 抓取指令 2f084ae5-f96a-4f36-a74e-610f0767140e 已被移动服务接受并加入队列
2025-07-28 12:38:13,026 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(7)，添加虚拟玩家。
2025-07-28 12:38:13,030 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['🌈追梦少年(2次)', '微笑与不微笑(2次)']。已选择: 🌈追梦少年
2025-07-28 12:38:13,032 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 🌈追梦少年 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:38:13,066 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.033秒 - 玩家: VirtualPlayerID0486, 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:38:13,067 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0486] = 2
2025-07-28 12:38:13,536 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e'}, pending_retry_player: None
2025-07-28 12:38:13,536 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:14,043 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e'}, pending_retry_player: None
2025-07-28 12:38:14,045 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:14,563 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e'}, pending_retry_player: None
2025-07-28 12:38:14,566 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:14,923 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AB20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 16
2025-07-28 12:38:14,931 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AB20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:14,933 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:15,081 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e'}, pending_retry_player: None
2025-07-28 12:38:15,083 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:15,600 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e'}, pending_retry_player: None
2025-07-28 12:38:15,601 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:15,980 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-28 12:38:15,981 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=2f084ae5-f96a-4f36-a74e-610f0767140e
2025-07-28 12:38:15,982 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-28 12:38:15,983 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0487, player_name=微笑与不微笑, requested_object_id=4
2025-07-28 12:38:15,983 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-28 12:38:15,984 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'player_id': 'VirtualPlayerID0487', 'player_name': '微笑与不微笑', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-28 12:38:15,985 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 微笑与不微笑(VirtualPlayerID0487), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: 2f084ae5-f96a-4f36-a74e-610f0767140e, 消息: 物品_4
2025-07-28 12:38:15,987 - [StatusUpdateThread] - INFO - 玩家 微笑与不微笑 本次是第 2 次成功抓取。
2025-07-28 12:38:15,988 - [StatusUpdateThread] - INFO - 为玩家 微笑与不微笑 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:15,990 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:38:15,991 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 777, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:38:15,993 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 777, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:16,108 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:16,113 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:16,628 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:16,634 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:16,643 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:38:16,650 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:38:16,653 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 63.6 秒
2025-07-28 12:38:16,654 - [HealthCheckThread] - WARNING - 已经 63.6 秒没有成功请求，尝试恢复连接...
2025-07-28 12:38:16,654 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:16,659 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:16,940 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:16,946 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:38:16,947 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:16,960 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:17,052 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:38:17,060 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['微笑与不微笑(2次)']。已选择: 微笑与不微笑
2025-07-28 12:38:17,063 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 微笑与不微笑 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:38:17,148 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:17,156 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:17,666 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:17,674 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:18,134 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:38:18,180 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:18,182 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:18,201 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:38:18,685 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:18,688 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:18,702 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934EE0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:18,758 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:38:19,000 - [Thread-8] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 942, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:38:19,003 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809237C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 17
2025-07-28 12:38:19,009 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279809237C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:19,010 - [Thread-8] - DEBUG - Response received {'d': {'requestId': 942, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:19,012 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:19,190 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:19,192 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:19,709 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:19,711 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:20,228 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0487', 'name': '微笑与不微笑', 'start_time': **********.0052028, 'target_id': '4', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'game_result': '物品_4'}, pending_retry_player: None
2025-07-28 12:38:20,231 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:20,512 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '2f084ae5-f96a-4f36-a74e-610f0767140e', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:38:20,514 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=2f084ae5-f96a-4f36-a74e-610f0767140e
2025-07-28 12:38:20,515 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:38:20,516 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0487, player_name=微笑与不微笑, requested_object_id=4
2025-07-28 12:38:20,517 - [MoveServiceEventReceiver] - INFO - 移动服务请求 2f084ae5-f96a-4f36-a74e-610f0767140e (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:38:20,518 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 微笑与不微笑(VirtualPlayerID0487)
2025-07-28 12:38:20,520 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-28 12:38:20,521 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 微笑与不微笑(VirtualPlayerID0487), 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:38:20,522 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:38:20,522 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:38:20,748 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:38:20,750 - [GameProcessThread] - INFO - [游戏线程] 玩家 凌霜月(VirtualPlayerID0490) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:38:20,752 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 凌霜月(VirtualPlayerID0490) 确保抓中, z_offset_extra=0
2025-07-28 12:38:20,757 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=60346d05-d7e2-4112-be5c-e74f1effd65c, Player=凌霜月, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:38:20,759 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:38:20,761 - [GameProcessThread] - INFO - [游戏线程] 玩家 凌霜月(VirtualPlayerID0490) 抓取指令已发送到移动服务，命令ID: 60346d05-d7e2-4112-be5c-e74f1effd65c
2025-07-28 12:38:20,763 - [MoveServiceEventReceiver] - INFO - 抓取指令 60346d05-d7e2-4112-be5c-e74f1effd65c 已被移动服务接受并加入队列
2025-07-28 12:38:20,823 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.060秒 - 玩家: VirtualPlayerID0487, 结果: 物品_4, 订单: virtual_paid
2025-07-28 12:38:20,824 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0487] = 2
2025-07-28 12:38:20,860 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0490 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:38:20,869 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0490 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:38:21,015 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:21,018 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:38:21,018 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:21,023 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:21,094 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 凌霜月 已完成游戏，总游戏次数: 1
2025-07-28 12:38:21,096 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 凌霜月 下次将以付费状态排队。
2025-07-28 12:38:21,268 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c'}, pending_retry_player: None
2025-07-28 12:38:21,273 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:21,787 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c'}, pending_retry_player: None
2025-07-28 12:38:21,788 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:22,302 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c'}, pending_retry_player: None
2025-07-28 12:38:22,304 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:22,823 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c'}, pending_retry_player: None
2025-07-28 12:38:22,825 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:23,077 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 18
2025-07-28 12:38:23,080 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFBD250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:23,082 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:38:23,083 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:38:23,083 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:38:23,084 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:38:23,085 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:38:23,086 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:38:23,090 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:38:23,329 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c'}, pending_retry_player: None
2025-07-28 12:38:23,331 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:23,785 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:38:23,787 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=60346d05-d7e2-4112-be5c-e74f1effd65c
2025-07-28 12:38:23,787 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:38:23,788 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0490, player_name=凌霜月, requested_object_id=2
2025-07-28 12:38:23,789 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:38:23,789 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'player_id': 'VirtualPlayerID0490', 'player_name': '凌霜月', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:38:23,790 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 凌霜月(VirtualPlayerID0490), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: 60346d05-d7e2-4112-be5c-e74f1effd65c, 消息: 物品_2
2025-07-28 12:38:23,792 - [StatusUpdateThread] - INFO - 玩家 凌霜月 本次是第 1 次成功抓取。
2025-07-28 12:38:23,793 - [StatusUpdateThread] - INFO - 为玩家 凌霜月 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:23,796 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:38:23,797 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 493, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:38:23,801 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 493, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:23,848 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:23,855 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:24,114 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:38:24,115 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:38:24,116 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:38:24,116 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:24,116 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:38:24,117 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:38:24,117 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 7
2025-07-28 12:38:24,117 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:38:24,118 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:24,121 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:24,369 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:24,377 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:24,890 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:24,891 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:25,112 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:38:25,116 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['凌霜月(1次)']。已选择: 凌霜月
2025-07-28 12:38:25,121 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 凌霜月 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:38:25,397 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:25,398 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:25,916 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:25,919 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:26,155 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923880>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 19
2025-07-28 12:38:26,158 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923880>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:26,159 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:26,437 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:26,440 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:26,812 - [Thread-9] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 67, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:38:26,817 - [Thread-9] - DEBUG - Response received {'d': {'requestId': 67, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:26,956 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:26,958 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:27,471 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:27,477 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:27,989 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0490', 'name': '凌霜月', 'start_time': 1753677500.750091, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:27,990 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:28,161 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:28,163 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:38:28,164 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:28,177 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:28,257 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '60346d05-d7e2-4112-be5c-e74f1effd65c', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:38:28,259 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=60346d05-d7e2-4112-be5c-e74f1effd65c
2025-07-28 12:38:28,260 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:38:28,261 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0490, player_name=凌霜月, requested_object_id=2
2025-07-28 12:38:28,262 - [MoveServiceEventReceiver] - INFO - 移动服务请求 60346d05-d7e2-4112-be5c-e74f1effd65c (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:38:28,262 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 凌霜月(VirtualPlayerID0490)
2025-07-28 12:38:28,264 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:38:28,264 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 凌霜月(VirtualPlayerID0490), 结果: 物品_2, 订单: None
2025-07-28 12:38:28,267 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:38:28,269 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:38:28,506 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:38:28,511 - [GameProcessThread] - INFO - [游戏线程] 玩家 子葵(VirtualPlayerID0491) 开始免费游戏，扣除免费次数 (1)
2025-07-28 12:38:28,512 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 子葵(VirtualPlayerID0491) 确保抓中, z_offset_extra=0
2025-07-28 12:38:28,516 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4, Player=子葵, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:38:28,517 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:38:28,517 - [GameProcessThread] - INFO - [游戏线程] 玩家 子葵(VirtualPlayerID0491) 抓取指令已发送到移动服务，命令ID: ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4
2025-07-28 12:38:28,518 - [MoveServiceEventReceiver] - INFO - 抓取指令 ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4 已被移动服务接受并加入队列
2025-07-28 12:38:28,525 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.052秒 - 玩家: VirtualPlayerID0490, 结果: 物品_2, 订单: NULL
2025-07-28 12:38:28,530 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0490] = 1
2025-07-28 12:38:28,564 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0491 订单状态: {'free_games_used_this_session': 1}
2025-07-28 12:38:28,566 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0491 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-28 12:38:28,579 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:38:28,586 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:38:29,024 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4'}, pending_retry_player: None
2025-07-28 12:38:29,024 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:29,134 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 子葵 已完成游戏，总游戏次数: 1
2025-07-28 12:38:29,140 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 子葵 下次将以付费状态排队。
2025-07-28 12:38:29,144 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(6) < 目标(7)，添加虚拟玩家。
2025-07-28 12:38:29,146 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['子葵(1次)']。已选择: 子葵
2025-07-28 12:38:29,147 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 子葵 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:38:29,532 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4'}, pending_retry_player: None
2025-07-28 12:38:29,533 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:30,046 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4'}, pending_retry_player: None
2025-07-28 12:38:30,046 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:30,205 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923220>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 20
2025-07-28 12:38:30,210 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923220>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:30,211 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:30,562 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4'}, pending_retry_player: None
2025-07-28 12:38:30,563 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:31,080 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4'}, pending_retry_player: None
2025-07-28 12:38:31,082 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:31,531 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:38:31,539 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4
2025-07-28 12:38:31,542 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:38:31,543 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0491, player_name=子葵, requested_object_id=2
2025-07-28 12:38:31,544 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:38:31,547 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'player_id': 'VirtualPlayerID0491', 'player_name': '子葵', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:38:31,550 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 子葵(VirtualPlayerID0491), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4, 消息: 物品_2
2025-07-28 12:38:31,554 - [StatusUpdateThread] - INFO - 玩家 子葵 本次是第 1 次成功抓取。
2025-07-28 12:38:31,555 - [StatusUpdateThread] - INFO - 为玩家 子葵 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:31,558 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(1次): 抓中特效1
2025-07-28 12:38:31,559 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 791, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': True}}}
2025-07-28 12:38:31,562 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 791, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:31,595 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:31,597 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:32,111 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:32,112 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:32,221 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:32,224 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:38:32,225 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:32,248 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:32,629 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:32,631 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:33,142 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:33,144 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:33,660 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:33,662 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:34,180 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:34,182 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:34,276 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6F10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 21
2025-07-28 12:38:34,280 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000279FFFB6F10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:34,281 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:38:34,282 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:38:34,282 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:38:34,283 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.3s, 心跳间隔=1s
2025-07-28 12:38:34,284 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:38:34,285 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:38:34,291 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:38:34,588 - [Thread-10] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 615, 'requestData': {'sceneName': '场景1', 'sceneItemId': 60, 'sceneItemEnabled': False}}}
2025-07-28 12:38:34,600 - [Thread-10] - DEBUG - Response received {'d': {'requestId': 615, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:34,698 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:34,699 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:35,205 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:35,212 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:35,299 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:38:35,308 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:38:35,309 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:38:35,311 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:35,311 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:38:35,311 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:38:35,312 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 8
2025-07-28 12:38:35,312 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:38:35,313 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:35,317 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:35,727 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {'player_id': 'VirtualPlayerID0491', 'name': '子葵', 'start_time': 1753677508.510908, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:35,734 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:35,992 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:38:35,994 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4
2025-07-28 12:38:35,995 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:38:35,995 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0491, player_name=子葵, requested_object_id=2
2025-07-28 12:38:35,996 - [MoveServiceEventReceiver] - INFO - 移动服务请求 ab6b5e3b-dcff-4bc9-9b1c-ed51885ec4e4 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:38:35,996 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 子葵(VirtualPlayerID0491)
2025-07-28 12:38:35,998 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:38:35,999 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 子葵(VirtualPlayerID0491), 结果: 物品_2, 订单: None
2025-07-28 12:38:36,001 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:38:36,002 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:38:36,186 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.036秒 - 玩家: VirtualPlayerID0491, 结果: 物品_2, 订单: NULL
2025-07-28 12:38:36,187 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0491] = 1
2025-07-28 12:38:36,244 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 7, current_player: {}, pending_retry_player: None
2025-07-28 12:38:36,249 - [GameProcessThread] - INFO - [游戏线程] 玩家 念旧人(VirtualPlayerID0488) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:38:36,251 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 念旧人(VirtualPlayerID0488) 确保抓中, z_offset_extra=0
2025-07-28 12:38:36,257 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=d96bf113-5844-4801-9462-fa6964ade894, Player=念旧人, Target=2, Z_Offset_Extra=0.0
2025-07-28 12:38:36,258 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-28 12:38:36,261 - [GameProcessThread] - INFO - [游戏线程] 玩家 念旧人(VirtualPlayerID0488) 抓取指令已发送到移动服务，命令ID: d96bf113-5844-4801-9462-fa6964ade894
2025-07-28 12:38:36,266 - [MoveServiceEventReceiver] - INFO - 抓取指令 d96bf113-5844-4801-9462-fa6964ade894 已被移动服务接受并加入队列
2025-07-28 12:38:36,781 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894'}, pending_retry_player: None
2025-07-28 12:38:36,782 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:37,191 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 念旧人 已完成游戏，总游戏次数: 2
2025-07-28 12:38:37,286 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894'}, pending_retry_player: None
2025-07-28 12:38:37,290 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:37,350 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 22
2025-07-28 12:38:37,353 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:37,354 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:37,800 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894'}, pending_retry_player: None
2025-07-28 12:38:37,807 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:38,320 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894'}, pending_retry_player: None
2025-07-28 12:38:38,326 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:38,761 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:38:38,801 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:38:38,842 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894'}, pending_retry_player: None
2025-07-28 12:38:38,848 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:39,219 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-28 12:38:39,226 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=d96bf113-5844-4801-9462-fa6964ade894
2025-07-28 12:38:39,228 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-28 12:38:39,230 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0488, player_name=念旧人, requested_object_id=2
2025-07-28 12:38:39,233 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-28 12:38:39,236 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'player_id': 'VirtualPlayerID0488', 'player_name': '念旧人', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-28 12:38:39,237 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 念旧人(VirtualPlayerID0488), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: d96bf113-5844-4801-9462-fa6964ade894, 消息: 物品_2
2025-07-28 12:38:39,240 - [StatusUpdateThread] - INFO - 玩家 念旧人 本次是第 2 次成功抓取。
2025-07-28 12:38:39,240 - [StatusUpdateThread] - INFO - 为玩家 念旧人 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:39,244 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:38:39,245 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 821, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:38:39,246 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 821, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:39,362 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:39,361 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:39,371 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:39,374 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:38:39,380 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:39,387 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:39,880 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:39,882 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:40,399 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:40,406 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:40,919 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:40,926 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:41,428 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923C10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 23
2025-07-28 12:38:41,431 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923C10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:41,433 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:41,442 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:41,449 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:41,960 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:41,967 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:42,259 - [Thread-11] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 779, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:38:42,268 - [Thread-11] - DEBUG - Response received {'d': {'requestId': 779, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:42,483 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:42,489 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:43,001 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:43,007 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:43,439 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:43,443 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:38:43,444 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:43,457 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:43,518 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0488', 'name': '念旧人', 'start_time': 1753677516.2498221, 'target_id': '2', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'game_result': '物品_2'}, pending_retry_player: None
2025-07-28 12:38:43,523 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:43,754 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'd96bf113-5844-4801-9462-fa6964ade894', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:38:43,760 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=d96bf113-5844-4801-9462-fa6964ade894
2025-07-28 12:38:43,763 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:38:43,765 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0488, player_name=念旧人, requested_object_id=2
2025-07-28 12:38:43,765 - [MoveServiceEventReceiver] - INFO - 移动服务请求 d96bf113-5844-4801-9462-fa6964ade894 (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:38:43,770 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 念旧人(VirtualPlayerID0488)
2025-07-28 12:38:43,777 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-28 12:38:43,778 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 念旧人(VirtualPlayerID0488), 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:38:43,779 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:38:43,780 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:38:43,890 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.042秒 - 玩家: VirtualPlayerID0488, 结果: 物品_2, 订单: virtual_paid
2025-07-28 12:38:43,890 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0488] = 2
2025-07-28 12:38:44,039 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:38:44,048 - [GameProcessThread] - INFO - [游戏线程] 玩家 郭靖的现代传奇(VirtualPlayerID0489) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:38:44,050 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 郭靖的现代传奇(VirtualPlayerID0489) 确保抓中, z_offset_extra=0
2025-07-28 12:38:44,055 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-28 12:38:44,056 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=00453e81-47c8-4f53-9fe6-0f8c9eedb06f, Player=郭靖的现代传奇, Target=5, Z_Offset_Extra=0.0
2025-07-28 12:38:44,059 - [MoveServiceEventReceiver] - INFO - 抓取指令 00453e81-47c8-4f53-9fe6-0f8c9eedb06f 已被移动服务接受并加入队列
2025-07-28 12:38:44,061 - [GameProcessThread] - INFO - [游戏线程] 玩家 郭靖的现代传奇(VirtualPlayerID0489) 抓取指令已发送到移动服务，命令ID: 00453e81-47c8-4f53-9fe6-0f8c9eedb06f
2025-07-28 12:38:44,571 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f'}, pending_retry_player: None
2025-07-28 12:38:44,573 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:45,076 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f'}, pending_retry_player: None
2025-07-28 12:38:45,081 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:45,250 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 郭靖的现代传奇 已完成游戏，总游戏次数: 2
2025-07-28 12:38:45,501 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AB50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 24
2025-07-28 12:38:45,511 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798090AB50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:45,511 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:38:45,512 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:38:45,512 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:38:45,512 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:38:45,513 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:38:45,513 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:38:45,515 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:38:45,594 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f'}, pending_retry_player: None
2025-07-28 12:38:45,596 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:46,099 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f'}, pending_retry_player: None
2025-07-28 12:38:46,101 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:46,525 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:38:46,527 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:38:46,528 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:38:46,528 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:46,529 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:38:46,530 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:38:46,531 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 9
2025-07-28 12:38:46,531 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:38:46,532 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:46,550 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:46,603 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f'}, pending_retry_player: None
2025-07-28 12:38:46,605 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:47,079 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-28 12:38:47,081 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=00453e81-47c8-4f53-9fe6-0f8c9eedb06f
2025-07-28 12:38:47,082 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-28 12:38:47,082 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0489, player_name=郭靖的现代传奇, requested_object_id=5
2025-07-28 12:38:47,083 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-28 12:38:47,084 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'player_id': 'VirtualPlayerID0489', 'player_name': '郭靖的现代传奇', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-28 12:38:47,084 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 郭靖的现代传奇(VirtualPlayerID0489), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: 00453e81-47c8-4f53-9fe6-0f8c9eedb06f, 消息: 物品_5
2025-07-28 12:38:47,085 - [StatusUpdateThread] - INFO - 玩家 郭靖的现代传奇 本次是第 2 次成功抓取。
2025-07-28 12:38:47,086 - [StatusUpdateThread] - INFO - 为玩家 郭靖的现代传奇 生成优惠券提示: 获得满减40元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:47,087 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(2次): 抓中特效2
2025-07-28 12:38:47,087 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 454, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': True}}}
2025-07-28 12:38:47,088 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 454, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:47,121 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:47,123 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:47,263 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(5) < 目标(6)，添加虚拟玩家。
2025-07-28 12:38:47,265 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['念旧人(2次)', '郭靖的现代传奇(2次)']。已选择: 念旧人
2025-07-28 12:38:47,267 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 念旧人 已加入队列末尾。优先级: 3.14 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-28 12:38:47,626 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:47,628 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:48,131 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:48,133 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:48,577 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798093D100>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 25
2025-07-28 12:38:48,583 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798093D100>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:48,588 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:48,638 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:48,640 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:48,762 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-28 12:38:48,770 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-28 12:38:48,774 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 95.7 秒
2025-07-28 12:38:48,777 - [HealthCheckThread] - WARNING - 已经 95.7 秒没有成功请求，尝试恢复连接...
2025-07-28 12:38:48,781 - [HealthCheckThread] - DEBUG - [健康检查] 发送 GET 请求到 http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:48,791 - [HealthCheckThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:48,954 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:38:49,013 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:38:49,144 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:49,151 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:49,660 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:49,666 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:50,098 - [Thread-12] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 90, 'requestData': {'sceneName': '场景1', 'sceneItemId': 53, 'sceneItemEnabled': False}}}
2025-07-28 12:38:50,105 - [Thread-12] - DEBUG - Response received {'d': {'requestId': 90, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:50,178 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:50,184 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:50,600 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:50,610 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:38:50,613 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:50,628 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:50,697 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:50,703 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:50,823 - [HealthCheckThread] - ERROR - 健康检查请求失败: HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980947370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:50,825 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-28 12:38:51,215 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {'player_id': 'VirtualPlayerID0489', 'name': '郭靖的现代传奇', 'start_time': **********.0474775, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'game_result': '物品_5'}, pending_retry_player: None
2025-07-28 12:38:51,221 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:51,546 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '00453e81-47c8-4f53-9fe6-0f8c9eedb06f', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-28 12:38:51,551 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=00453e81-47c8-4f53-9fe6-0f8c9eedb06f
2025-07-28 12:38:51,556 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-28 12:38:51,559 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0489, player_name=郭靖的现代传奇, requested_object_id=5
2025-07-28 12:38:51,564 - [MoveServiceEventReceiver] - INFO - 移动服务请求 00453e81-47c8-4f53-9fe6-0f8c9eedb06f (cycle_completed) 已完成，从活动命令中移除。
2025-07-28 12:38:51,568 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 郭靖的现代传奇(VirtualPlayerID0489)
2025-07-28 12:38:51,569 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-28 12:38:51,570 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 郭靖的现代传奇(VirtualPlayerID0489), 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:38:51,572 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-28 12:38:51,572 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-28 12:38:51,734 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 6, current_player: {}, pending_retry_player: None
2025-07-28 12:38:51,745 - [GameProcessThread] - INFO - [游戏线程] 玩家 星坠潭(VirtualPlayerID0485) 使用订单 virtual_paid，不消耗免费次数
2025-07-28 12:38:51,747 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 星坠潭(VirtualPlayerID0485) 确保抓中, z_offset_extra=0
2025-07-28 12:38:51,754 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=41061c9d-0a9b-4c44-889e-eadb3c3a7bfc, Player=星坠潭, Target=1, Z_Offset_Extra=0.0
2025-07-28 12:38:51,757 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'status': 'queued', 'message': '抓取物体 1 指令已加入队列'}
2025-07-28 12:38:51,759 - [GameProcessThread] - INFO - [游戏线程] 玩家 星坠潭(VirtualPlayerID0485) 抓取指令已发送到移动服务，命令ID: 41061c9d-0a9b-4c44-889e-eadb3c3a7bfc
2025-07-28 12:38:51,760 - [MoveServiceEventReceiver] - INFO - 抓取指令 41061c9d-0a9b-4c44-889e-eadb3c3a7bfc 已被移动服务接受并加入队列
2025-07-28 12:38:52,064 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.033秒 - 玩家: VirtualPlayerID0489, 结果: 物品_5, 订单: virtual_paid
2025-07-28 12:38:52,065 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0489] = 2
2025-07-28 12:38:52,269 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc'}, pending_retry_player: None
2025-07-28 12:38:52,275 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:52,662 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923D30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 26
2025-07-28 12:38:52,664 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980923D30>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:52,664 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:38:52,790 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc'}, pending_retry_player: None
2025-07-28 12:38:52,790 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:53,294 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc'}, pending_retry_player: None
2025-07-28 12:38:53,300 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:53,308 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 星坠潭 已完成游戏，总游戏次数: 3
2025-07-28 12:38:53,817 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc'}, pending_retry_player: None
2025-07-28 12:38:53,823 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:54,334 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc'}, pending_retry_player: None
2025-07-28 12:38:54,340 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:54,678 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:38:54,680 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:38:54,680 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:54,687 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:54,711 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '1', 'message': '物品_1'}}
2025-07-28 12:38:54,716 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=41061c9d-0a9b-4c44-889e-eadb3c3a7bfc
2025-07-28 12:38:54,719 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '1', 'message': '物品_1'}
2025-07-28 12:38:54,720 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0485, player_name=星坠潭, requested_object_id=1
2025-07-28 12:38:54,720 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 1, 物品名称: '物品_1', 原始消息: '物品_1'
2025-07-28 12:38:54,721 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'player_id': 'VirtualPlayerID0485', 'player_name': '星坠潭', 'item_name': '物品_1', 'success': True, 'object_id': '1', 'message': '物品_1', 'source': 'real_mode'}
2025-07-28 12:38:54,722 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 星坠潭(VirtualPlayerID0485), 成功: True, 物品: 物品_1, 物品ID: 1, 来源: real_mode, ReqID: 41061c9d-0a9b-4c44-889e-eadb3c3a7bfc, 消息: 物品_1
2025-07-28 12:38:54,723 - [StatusUpdateThread] - INFO - 玩家 星坠潭 本次是第 3 次成功抓取。
2025-07-28 12:38:54,723 - [StatusUpdateThread] - INFO - 为玩家 星坠潭 生成优惠券提示: 获得满减80元券！
再抓到一次优惠翻倍！
2025-07-28 12:38:54,724 - [StatusUpdateThread] - INFO - 显示祝贺特效视频(3次): 抓中特效3
2025-07-28 12:38:54,724 - [StatusUpdateThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 779, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': True}}}
2025-07-28 12:38:54,727 - [StatusUpdateThread] - DEBUG - Response received {'d': {'requestId': 779, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:54,852 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:54,858 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:55,366 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:55,373 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:55,886 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:55,889 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:56,405 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:56,411 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:56,723 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934D90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 27
2025-07-28 12:38:56,727 - [MessagePollThread] - ERROR - 请求失败，已达到最大重试次数 (3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027980934D90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-28 12:38:56,728 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (达到最大重试次数)
2025-07-28 12:38:56,729 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: None, status: N/A
2025-07-28 12:38:56,729 - [MessagePollThread] - ERROR - 错误: 收到状态码 无响应
2025-07-28 12:38:56,729 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=11.2s, 心跳间隔=1s
2025-07-28 12:38:56,730 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-28 12:38:56,730 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-28 12:38:56,731 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-28 12:38:56,924 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:56,932 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:57,443 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0485', 'name': '星坠潭', 'start_time': 1753677531.744575, 'target_id': '1', 'target_object': '毛绒兔子', 'order_id': 'virtual_paid', 'status': 'waiting_move_service', 'move_command_id': '41061c9d-0a9b-4c44-889e-eadb3c3a7bfc', 'game_result': '物品_1'}, pending_retry_player: None
2025-07-28 12:38:57,445 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-28 12:38:57,544 - [DetectionReceiver] - INFO - 已断开检测服务器连接
2025-07-28 12:38:57,545 - [DetectionReceiver] - INFO - 检测数据接收器已停止
2025-07-28 12:38:57,744 - [Thread-13] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'SetSceneItemEnabled', 'requestId': 946, 'requestData': {'sceneName': '场景1', 'sceneItemId': 56, 'sceneItemEnabled': False}}}
2025-07-28 12:38:57,742 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-28 12:38:57,747 - [MainThread] - INFO - 进入清理阶段...
2025-07-28 12:38:57,752 - [Thread-13] - DEBUG - Response received {'d': {'requestId': 946, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'SetSceneItemEnabled'}, 'op': 7}
2025-07-28 12:38:57,750 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-28 12:38:57,755 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-28 12:38:57,756 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-28 12:38:57,757 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-28 12:38:57,761 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-28 12:38:57,762 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 10
2025-07-28 12:38:57,762 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-28 12:38:57,766 - [MainThread] - INFO - [清理] 开始执行清理操作
2025-07-28 12:38:57,763 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:38:57,769 - [MainThread] - DEBUG - [清理] 设置 stop_flag['running'] = False
2025-07-28 12:38:57,770 - [MainThread] - INFO - [清理] 阶段1: 停止所有外部输入...
2025-07-28 12:38:57,770 - [MainThread] - DEBUG - [清理] 停止消息获取线程...
2025-07-28 12:38:57,772 - [MainThread] - DEBUG - [消息线程] 尝试停止消息线程
2025-07-28 12:38:57,774 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:38:57,774 - [MainThread] - DEBUG - [消息线程] 等待消息线程结束
2025-07-28 12:38:57,935 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已停止。
2025-07-28 12:38:57,937 - [MoveServiceEventReceiver] - INFO - 正在断开与移动服务的连接...
2025-07-28 12:38:57,939 - [MoveServiceEventReceiver] - INFO - 与移动服务的连接已断开。
2025-07-28 12:38:59,177 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.022秒
2025-07-28 12:38:59,178 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:38:59,192 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:38:59,345 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 管理器线程已停止。
2025-07-28 12:38:59,789 - [MainThread] - WARNING - [消息线程] 消息线程未在2秒内结束
2025-07-28 12:38:59,790 - [MainThread] - DEBUG - [消息线程] 消息队列已清空
2025-07-28 12:38:59,790 - [MainThread] - DEBUG - [清理] 停止移动服务客户端...
2025-07-28 12:38:59,791 - [MainThread] - INFO - 正在停止移动服务客户端...
2025-07-28 12:38:59,791 - [MainThread] - INFO - 正在断开与移动服务的连接...
2025-07-28 12:38:59,791 - [MainThread] - INFO - 与移动服务的连接已断开。
2025-07-28 12:38:59,791 - [MainThread] - INFO - 移动服务客户端已停止。
2025-07-28 12:38:59,792 - [MainThread] - DEBUG - [清理] 断开检测服务器连接...
2025-07-28 12:38:59,792 - [MainThread] - INFO - 已断开检测服务器连接
2025-07-28 12:38:59,792 - [MainThread] - INFO - [清理] 阶段2: 停止独立进程...
2025-07-28 12:38:59,792 - [MainThread] - DEBUG - [清理] 关闭GUI通信队列...
2025-07-28 12:38:59,798 - [MainThread] - DEBUG - [清理] 停止Web显示进程...
2025-07-28 12:38:59,805 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798093D070>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 28
2025-07-28 12:38:59,807 - [MessagePollThread] - WARNING - 请求失败 (尝试 1/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798093D070>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:38:59,808 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:39:00,070 - [MainThread] - INFO - [清理] 阶段3: 等待内部线程结束...
2025-07-28 12:39:00,073 - [MainThread] - DEBUG - [清理] 等待主循环监控线程结束…
2025-07-28 12:39:01,818 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:39:01,819 - [MessagePollThread] - DEBUG - [安全请求] 尝试 2/3
2025-07-28 12:39:01,819 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:39:01,824 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:39:02,089 - [MainThread] - WARNING - [清理] 主循环监控线程未在2秒内结束
2025-07-28 12:39:02,089 - [MainThread] - INFO - [清理] 阶段4: 最终数据同步和资源释放...
2025-07-28 12:39:02,090 - [MainThread] - DEBUG - [清理] 停止数据库同步管理器...
2025-07-28 12:39:02,090 - [MainThread] - INFO - 停止数据库同步线程
2025-07-28 12:39:02,230 - [MainThread] - INFO - 数据库同步线程已停止
2025-07-28 12:39:02,230 - [MainThread] - DEBUG - Sending request {'op': 6, 'd': {'requestType': 'GetSceneItemList', 'requestId': 927, 'requestData': {'sceneName': '场景1'}}}
2025-07-28 12:39:02,238 - [MainThread] - DEBUG - Response received {'d': {'requestId': 927, 'requestStatus': {'code': 100, 'result': True}, 'requestType': 'GetSceneItemList', 'responseData': {'sceneItems': [{'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 58, 'sceneItemIndex': 0, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '没抓到特效', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'color_source_v3', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 49, 'sceneItemIndex': 1, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '色源', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'vlc_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 40, 'sceneItemIndex': 2, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 8, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 540.0, 'positionY': 1920.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '背景视频', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'image_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 62, 'sceneItemIndex': 3, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '临时图像', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'dshow_input', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 46, 'sceneItemIndex': 4, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1089.0, 'positionX': 1089.0, 'positionY': -16.0, 'rotation': 90.0, 'scaleX': 1.0083333253860474, 'scaleY': 1.0083333253860474, 'sourceHeight': 1080.0, 'sourceWidth': 1920.0, 'width': 1936.0}, 'sourceName': '主摄像头', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 1, 'sceneItemIndex': 5, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 116.22856903076172, 'positionX': 214.0, 'positionY': 44.77142333984375, 'rotation': 0.0, 'scaleX': 1.6139534711837769, 'scaleY': 1.6142857074737549, 'sourceHeight': 72.0, 'sourceWidth': 444.0, 'width': 716.5953369140625}, 'sourceName': '大标题', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 60, 'sceneItemIndex': 6, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 14.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4541666507720947, 'scaleY': 1.4546159505844116, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 53, 'sceneItemIndex': 7, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': -4.0, 'rotation': 0.0, 'scaleX': 1.5, 'scaleY': 1.5003879070281982, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效2', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 56, 'sceneItemIndex': 8, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 21.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.4416667222976685, 'scaleY': 1.44142746925354, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效3', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'ffmpeg_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 57, 'sceneItemIndex': 9, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 1.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 1.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 22.0, 'positionY': -163.0, 'rotation': 0.0, 'scaleX': 1.4375, 'scaleY': 1.4375485181808472, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '抓中特效4', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'text_gdiplus_v2', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 34, 'sceneItemIndex': 10, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 164.0, 'positionX': 454.0, 'positionY': 132.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 164.0, 'sourceWidth': 626.0, 'width': 626.0}, 'sourceName': '说明1', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'monitor_capture', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': False, 'sceneItemId': 41, 'sceneItemIndex': 11, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 0.0, 'positionX': 0.0, 'positionY': 734.0, 'rotation': 0.0, 'scaleX': 0.6026041507720947, 'scaleY': 0.6027777791023254, 'sourceHeight': 0.0, 'sourceWidth': 0.0, 'width': 0.0}, 'sourceName': '显示器采集', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}, {'inputKind': 'browser_source', 'isGroup': None, 'sceneItemBlendMode': 'OBS_BLEND_NORMAL', 'sceneItemEnabled': True, 'sceneItemId': 50, 'sceneItemIndex': 12, 'sceneItemLocked': True, 'sceneItemTransform': {'alignment': 5, 'boundsAlignment': 0, 'boundsHeight': 0.0, 'boundsType': 'OBS_BOUNDS_NONE', 'boundsWidth': 0.0, 'cropBottom': 0, 'cropLeft': 0, 'cropRight': 0, 'cropTop': 0, 'height': 1920.0, 'positionX': 0.0, 'positionY': 0.0, 'rotation': 0.0, 'scaleX': 1.0, 'scaleY': 1.0, 'sourceHeight': 1920.0, 'sourceWidth': 1080.0, 'width': 1080.0}, 'sourceName': '浏览器显示', 'sourceType': 'OBS_SOURCE_TYPE_INPUT'}]}}, 'op': 7}
2025-07-28 12:39:02,240 - [MainThread] - WARNING - 在场景 '场景1' 中未找到源 '抓中特效'
2025-07-28 12:39:02,241 - [MainThread] - DEBUG - [清理] 强制隐藏 OBS 视频源
2025-07-28 12:39:02,241 - [MainThread] - DEBUG - [清理] 停止健康检查线程...
2025-07-28 12:39:02,241 - [MainThread] - DEBUG - [健康检查] 收到停止信号
2025-07-28 12:39:02,241 - [MainThread] - DEBUG - [健康检查] 等待线程结束
2025-07-28 12:39:03,859 - [MessagePollThread] - DEBUG - [安全请求] 请求失败 (超时/连接错误): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798093D550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')), 失败计数: 29
2025-07-28 12:39:03,861 - [MessagePollThread] - WARNING - 请求失败 (尝试 2/3): HTTPConnectionPool(host='127.0.0.1', port=9999): Max retries exceeded with url: /game-da302d82 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002798093D550>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))，2秒后重试...
2025-07-28 12:39:03,861 - [MessagePollThread] - DEBUG - [安全请求] 休眠 2 秒后重试
2025-07-28 12:39:04,255 - [MainThread] - INFO - 健康检查线程已停止
2025-07-28 12:39:04,256 - [MainThread] - DEBUG - [清理] 已获取队列锁，执行最终数据同步...
2025-07-28 12:39:04,276 - [MainThread] - INFO - 数据库操作: 更新队列完成，耗时 0.020秒
2025-07-28 12:39:05,874 - [MessagePollThread] - DEBUG - [安全请求] 重试休眠结束
2025-07-28 12:39:05,877 - [MessagePollThread] - DEBUG - [安全请求] 尝试 3/3
2025-07-28 12:39:05,877 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-28 12:39:05,881 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-28 12:39:06,287 - [MainThread] - WARNING - 获取快照时未能获取 queue_lock，使用上次快照队列数据
2025-07-28 12:39:06,287 - [MainThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-28 12:39:06,301 - [MainThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 7 个玩家, 成功更新 7 条记录.
2025-07-28 12:39:06,302 - [MainThread] - INFO - [清理] 最终数据同步已完成
2025-07-28 12:39:06,302 - [MainThread] - DEBUG - [清理] 关闭数据库会话...
2025-07-28 12:39:06,319 - [MainThread] - INFO - 已关闭会话，ID: 1
2025-07-28 12:39:06,319 - [MainThread] - INFO - [清理] 数据库会话已关闭
2025-07-28 12:39:06,320 - [MainThread] - DEBUG - [清理] 队列锁已释放
2025-07-28 12:39:06,321 - [MainThread] - INFO - [清理] 清理操作成功
2025-07-28 12:39:06,382 - [MainThread] - INFO - 程序退出
