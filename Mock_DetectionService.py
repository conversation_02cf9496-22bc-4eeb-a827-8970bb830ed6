#!/usr/bin/env python3
"""
模拟检测服务端程序
用于测试虚拟玩家功能，模拟真实的检测服务端行为
基于真实的Detect_BaseService.py实现，完全匹配网络协议和数据格式
"""

import socket
import threading
import json
import time
import logging
import random
import datetime
from typing import Dict, Any, Optional, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_timestamp_mock_detection():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"

class MockClientManager:
    """客户端连接管理器，处理订阅客户端的连接和数据推送，匹配真实ClientManager"""
    def __init__(self):
        self.clients = {}  # {client_id: socket}
        self.clients_lock = threading.Lock()
        self.next_client_id = 1

    def add_client(self, client_socket, addr):
        """添加新的客户端连接"""
        with self.clients_lock:
            client_id = self.next_client_id
            self.next_client_id += 1
            self.clients[client_id] = client_socket
            print(f"[{get_timestamp_mock_detection()}] 客户端 {client_id} 已连接，地址：{addr}")
            return client_id

    def remove_client(self, client_id):
        """移除客户端连接"""
        with self.clients_lock:
            if client_id in self.clients:
                client_socket = self.clients.pop(client_id)
                try:
                    client_socket.shutdown(socket.SHUT_RDWR)
                except OSError:
                    pass
                finally:
                    try:
                        client_socket.close()
                    except OSError:
                        pass
                print(f"[{get_timestamp_mock_detection()}] 客户端 {client_id} 已断开")

    def broadcast_results(self, detection_results):
        """向所有订阅客户端推送检测结果，完全匹配真实broadcast_results格式"""
        if not detection_results:
            return

        # 转换为推送格式，匹配真实实现
        push_data = {
            "status": "data_updated",
            "data": {
                "timestamp": detection_results.get("timestamp", time.time()),
                "fps": detection_results.get("fps", 0),
                "process_time_ms": detection_results.get("process_time_ms", 0),
                "objects": []
            }
        }

        # 转换物体信息，使用真实的字段格式
        for obj in detection_results.get("objects", []):
            push_obj = {
                "track_number": obj.get("track_number"),
                "class_id": obj.get("class_id"),
                "class_name": obj.get("class_name"),
                "center_abs_pixel": obj.get("center_abs_pixel", [0, 0]),
                "center_normalized_prop": obj.get("center_normalized_prop", [None, None]),
                "confidence": obj.get("confidence", 0.0),
                "bounding_box_xyxy_abs": obj.get("bounding_box_xyxy_abs", [0, 0, 0, 0]),
                "bounding_box_xyxy_normalized_prop": obj.get("bounding_box_xyxy_normalized_prop", [None, None, None, None])
            }
            push_data["data"]["objects"].append(push_obj)

        message = json.dumps(push_data) + '\n'
        message_bytes = message.encode('utf-8')

        # 获取当前客户端列表的副本
        with self.clients_lock:
            current_clients = list(self.clients.items())

        # 向每个客户端发送数据
        disconnected_clients = []
        for client_id, client_socket in current_clients:
            try:
                client_socket.sendall(message_bytes)
            except Exception as e:
                print(f"[{get_timestamp_mock_detection()}] 向客户端 {client_id} 推送数据失败: {e}")
                disconnected_clients.append(client_id)

        # 移除断开的客户端
        for client_id in disconnected_clients:
            self.remove_client(client_id)

class MockDetectionService:
    """模拟检测服务端，完全匹配真实Detect_BaseService的行为"""

    def __init__(self, host: str = 'localhost', port: int = 5555):
        self.host = host
        self.port = port
        self.server_socket: Optional[socket.socket] = None
        self.running = True  # 修复：初始化时设为True
        self.client_manager = MockClientManager()

        # 模拟物体类别映射，匹配真实检测系统
        self.class_names = {
            0: "小熊玩偶",
            1: "机器人模型",
            2: "卡通公仔",
            3: "毛绒兔子",
            4: "变形金刚",
            5: "Hello Kitty",
            6: "皮卡丘",
            7: "独角兽",
            8: "小黄鸭",
            9: "泰迪熊"
        }

        # FPS和性能模拟
        self.fps = 0
        self.frame_count = 0
        self.start_time = time.time()
        self.process_time_ms = 0

        # 启动数据推送线程
        self.data_thread = threading.Thread(
            target=self._data_broadcast_loop,
            daemon=True,
            name="MockDetectionDataBroadcast"
        )
        self.data_thread.start()

    def start(self):
        """启动模拟检测服务端，匹配真实Detect_BaseService的行为"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.server_socket.settimeout(0.1)  # 匹配真实实现

            print(f"[{get_timestamp_mock_detection()}] 模拟检测服务器已启动，监听地址: {self.host}:{self.port}")

            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    print(f"[{get_timestamp_mock_detection()}] 接受来自 {client_address} 的新连接")

                    # 为每个客户端启动一个处理线程，匹配真实实现
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address),
                        daemon=True,
                        name=f"MockDetectionClient-{client_address[1]}"
                    )
                    client_thread.start()

                except socket.timeout:
                    pass  # 匹配真实实现的超时处理
                except socket.error as e:
                    if self.running:
                        print(f"[{get_timestamp_mock_detection()}] 服务器循环错误: {e}")
                    break

        except Exception as e:
            print(f"[{get_timestamp_mock_detection()}] 启动检测服务器失败: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()
                print(f"[{get_timestamp_mock_detection()}] 检测服务器已关闭")
            self.stop()

    def _handle_client(self, client_socket: socket.socket, client_address):
        """处理单个客户端连接，响应订阅请求并保持连接以供数据推送，匹配真实_handle_client"""
        client_id = None
        print(f"[{get_timestamp_mock_detection()}] 接受来自 {client_address} 的新连接。")
        try:
            client_socket.settimeout(10.0)  # 设置合理的超时以接收初始请求

            # 1. 接收客户端的初始消息
            initial_data_bytes = client_socket.recv(1024)
            if not initial_data_bytes:
                print(f"[{get_timestamp_mock_detection()}] 客户端 {client_address} 连接后未发送数据，关闭连接。")
                client_socket.close()
                return

            initial_data_str = initial_data_bytes.decode('utf-8').strip()
            print(f"[{get_timestamp_mock_detection()}] 收到来自 {client_address} 的初始数据: {initial_data_str}")

            # 2. 解析JSON并检查是否为订阅命令
            is_subscribe_request = False
            try:
                request_json = json.loads(initial_data_str)
                if isinstance(request_json, dict) and request_json.get('command') == 'subscribe':
                    is_subscribe_request = True
                    print(f"[{get_timestamp_mock_detection()}] 来自 {client_address} 的请求确认为订阅命令。")
                else:
                    print(f"[{get_timestamp_mock_detection()}] 来自 {client_address} 的请求不是有效的订阅命令: {initial_data_str}")
            except json.JSONDecodeError:
                print(f"[{get_timestamp_mock_detection()}] 来自 {client_address} 的初始数据不是有效的JSON: {initial_data_str}")

            if is_subscribe_request:
                # 3. 注册客户端
                client_id = self.client_manager.add_client(client_socket, client_address)

                # 4. 构造并发送JSON确认响应，匹配真实格式
                response_data = {"status": "subscribed", "message": f"Subscription successful for client {client_id}"}
                response_json_str = json.dumps(response_data) + '\n'  # 确保有换行符
                client_socket.sendall(response_json_str.encode('utf-8'))
                print(f"[{get_timestamp_mock_detection()}] 已向客户端 {client_id} ({client_address}) 发送订阅确认。")

                # 5. 保持连接，允许ClientManager推送数据，并检测客户端断开
                client_socket.settimeout(1.0)  # 使用较短超时进行非阻塞检查
                while self.running:
                    try:
                        # 尝试接收少量数据以检测连接是否仍然存活
                        ping_data = client_socket.recv(64)
                        if not ping_data:
                            print(f"[{get_timestamp_mock_detection()}] 客户端 {client_id} ({client_address}) 主动断开连接。")
                            break
                    except socket.timeout:
                        # 超时是正常的，继续检查running状态
                        continue
                    except ConnectionResetError:
                        print(f"[{get_timestamp_mock_detection()}] 客户端 {client_id} ({client_address}) 连接被重置。")
                        break
                    except Exception as e:
                        print(f"[{get_timestamp_mock_detection()}] 与客户端 {client_id} ({client_address}) 通信发生错误: {e}")
                        break
            else:
                # 如果不是有效的订阅请求，发送错误信息并关闭连接
                print(f"[{get_timestamp_mock_detection()}] 来自 {client_address} 的请求未被处理为订阅，发送错误并关闭连接。")
                error_response = {"status": "error", "message": "Invalid or non-subscribe request"}
                error_json_str = json.dumps(error_response) + '\n'
                try:
                    client_socket.sendall(error_json_str.encode('utf-8'))
                except Exception as e_send_err:
                    print(f"[{get_timestamp_mock_detection()}] 发送错误响应给 {client_address} 失败: {e_send_err}")
                finally:
                    client_socket.close()

        except socket.timeout:
            print(f"[{get_timestamp_mock_detection()}] 客户端 {client_address} 初始请求超时。")
            try:
                client_socket.close()
            except:
                pass
        except Exception as e:
            print(f"[{get_timestamp_mock_detection()}] 处理客户端 {client_address} 时发生未预期错误: {e}")
            if client_id is None:
                try:
                    client_socket.close()
                except Exception:
                    pass
        finally:
            if client_id is not None:  # 只有成功订阅并注册的客户端才从管理器移除
                print(f"[{get_timestamp_mock_detection()}] 从管理器移除客户端 {client_id} ({client_address})。")
                self.client_manager.remove_client(client_id)
            else:
                try:
                    client_socket.close()
                    print(f"[{get_timestamp_mock_detection()}] 客户端 {client_address} 未注册，套接字已关闭。")
                except Exception:
                    pass

    def _data_broadcast_loop(self):
        """数据广播循环，模拟真实检测服务的数据推送"""
        print(f"[{get_timestamp_mock_detection()}] 模拟检测数据广播线程已启动")

        while self.running:
            try:
                # 更新FPS
                self._update_fps()

                # 生成模拟检测结果
                objects_info = self._generate_mock_detection_objects()

                # 模拟处理时间
                self.process_time_ms = random.uniform(15.0, 35.0)

                # 构造检测结果数据
                detection_results = {
                    "timestamp": time.time(),
                    "fps": self.fps,
                    "process_time_ms": self.process_time_ms,
                    "objects": objects_info
                }

                # 通过ClientManager推送给所有订阅客户端
                self.client_manager.broadcast_results(detection_results)

                # 打印检测结果（模拟真实服务的控制台输出）
                # self._print_detection_results(objects_info)  # 注释掉以减少输出

                # 模拟检测频率（约30FPS）
                time.sleep(1.0 / 30.0)

            except Exception as e:
                print(f"[{get_timestamp_mock_detection()}] 数据广播循环错误: {e}")
                time.sleep(1.0)

        print(f"[{get_timestamp_mock_detection()}] 模拟检测数据广播线程结束")

    def _update_fps(self):
        """更新FPS计算"""
        self.frame_count += 1
        elapsed_time = time.time() - self.start_time
        if elapsed_time >= 1.0:
            self.fps = self.frame_count / elapsed_time
            self.frame_count = 0
            self.start_time = time.time()

    def _generate_mock_detection_objects(self) -> List[Dict[str, Any]]:
        """生成模拟的检测物体数据，匹配真实检测结果格式"""
        objects = []

        # 随机生成2-5个物体->固定生成5个物体
        num_objects = 5  # random.randint(2, 5)
        selected_class_ids = random.sample(list(self.class_names.keys()), min(num_objects, len(self.class_names)))

        for i, class_id in enumerate(selected_class_ids):
            # 生成随机的绝对坐标
            center_x = random.uniform(200, 600)
            center_y = random.uniform(150, 450)
            width = random.uniform(50, 120)
            height = random.uniform(50, 120)

            # 计算边界框
            x1 = center_x - width / 2
            y1 = center_y - height / 2
            x2 = center_x + width / 2
            y2 = center_y + height / 2

            # 生成归一化坐标（模拟透视变换结果）
            norm_x = random.uniform(0.1, 0.9)
            norm_y = random.uniform(0.1, 0.9)
            norm_x1 = max(0.0, norm_x - 0.05)
            norm_y1 = max(0.0, norm_y - 0.05)
            norm_x2 = min(1.0, norm_x + 0.05)
            norm_y2 = min(1.0, norm_y + 0.05)

            obj_data = {
                "track_number": i + 1,  # 简单的跟踪编号
                "class_id": class_id,
                "class_name": self.class_names[class_id],
                "center_abs_pixel": [float(center_x), float(center_y)],
                "center_normalized_prop": [float(norm_x), float(norm_y)],
                "confidence": random.uniform(0.75, 0.95),
                "bounding_box_xyxy_abs": [float(x1), float(y1), float(x2), float(y2)],
                "bounding_box_xyxy_normalized_prop": [float(norm_x1), float(norm_y1), float(norm_x2), float(norm_y2)]
            }
            objects.append(obj_data)

        return objects

    def _print_detection_results(self, objects_info):
        """打印检测结果，模拟真实服务的控制台输出"""
        if not objects_info:
            # 每隔一段时间打印一次，避免刷屏
            if not hasattr(self, '_last_print_no_object_time') or time.time() - self._last_print_no_object_time > 5:
                print(f"[{get_timestamp_mock_detection()}] 当前帧未检测到物体")
                self._last_print_no_object_time = time.time()
            return

        print(f"\n[{get_timestamp_mock_detection()}] 当前检测到 {len(objects_info)} 个物体 (FPS: {self.fps:.1f}, Delay: {self.process_time_ms:.1f}ms):")
        for obj in objects_info:
            track_number = obj.get("track_number", "无")
            class_name = obj.get("class_name", "N/A")
            center_abs = obj.get("center_abs_pixel", [0, 0])
            norm_center_coords = obj.get("center_normalized_prop", [None, None])
            norm_bbox_coords = obj.get("bounding_box_xyxy_normalized_prop", [None, None, None, None])
            confidence = obj.get("confidence", 0.0)

            norm_center_str = "N/A"
            if norm_center_coords and norm_center_coords[0] is not None and norm_center_coords[1] is not None:
                norm_center_str = f"({norm_center_coords[0]:.2f}, {norm_center_coords[1]:.2f})"

            norm_bbox_str = "N/A"
            if norm_bbox_coords and all(c is not None for c in norm_bbox_coords):
                norm_bbox_str = f"({norm_bbox_coords[0]:.2f}, {norm_bbox_coords[1]:.2f}, {norm_bbox_coords[2]:.2f}, {norm_bbox_coords[3]:.2f})"

            print(f"  编号: #{track_number}, 类别: {class_name}, "
                  f"中心(绝对): ({center_abs[0]:.1f}, {center_abs[1]:.1f}), "
                  f"中心(归一化): {norm_center_str}, "
                  f"边界框(归一化): {norm_bbox_str}, "
                  f"置信度: {confidence:.2f}")

    def stop(self):
        """停止服务端"""
        print(f"[{get_timestamp_mock_detection()}] 正在停止模拟检测服务端...")
        self.running = False

        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass

        # 等待数据广播线程结束
        if hasattr(self, 'data_thread') and self.data_thread.is_alive():
            self.data_thread.join(timeout=2.0)

        print(f"[{get_timestamp_mock_detection()}] 模拟检测服务端已停止")

def main():
    """主函数"""
    print("启动模拟检测服务端...")
    print("完全匹配真实Detect_BaseService.py的行为")
    print("支持客户端订阅和数据推送")
    print("按 Ctrl+C 停止服务")

    service = MockDetectionService()
    try:
        service.start()
    except KeyboardInterrupt:
        print(f"\n[{get_timestamp_mock_detection()}] 收到停止信号，正在关闭服务...")
        service.stop()
    except Exception as e:
        print(f"[{get_timestamp_mock_detection()}] 服务运行出错: {e}")
        service.stop()

if __name__ == "__main__":
    main()
