(['C:\\Users\\<USER>\\Downloads\\code-program\\Main_Play\\Mock_DetectionService.py'],
 ['C:\\Users\\<USER>\\Downloads\\code-program\\Main_Play'],
 [],
 [('C:\\Program Files\\Python39\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('c:\\program '
   'files\\python39\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
   0),
  ('c:\\program files\\python39\\lib\\site-packages\\webview\\__pyinstaller',
   0),
  ('c:\\program '
   'files\\python39\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('c:\\program '
   'files\\python39\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.0 (tags/v3.9.0:9cf6752, Oct  5 2020, 15:34:40) [MSC v.1927 64 bit '
 '(AMD64)]',
 [('Mock_DetectionService',
   'C:\\Users\\<USER>\\Downloads\\code-program\\Main_Play\\Mock_DetectionService.py',
   'PYSOURCE')],
 [('tokenize', 'c:\\program files\\python39\\lib\\tokenize.py', 'PYMODULE'),
  ('argparse', 'c:\\program files\\python39\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python39\\lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python39\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'c:\\program files\\python39\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'c:\\program files\\python39\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'c:\\program files\\python39\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python39\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'c:\\program files\\python39\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python39\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python39\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'c:\\program files\\python39\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email', 'c:\\program files\\python39\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python39\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python39\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python39\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python39\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'c:\\program files\\python39\\lib\\calendar.py', 'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python39\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python39\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python39\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python39\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'c:\\program files\\python39\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python39\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python39\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'c:\\program files\\python39\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'c:\\program files\\python39\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python39\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'c:\\program files\\python39\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python39\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python39\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python39\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python39\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python39\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python39\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'c:\\program files\\python39\\lib\\csv.py', 'PYMODULE'),
  ('contextlib', 'c:\\program files\\python39\\lib\\contextlib.py', 'PYMODULE'),
  ('struct', 'c:\\program files\\python39\\lib\\struct.py', 'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python39\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'c:\\program files\\python39\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python39\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'c:\\program files\\python39\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'c:\\program files\\python39\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'c:\\program files\\python39\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python39\\lib\\fnmatch.py', 'PYMODULE'),
  ('copy', 'c:\\program files\\python39\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python39\\lib\\gettext.py', 'PYMODULE'),
  ('token', 'c:\\program files\\python39\\lib\\token.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python39\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'c:\\program files\\python39\\lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python39\\lib\\selectors.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python39\\lib\\signal.py', 'PYMODULE'),
  ('tracemalloc',
   'c:\\program files\\python39\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('pickle', 'c:\\program files\\python39\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python39\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python39\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python39\\lib\\_py_abc.py', 'PYMODULE'),
  ('typing', 'c:\\program files\\python39\\lib\\typing.py', 'PYMODULE'),
  ('datetime', 'c:\\program files\\python39\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'c:\\program files\\python39\\lib\\_strptime.py', 'PYMODULE'),
  ('random', 'c:\\program files\\python39\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'c:\\program files\\python39\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'c:\\program files\\python39\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python39\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'c:\\program files\\python39\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions', 'c:\\program files\\python39\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python39\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python39\\lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'c:\\program files\\python39\\lib\\bisect.py', 'PYMODULE'),
  ('logging',
   'c:\\program files\\python39\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('json', 'c:\\program files\\python39\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python39\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python39\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python39\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('threading', 'c:\\program files\\python39\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python39\\lib\\_threading_local.py',
   'PYMODULE'),
  ('socket', 'c:\\program files\\python39\\lib\\socket.py', 'PYMODULE')],
 [('python39.dll', 'c:\\program files\\python39\\python39.dll', 'BINARY'),
  ('unicodedata.pyd',
   'c:\\program files\\python39\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'c:\\program files\\python39\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'c:\\program files\\python39\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'c:\\program files\\python39\\DLLs\\select.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'c:\\program files\\python39\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\program files\\python39\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'c:\\program files\\python39\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'c:\\program files\\python39\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\program files\\python39\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Downloads\\code-program\\Main_Play\\build\\Mock_DetectionService\\base_library.zip',
   'DATA')],
 [('heapq', 'c:\\program files\\python39\\lib\\heapq.py', 'PYMODULE'),
  ('locale', 'c:\\program files\\python39\\lib\\locale.py', 'PYMODULE'),
  ('operator', 'c:\\program files\\python39\\lib\\operator.py', 'PYMODULE'),
  ('linecache', 'c:\\program files\\python39\\lib\\linecache.py', 'PYMODULE'),
  ('enum', 'c:\\program files\\python39\\lib\\enum.py', 'PYMODULE'),
  ('re', 'c:\\program files\\python39\\lib\\re.py', 'PYMODULE'),
  ('weakref', 'c:\\program files\\python39\\lib\\weakref.py', 'PYMODULE'),
  ('ntpath', 'c:\\program files\\python39\\lib\\ntpath.py', 'PYMODULE'),
  ('stat', 'c:\\program files\\python39\\lib\\stat.py', 'PYMODULE'),
  ('io', 'c:\\program files\\python39\\lib\\io.py', 'PYMODULE'),
  ('functools', 'c:\\program files\\python39\\lib\\functools.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'c:\\program files\\python39\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'c:\\program files\\python39\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'c:\\program files\\python39\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'c:\\program files\\python39\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'c:\\program files\\python39\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'c:\\program files\\python39\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'c:\\program files\\python39\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'c:\\program files\\python39\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'c:\\program files\\python39\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'c:\\program files\\python39\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'c:\\program files\\python39\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'c:\\program files\\python39\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'c:\\program files\\python39\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'c:\\program files\\python39\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'c:\\program files\\python39\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'c:\\program files\\python39\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'c:\\program files\\python39\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'c:\\program files\\python39\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'c:\\program files\\python39\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'c:\\program files\\python39\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'c:\\program files\\python39\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'c:\\program files\\python39\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'c:\\program files\\python39\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'c:\\program files\\python39\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'c:\\program files\\python39\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'c:\\program files\\python39\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'c:\\program files\\python39\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'c:\\program files\\python39\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'c:\\program files\\python39\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'c:\\program files\\python39\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'c:\\program files\\python39\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'c:\\program files\\python39\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'c:\\program files\\python39\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'c:\\program files\\python39\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'c:\\program files\\python39\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'c:\\program files\\python39\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'c:\\program files\\python39\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'c:\\program files\\python39\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'c:\\program files\\python39\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'c:\\program files\\python39\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'c:\\program files\\python39\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'c:\\program files\\python39\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'c:\\program files\\python39\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'c:\\program files\\python39\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'c:\\program files\\python39\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'c:\\program files\\python39\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'c:\\program files\\python39\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'c:\\program files\\python39\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'c:\\program files\\python39\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'c:\\program files\\python39\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'c:\\program files\\python39\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'c:\\program files\\python39\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'c:\\program files\\python39\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'c:\\program files\\python39\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'c:\\program files\\python39\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'c:\\program files\\python39\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'c:\\program files\\python39\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'c:\\program files\\python39\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'c:\\program files\\python39\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'c:\\program files\\python39\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'c:\\program files\\python39\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'c:\\program files\\python39\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'c:\\program files\\python39\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'c:\\program files\\python39\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'c:\\program files\\python39\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'c:\\program files\\python39\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'c:\\program files\\python39\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'c:\\program files\\python39\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'c:\\program files\\python39\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'c:\\program files\\python39\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'c:\\program files\\python39\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'c:\\program files\\python39\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'c:\\program files\\python39\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'c:\\program files\\python39\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'c:\\program files\\python39\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'c:\\program files\\python39\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'c:\\program files\\python39\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'c:\\program files\\python39\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'c:\\program files\\python39\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'c:\\program files\\python39\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'c:\\program files\\python39\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'c:\\program files\\python39\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'c:\\program files\\python39\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'c:\\program files\\python39\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'c:\\program files\\python39\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'c:\\program files\\python39\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'c:\\program files\\python39\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'c:\\program files\\python39\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'c:\\program files\\python39\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'c:\\program files\\python39\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'c:\\program files\\python39\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'c:\\program files\\python39\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'c:\\program files\\python39\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'c:\\program files\\python39\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'c:\\program files\\python39\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'c:\\program files\\python39\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'c:\\program files\\python39\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'c:\\program files\\python39\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'c:\\program files\\python39\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'c:\\program files\\python39\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'c:\\program files\\python39\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'c:\\program files\\python39\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'c:\\program files\\python39\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'c:\\program files\\python39\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'c:\\program files\\python39\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'c:\\program files\\python39\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'c:\\program files\\python39\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'c:\\program files\\python39\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('_bootlocale',
   'c:\\program files\\python39\\lib\\_bootlocale.py',
   'PYMODULE'),
  ('sre_parse', 'c:\\program files\\python39\\lib\\sre_parse.py', 'PYMODULE'),
  ('reprlib', 'c:\\program files\\python39\\lib\\reprlib.py', 'PYMODULE'),
  ('os', 'c:\\program files\\python39\\lib\\os.py', 'PYMODULE'),
  ('warnings', 'c:\\program files\\python39\\lib\\warnings.py', 'PYMODULE'),
  ('copyreg', 'c:\\program files\\python39\\lib\\copyreg.py', 'PYMODULE'),
  ('traceback', 'c:\\program files\\python39\\lib\\traceback.py', 'PYMODULE'),
  ('sre_constants',
   'c:\\program files\\python39\\lib\\sre_constants.py',
   'PYMODULE'),
  ('genericpath',
   'c:\\program files\\python39\\lib\\genericpath.py',
   'PYMODULE'),
  ('collections.abc',
   'c:\\program files\\python39\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'c:\\program files\\python39\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('keyword', 'c:\\program files\\python39\\lib\\keyword.py', 'PYMODULE'),
  ('sre_compile',
   'c:\\program files\\python39\\lib\\sre_compile.py',
   'PYMODULE'),
  ('codecs', 'c:\\program files\\python39\\lib\\codecs.py', 'PYMODULE'),
  ('_weakrefset',
   'c:\\program files\\python39\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('abc', 'c:\\program files\\python39\\lib\\abc.py', 'PYMODULE'),
  ('types', 'c:\\program files\\python39\\lib\\types.py', 'PYMODULE'),
  ('_collections_abc',
   'c:\\program files\\python39\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('posixpath', 'c:\\program files\\python39\\lib\\posixpath.py', 'PYMODULE')])
