# 直播提示词栏目功能开发需求书

## 1. 背景与目标

本需求旨在完善游戏助手GUI中“直播提示词”栏目，使其能够根据配置和游戏进程，动态、分区、智能地展示解说词和奖品提示词，并支持灵活的轮换与分级显示，提升直播互动体验和观赏性。

---

## 2. 需求概述

### 2.1 功能目标

- 直播提示词栏目分为上下两部分，分别显示“通用提示词”和“奖品级别提示词”。
- 支持多级奖品提示词（包括未中奖的“奖品级别0”）。
- 每次玩家抓取结果（object_picked事件）后，通用和当前奖品级别部分各自只轮换一行，显示行数保持不变。
- 轮换状态在下一次object_picked事件前保持不变，不因game_start或game_end等事件清空。
- 支持配置显示行数、分隔线或颜色区分等UI细节。

---

## 3. 详细需求

### 3.1 直播提示词文档格式

- 文档分为多个段落，格式如下：
  - `# 通用`：通用提示词段落
  - `#奖品级别0`：未中奖提示词
  - `#奖品级别1`、`#奖品级别2`...：不同奖品等级的提示词
- 每个段落下有多行提示句子。

### 3.2 显示逻辑

#### 3.2.1 分区显示

- 上半部分：显示“# 通用”段落下的若干行（行数由配置项 `commentary_panel_general_lines` 决定）。
- 下半部分：根据当前玩家抓到的奖品次数，显示对应“#奖品级别X”段落的若干行（行数可配置）。
  - 未中奖时，显示“#奖品级别0”段落。
  - 若抓到次数超过最大等级，显示最后一个等级的提示词。

#### 3.2.2 轮换机制

- 每次object_picked事件发生时：
  - 通用部分：只轮换一行（即把当前显示的第1行移到末尾，其余行顺序前移），显示行数不变。
  - 当前奖品级别部分：只轮换一行（同上），各奖品级别互不影响。
- 轮换状态在下一次object_picked事件前保持不变，不因game_start、game_end等事件清空。
- 每个奖品级别的轮换索引独立维护，不跨级别轮换。

#### 3.2.3 UI表现

- 上下两部分之间用一条细分隔线或不同颜色区分。
- 支持配置字体、颜色等UI细节。

---

## 4. 配置项

- `commentary_file`：直播提示词文档路径（如“直播词提示.txt”）。
- `displayer.layout.commentary_panel_general_lines`：通用部分显示行数。
- `displayer.layout.commentary_panel_prize_lines`：奖品部分显示行数（如需新增）。
- 其它字体、颜色等见config_play.yaml。

---

## 5. 事件驱动

- 仅在object_picked事件发生时，进行轮换并刷新显示。
- 其它事件（如game_start、game_end）不影响提示词显示和轮换状态。

---

## 6. 代码实现要点

- 解析直播提示词文档为结构化数据，按段落分组。
- 维护每个段落的当前轮换索引。
- object_picked事件时，通用和当前奖品级别各自轮换一行。
- GUI分区显示，支持分隔线或颜色区分。
- 支持配置显示行数、字体、颜色等。

---

## 7. 示例

### 7.1 直播词提示.txt 示例

```
# 通用
通用提示词一
通用提示词二
通用提示词三
通用提示词四
通用提示词五

#奖品级别0
级别0提示词一
级别0提示词二
...

#奖品级别1
级别1提示词一
...
```

### 7.2 配置示例

```yaml
displayer:
  layout:
    commentary_panel_general_lines: 4
    commentary_panel_prize_lines: 2  # 可选
```

---

## 8. 兼容性与扩展

- 支持任意数量的奖品级别。
- 支持后续扩展如多语言、动态加载等。

---

## 9. 非功能需求

- 代码需具备良好可读性和可维护性。
- 需有异常处理，保证文件缺失或格式异常时有友好提示。

---

## 10. 交付物

- 代码实现及注释
- 配置示例
- 直播提示词文档示例
- 简要使用说明

