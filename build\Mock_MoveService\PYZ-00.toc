('C:\\Users\\<USER>\\Downloads\\code-program\\Main_Play\\build\\Mock_MoveService\\PYZ-00.pyz',
 [('_compat_pickle',
   'c:\\program files\\python39\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\program files\\python39\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python39\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python39\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'c:\\program files\\python39\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python39\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'c:\\program files\\python39\\lib\\argparse.py', 'PYMODULE'),
  ('base64', 'c:\\program files\\python39\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'c:\\program files\\python39\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'c:\\program files\\python39\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'c:\\program files\\python39\\lib\\calendar.py', 'PYMODULE'),
  ('configparser',
   'c:\\program files\\python39\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python39\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'c:\\program files\\python39\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'c:\\program files\\python39\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'c:\\program files\\python39\\lib\\csv.py', 'PYMODULE'),
  ('datetime', 'c:\\program files\\python39\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'c:\\program files\\python39\\lib\\decimal.py', 'PYMODULE'),
  ('email', 'c:\\program files\\python39\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python39\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python39\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python39\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python39\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python39\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python39\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python39\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python39\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python39\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python39\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python39\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python39\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python39\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python39\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python39\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python39\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'c:\\program files\\python39\\lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'c:\\program files\\python39\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python39\\lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python39\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python39\\lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'c:\\program files\\python39\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python39\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python39\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('json', 'c:\\program files\\python39\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python39\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python39\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python39\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'c:\\program files\\python39\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'c:\\program files\\python39\\lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python39\\lib\\numbers.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python39\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'c:\\program files\\python39\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'c:\\program files\\python39\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python39\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'c:\\program files\\python39\\lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'c:\\program files\\python39\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python39\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'c:\\program files\\python39\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python39\\lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python39\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python39\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'c:\\program files\\python39\\lib\\socket.py', 'PYMODULE'),
  ('statistics', 'c:\\program files\\python39\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'c:\\program files\\python39\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python39\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'c:\\program files\\python39\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'c:\\program files\\python39\\lib\\tarfile.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python39\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'c:\\program files\\python39\\lib\\threading.py', 'PYMODULE'),
  ('token', 'c:\\program files\\python39\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'c:\\program files\\python39\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'c:\\program files\\python39\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'c:\\program files\\python39\\lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'c:\\program files\\python39\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python39\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('uu', 'c:\\program files\\python39\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'c:\\program files\\python39\\lib\\zipfile.py', 'PYMODULE')])
