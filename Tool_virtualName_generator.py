import os
import random

# 文件路径
name_file = "网名列表.txt"
output_file = "托玩家列表.txt"

# 读取网名列表
with open(name_file, "r", encoding="utf-8") as f:
    names = [line.strip().replace(" ", "") for line in f if line.strip()]

# 去重
unique_names = list(set(names))

# 随机打乱顺序
random.shuffle(unique_names)

# 生成输出内容
output_lines = []
for idx, name in enumerate(unique_names, start=1):
    output_lines.append(f"{name} VirtualPlayerID{idx:04d}")

# 写入托玩家列表（覆盖原文件）
with open(output_file, "w", encoding="utf-8") as f:
    f.write("\n".join(output_lines))