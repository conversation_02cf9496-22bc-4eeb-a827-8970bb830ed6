#!/usr/bin/env python3
"""
Tool_web2OBSpreview_simple.py - 简化版OBS WebDisplay 预览工具

直接启动HTTP和WebSocket服务器，不依赖复杂的WebDisplayProcess
"""

import sys
import yaml
import random
import time
import json
import socket
import threading
import asyncio
import websockets
from pathlib import Path
from typing import Dict, List, Any, Set
from http.server import SimpleHTTPRequestHandler
import socketserver
import os

# 确保可以从当前目录导入模块
sys.path.append(str(Path(__file__).parent.absolute()))

# 导入Play_webdisplay模块
from Play_webdisplay import WebDisplayProcess

# 简化的日志记录器
import logging

def get_logger(name: str) -> logging.Logger:
    """简化的日志记录器"""
    logger = logging.getLogger(name)
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    return logger

logger = get_logger('SimpleOBSPreviewTool')

class SimpleOBSPreviewTool:
    """简化版OBS预览工具"""
    
    def __init__(self):
        self.config = None
        self.config_path = 'config_play.yaml'
        self.config_last_modified = 0
        self.tcp_port = 5558
        self.http_port = 5559
        self.websocket_port = 5560
        self.connected_websockets: Set[Any] = set()
        self.http_server_thread = None
        self.websocket_server_task = None
        
    def load_config(self, config_path='config_play.yaml'):
        """加载配置文件"""
        try:
            self.config_path = config_path

            # 检查文件修改时间
            import os
            current_modified = os.path.getmtime(config_path)

            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)

            # 更新修改时间
            self.config_last_modified = current_modified

            # 获取端口配置
            web_config = self.config.get('web_display', {})
            self.tcp_port = web_config.get('tcp_port', web_config.get('websocket_port', 5558))
            self.websocket_port = web_config.get('websocket_port', 5560)

            http_config = web_config.get('http_server', {})
            self.http_port = http_config.get('port', 5559)

            logger.info(f"配置加载成功 - TCP:{self.tcp_port}, WebSocket:{self.websocket_port}, HTTP:{self.http_port}")
            return True

        except FileNotFoundError:
            logger.error(f"错误：找不到配置文件 '{config_path}'。")
            return False
        except Exception as e:
            logger.error(f"加载或解析配置文件时出错: {e}")
            return False

    def check_and_reload_config(self):
        """检查配置文件是否有更新，如果有则重新加载"""
        try:
            import os
            current_modified = os.path.getmtime(self.config_path)

            if current_modified > self.config_last_modified:
                logger.info("检测到配置文件更新，正在重新加载...")
                if self.load_config(self.config_path):
                    # 重新生成HTML文件
                    if self.create_processed_html():
                        logger.info("配置重新加载成功，HTML文件已更新")
                        return True
                    else:
                        logger.error("HTML文件更新失败")
                        return False
                else:
                    logger.error("配置重新加载失败")
                    return False
            return True

        except Exception as e:
            logger.error(f"检查配置文件更新时出错: {e}")
            return False



    def create_processed_html(self):
        """创建处理过的HTML文件，使用Play_webdisplay.py的HTML生成功能"""
        try:
            # 创建一个临时的WebDisplayProcess实例，但不启动它
            # 只使用其HTML生成功能
            webdisplay_process = WebDisplayProcess(self.config)
            # 直接调用HTML生成方法，避免启动完整的进程
            processed_html = webdisplay_process.create_html_content()

            # 保存处理过的HTML文件，统一使用webdisplay.html
            with open('webdisplay.html', 'w', encoding='utf-8') as f:
                f.write(processed_html)

            logger.info("已更新HTML文件: webdisplay.html")
            return True

        except Exception as e:
            logger.error(f"创建处理过的HTML文件失败: {e}")
            return False

    def start_http_server(self):
        """启动HTTP服务器"""
        def run_server():
            original_cwd = os.getcwd()
            try:
                # 切换到脚本目录
                script_dir = Path(__file__).parent
                os.chdir(script_dir)

                # 创建处理过的HTML文件
                if not self.create_processed_html():
                    logger.error("无法创建处理过的HTML文件，HTTP服务器启动失败")
                    return

                class CustomHTTPHandler(SimpleHTTPRequestHandler):
                    def log_message(self, format_str, *args):
                        pass  # 减少日志输出

                with socketserver.TCPServer(("", self.http_port), CustomHTTPHandler) as httpd:
                    logger.info(f"HTTP服务器已启动，端口: {self.http_port}")
                    logger.info(f"OBS浏览器源URL: http://127.0.0.1:{self.http_port}/webdisplay.html")
                    httpd.serve_forever()
            except Exception as e:
                logger.error(f"HTTP服务器启动失败: {e}")
            finally:
                os.chdir(original_cwd)

        self.http_server_thread = threading.Thread(target=run_server, daemon=True)
        self.http_server_thread.start()

    async def websocket_handler(self, websocket):
        """处理WebSocket连接"""
        logger.info(f"WebSocket客户端连接: {websocket.remote_address}")
        self.connected_websockets.add(websocket)
        try:
            await websocket.wait_closed()
        except websockets.exceptions.ConnectionClosed:
            # 正常的连接关闭，不记录错误
            pass
        except Exception as e:
            # 只记录真正的错误，忽略常见的握手失败
            error_msg = str(e).lower()
            if "handshake" not in error_msg and "eof" not in error_msg:
                logger.warning(f"WebSocket连接异常: {e}")
        finally:
            if websocket in self.connected_websockets:
                self.connected_websockets.remove(websocket)
            logger.debug(f"WebSocket客户端断开: {websocket.remote_address}")  # 改为debug级别

    async def broadcast_to_websockets(self, message: Dict[str, Any]):
        """广播消息到所有WebSocket客户端"""
        if not self.connected_websockets:
            logger.debug("没有WebSocket客户端连接")
            return

        message_json = json.dumps(message, ensure_ascii=False)
        logger.debug(f"广播消息到 {len(self.connected_websockets)} 个WebSocket客户端")
        
        tasks = [client.send(message_json) for client in self.connected_websockets]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        failed_clients = []
        for client, result in zip(list(self.connected_websockets), results):
            if isinstance(result, Exception):
                logger.warning(f"向客户端发送消息失败: {result}")
                failed_clients.append(client)
        
        for client in failed_clients:
            if client in self.connected_websockets:
                self.connected_websockets.remove(client)

    async def start_websocket_server(self):
        """启动WebSocket服务器"""
        logger.info(f"正在启动WebSocket服务器，端口: {self.websocket_port}")
        try:
            # 添加更好的错误处理，忽略常见的连接错误
            import logging
            websockets_logger = logging.getLogger('websockets')
            websockets_logger.setLevel(logging.ERROR)  # 只显示严重错误

            async with websockets.serve(self.websocket_handler, "0.0.0.0", self.websocket_port):
                logger.info(f"WebSocket服务器已启动，监听端口: {self.websocket_port}")
                await asyncio.Future()  # 永远运行
        except Exception as e:
            logger.error(f"WebSocket服务器启动失败: {e}")

    def start_websocket_in_thread(self):
        """在线程中启动WebSocket服务器"""
        def run_websocket():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.start_websocket_server())
            except Exception as e:
                logger.error(f"WebSocket服务器线程出错: {e}")
        
        websocket_thread = threading.Thread(target=run_websocket, daemon=True)
        websocket_thread.start()
        return websocket_thread

    def send_tcp_command(self, command: Dict[str, Any]) -> bool:
        """向TCP服务器发送命令（模拟）"""
        try:
            # 直接广播到WebSocket客户端
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.broadcast_to_websockets(command))
            logger.debug(f"发送命令成功: {command['action']}")
            return True
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False

    def generate_dummy_grab_area_data(self) -> Dict[str, Any]:
        """生成用于测试抓取区域可视化的数据"""
        logger.info("生成抓取区域模拟数据...")

        # 模拟变换后的ROI边界点 (一个矩形)
        roi_points = [
            [100.0, 100.0], [700.0, 100.0], [700.0, 500.0], [100.0, 500.0]
        ]

        # 模拟几个变换后的物体
        objects = []
        for i in range(5):
            x = 150 + i * 110
            y = 200 + random.randint(-50, 50)
            size = random.randint(60, 90)
            objects.append({
                'id': i + 1,
                'class_id': i % 3,  # 模拟3个不同的类别
                'bbox_transformed_axis_aligned': [x, y, x + size, y + size]
            })

        return {
            'action': 'update_detection_display',
            'data': {
                'objects': objects,
                'roi_boundary_points_transformed': roi_points,
                'config': self.config.get('web_display', {}).get('grab_area', {})
            }
        }

    def generate_dummy_queue_data(self) -> Dict[str, Any]:
        """生成测试队列数据"""
        names = ["🎮游戏达人", "🌟闪亮之星", "🦄独角兽", "🎯神射手", "🎪马戏团长",
                "🚀火箭手", "🎨艺术家", "🎵音乐家", "🏆冠军", "🌈彩虹"]
        items = []

        # 生成10个玩家的队列数据
        for i in range(10):
            items.append({
                "index": i + 1,  # 排位从1开始
                "name": random.choice(names),
                "comments_count": random.randint(1, 25),  # 修正字段名
                "time_min": random.randint(1, 59),  # 修正字段名
                "order_id": None  # 默认非付费玩家
            })

        # 随机让1-2个玩家成为付费玩家
        num_paid = random.randint(1, 2)
        paid_indices = random.sample(range(len(items)), num_paid)
        for idx in paid_indices:
            items[idx]['order_id'] = f"ORDER_{random.randint(1000, 9999)}"  # 付费玩家有order_id

        # 从配置文件获取队列头部信息
        queue_headers_config = self.config.get('web_display', {}).get('queue_display', {}).get('headers', {})

        return {
            "items": items,
            "show_headers": queue_headers_config.get('show', True),
            "header_text": queue_headers_config.get('text_before', "排队朋友"),
            "footer_text": queue_headers_config.get('text_after', "加关注、多评论、停留长的朋友排队优先！<br>重复评论不算；玩过后评论重新计数"),
            "headers": queue_headers_config.get('columns', {
                "index": "排位",
                "name": "网名",
                "comments": "评论",
                "time": "停留分钟"
            })
        }

    def print_instructions(self):
        """打印操作说明"""
        print("\n" + "="*60)
        print("🎬 简化版 OBS WebDisplay 预览工具")
        print("="*60)
        print(f"🌐 HTTP端口: {self.http_port} (OBS浏览器源)")
        print(f"🔗 WebSocket端口: {self.websocket_port}")
        print(f"🎯 OBS浏览器源URL: http://127.0.0.1:{self.http_port}/webdisplay.html")
        print("\n📋 可用命令:")
        print("  1 <玩家名>        - 显示正在游戏状态")
        print("  2 <玩家名> [物品] - 显示抓中状态")
        print("  3 <玩家名>        - 显示未抓中状态")
        print("  msg <文本>        - 显示自定义消息")
        print("  queue             - 刷新排队列表")
        print("  grab              - 刷新抓取区域可视化")
        print("  clear             - 清除显示内容")
        print("  demo              - 运行演示序列")
        print("  reload            - 重新加载配置文件")
        print("  test-config       - 测试动态配置更新")
        print("  help, h           - 显示此帮助")
        print("  quit, q, exit     - 退出程序")
        print("="*60)

    def test_dynamic_config_update(self):
        """测试动态配置更新功能"""
        print("\n🧪 测试动态配置更新...")

        # 测试1: 更新背景颜色
        print("  📝 测试1: 更新背景颜色...")
        self.send_tcp_command({
            'action': 'update_config',
            'config': {
                'web_display': {
                    'background_color': 'rgba(255, 0, 0, 0.3)'  # 红色半透明背景
                }
            }
        })

        import time
        time.sleep(2)

        # 测试2: 更新队列显示样式
        print("  📝 测试2: 更新队列显示样式...")
        self.send_tcp_command({
            'action': 'update_config',
            'config': {
                'web_display': {
                    'queue_display': {
                        'font_size': '32px',
                        'font_color': '#FFD700',
                        'position_x': '100px',
                        'position_y': '50px'
                    }
                }
            }
        })

        time.sleep(2)

        # 测试3: 更新提示词位置
        print("  📝 测试3: 更新提示词位置...")
        self.send_tcp_command({
            'action': 'update_config',
            'config': {
                'web_display': {
                    'display_settings': {
                        'position': {
                            'center': ['30%', '30%'],
                            'width': '60%'
                        }
                    }
                }
            }
        })

        time.sleep(2)

        # 恢复默认配置
        print("  📝 恢复默认配置...")
        web_config = self.config.get('web_display', {})
        self.send_tcp_command({
            'action': 'update_config',
            'config': {
                'web_display': {
                    'background_color': web_config.get('background_color', 'rgba(0,255,237,1)'),
                    'queue_display': web_config.get('queue_display', {}),
                    'display_settings': web_config.get('display_settings', {})
                }
            }
        })

        print("✅ 动态配置更新测试完成！")

    def run_demo_sequence(self):
        """运行演示序列"""
        print("\n🎭 开始演示序列...")
        
        demo_steps = [
            ("欢迎消息", {
                'action': 'show_message',
                'text': '🎉 简化版WebDisplay演示！',
                'animated': True,
                'duration': 3
            }),
            ("更新队列", {
                'action': 'update_queue',
                'data': self.generate_dummy_queue_data()
            }),
            ("玩家开始游戏", {
                'action': 'show_status_message',
                'status_type': 'playing',
                'player_name': '演示玩家'
            }),
            # 新增：演示抓取区域
            ("显示抓取区域", self.generate_dummy_grab_area_data()),
            ("玩家抓中奖品", {
                'action': 'show_status_message',
                'status_type': 'caught_item',
                'player_name': '演示玩家',
                'item_name': '神秘大奖',
                'success_count': 1
            }),
            ("结束消息", {
                'action': 'show_message',
                'text': '✨ 演示完成！',
                'animated': True,
                'duration': 5
            })
        ]
        
        for step_name, command in demo_steps:
            print(f"  📍 {step_name}...")
            self.send_tcp_command(command)
            time.sleep(2)
        
        print("🎭 演示序列完成！")

    def process_command(self, command_str: str):
        """处理用户输入的命令"""
        parts = command_str.split()
        if not parts:
            return
            
        cmd = parts[0].lower()
        
        if cmd == 'demo':
            self.run_demo_sequence()
            
        elif cmd == 'clear':
            self.send_tcp_command({'action': 'clear_text'})
            print("✅ 已清除显示内容")
            
        elif cmd == 'queue':
            queue_data = self.generate_dummy_queue_data()
            self.send_tcp_command({'action': 'update_queue', 'data': queue_data})
            print("✅ 已刷新排队列表")

        elif cmd == 'grab':
            grab_data = self.generate_dummy_grab_area_data()
            self.send_tcp_command(grab_data)
            print("✅ 已刷新抓取区域可视化")

        elif cmd == 'reload':
            if self.check_and_reload_config():
                print("✅ 配置文件重新加载成功")
            else:
                print("❌ 配置文件重新加载失败")

        elif cmd == 'test-config':
            self.test_dynamic_config_update()

        elif cmd == 'msg':
            if len(parts) < 2:
                print("❌ 请提供消息内容，例如: msg 欢迎来到直播间")
                return
            text = ' '.join(parts[1:])
            self.send_tcp_command({
                'action': 'show_message',
                'text': text,
                'animated': True,
                'duration': 5
            })
            print(f"✅ 已发送消息: {text}")
            
        elif cmd in ['1', '2', '3']:
            if len(parts) < 2:
                print("❌ 请提供玩家名")
                return
                
            player_name = parts[1]
            
            if cmd == '1':
                self.send_tcp_command({
                    'action': 'show_status_message',
                    'status_type': 'playing',
                    'player_name': player_name
                })
                print(f"✅ 显示 {player_name} 正在游戏")
                
            elif cmd == '2':
                item_name = parts[2] if len(parts) > 2 else "神秘奖品"
                success_count = random.randint(1, 3)
                self.send_tcp_command({
                    'action': 'show_status_message',
                    'status_type': 'caught_item',
                    'player_name': player_name,
                    'item_name': item_name,
                    'success_count': success_count
                })
                print(f"✅ 显示 {player_name} 抓中了 {item_name}")
                
            elif cmd == '3':
                self.send_tcp_command({
                    'action': 'show_status_message',
                    'status_type': 'caught_nothing',
                    'player_name': player_name
                })
                print(f"✅ 显示 {player_name} 未抓中")
                
            # 每次状态更新后刷新队列
            queue_data = self.generate_dummy_queue_data()
            self.send_tcp_command({'action': 'update_queue', 'data': queue_data})
                
        else:
            print(f"❌ 未知命令: {cmd}")
            print("💡 输入 'help' 查看可用命令")

    def run_interactive_mode(self):
        """运行交互模式"""
        self.print_instructions()
        
        # 发送初始数据
        print("\n🚀 发送初始数据...")
        queue_data = self.generate_dummy_queue_data()
        self.send_tcp_command({'action': 'update_queue', 'data': queue_data})
        print("  ✅ 初始队列数据已发送")
        
        print("\n💡 提示：现在可以在OBS中添加浏览器源来观察效果")
        print(f"   URL: http://127.0.0.1:{self.http_port}/webdisplay.html")
        
        while True:
            try:
                command_str = input("\n🎮 请输入命令 > ").strip()
                if not command_str:
                    continue

                if command_str.lower() in ['q', 'quit', 'exit']:
                    break

                if command_str.lower() in ['h', 'help']:
                    self.print_instructions()
                    continue

                self.process_command(command_str)

            except (EOFError, KeyboardInterrupt):
                print("\n👋 检测到退出信号...")
                break
            except Exception as e:
                print(f"❌ 处理指令时发生错误: {e}")

def main():
    """主函数"""
    print("🚀 简化版 OBS WebDisplay 预览工具启动中...")
    
    tool = SimpleOBSPreviewTool()
    
    # 加载配置
    if not tool.load_config():
        print("❌ 配置加载失败，程序退出")
        return
    
    # 启动HTTP服务器
    tool.start_http_server()
    
    # 启动WebSocket服务器
    tool.start_websocket_in_thread()
    
    # 等待服务器启动
    time.sleep(3)
    
    try:
        # 运行交互模式
        tool.run_interactive_mode()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    finally:
        print("🔚 程序已退出")

if __name__ == "__main__":
    main()
