#!/usr/bin/env python3
"""
Tool_web2OBSpreview.py - 高保真OBS WebDisplay预览工具

本工具直接调用 Play_webdisplay.py 的核心模块来生成预览，
确保预览效果与主程序完全一致。
"""

import sys
import yaml
import random
import time
import json
import threading
import asyncio
import websockets
from pathlib import Path
from typing import Dict, Any, Set, Optional
from http.server import SimpleHTTPRequestHandler
import socketserver
import os
# 新增：导入cv2和numpy，因为我们仍然需要它们来生成变换后的虚拟物体
import cv2
import numpy as np

# 确保可以从当前目录导入模块
sys.path.append(str(Path(__file__).parent.absolute()))

# --- 核心修改：直接从主程序模块导入WebDisplayProcess ---
from Play_webdisplay import WebDisplayProcess

# 简化的日志记录器
import logging

def get_logger(name: str) -> logging.Logger:
    """简化的日志记录器"""
    logger = logging.getLogger(name)
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    return logger

logger = get_logger('OBSPreviewTool')

class OBSPreviewTool:
    """高保真OBS预览工具"""

    def __init__(self):
        self.config = None
        self.config_path = 'config_play.yaml'
        self.config_last_modified = 0
        self.http_port = 5559
        self.websocket_port = 5560
        self.connected_websockets: Set[Any] = set()
        self.http_server_thread = None
        self.websocket_server_task = None

        # --- 新增：持有WebDisplayProcess实例作为"逻辑助手" ---
        self.web_display_helper: Optional[WebDisplayProcess] = None
        
    def load_config(self, config_path='config_play.yaml'):
        """加载配置文件，并初始化WebDisplayProcess助手"""
        try:
            self.config_path = config_path
            current_modified = os.path.getmtime(config_path)

            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)

            self.config_last_modified = current_modified

            web_config = self.config.get('web_display', {})
            self.websocket_port = web_config.get('websocket_port', 5560)
            http_config = web_config.get('http_server', {})
            self.http_port = http_config.get('port', 5559)

            # --- 核心修改：创建WebDisplayProcess实例 ---
            # 这个实例将为我们处理所有复杂的逻辑，如加载摄像头配置、计算变换矩阵等
            self.web_display_helper = WebDisplayProcess(self.config)
            logger.info("已成功创建并初始化WebDisplayProcess助手实例。")

            logger.info(f"配置加载成功 - WebSocket:{self.websocket_port}, HTTP:{self.http_port}")
            return True

        except FileNotFoundError:
            logger.error(f"错误：找不到配置文件 '{config_path}'。")
            return False
        except Exception as e:
            logger.error(f"加载或解析配置文件时出错: {e}", exc_info=True)
            return False

    def check_and_reload_config(self):
        """检查配置文件是否有更新，如果有则重新加载"""
        try:
            current_modified = os.path.getmtime(self.config_path)
            if current_modified > self.config_last_modified:
                logger.info("检测到配置文件更新，正在重新加载...")
                if self.load_config(self.config_path):
                    if self.create_processed_html():
                        logger.info("配置重新加载成功，HTML文件已更新")
                        # 发送一个配置更新命令到前端，让其重新加载
                        # self.send_command({'action': 'update_config', 'config': self.config})
                        return True
                return False
            return True
        except Exception as e:
            logger.error(f"检查配置文件更新时出错: {e}")
            return False



    def create_processed_html(self):
        """
        --- 核心修改：直接调用助手实例的方法来生成HTML ---
        """
        if not self.web_display_helper:
            logger.error("WebDisplay助手未初始化，无法创建HTML。")
            return False
        try:
            # 直接调用，获取最权威的HTML内容
            processed_html = self.web_display_helper.create_html_content()

            with open('webdisplay.html', 'w', encoding='utf-8') as f:
                f.write(processed_html)

            logger.info("已更新HTML文件: webdisplay.html")
            return True
        except Exception as e:
            logger.error(f"创建处理过的HTML文件失败: {e}", exc_info=True)
            return False

    def start_http_server(self):
        """启动HTTP服务器"""
        # (此部分逻辑不变)
        def run_server():
            original_cwd = os.getcwd()
            try:
                script_dir = Path(__file__).parent
                os.chdir(script_dir)
                if not self.create_processed_html():
                    return
                class CustomHTTPHandler(SimpleHTTPRequestHandler):
                    def log_message(self, _format_str, *_args):
                        pass  # 禁用日志输出
                with socketserver.TCPServer(("", self.http_port), CustomHTTPHandler) as httpd:
                    logger.info(f"HTTP服务器已启动，端口: {self.http_port}")
                    logger.info(f"OBS浏览器源URL: http://127.0.0.1:{self.http_port}/webdisplay.html")
                    httpd.serve_forever()
            except Exception as e:
                logger.error(f"HTTP服务器启动失败: {e}")
            finally:
                os.chdir(original_cwd)
        self.http_server_thread = threading.Thread(target=run_server, daemon=True)
        self.http_server_thread.start()

    async def websocket_handler(self, websocket):
        # (此部分逻辑不变)
        logger.info(f"WebSocket客户端连接: {websocket.remote_address}")
        self.connected_websockets.add(websocket)
        try:
            await websocket.wait_closed()
        finally:
            if websocket in self.connected_websockets:
                self.connected_websockets.remove(websocket)

    async def broadcast_to_websockets(self, message: Dict[str, Any]):
        # (此部分逻辑不变)
        if not self.connected_websockets: return
        message_json = json.dumps(message, ensure_ascii=False)
        tasks = [client.send(message_json) for client in self.connected_websockets]
        await asyncio.gather(*tasks, return_exceptions=True)

    async def start_websocket_server(self):
        # (此部分逻辑不变)
        logger.info(f"正在启动WebSocket服务器，端口: {self.websocket_port}")
        async with websockets.serve(self.websocket_handler, "0.0.0.0", self.websocket_port):
            await asyncio.Future()

    def start_websocket_in_thread(self):
        # (此部分逻辑不变)
        def run_websocket():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.start_websocket_server())
        websocket_thread = threading.Thread(target=run_websocket, daemon=True)
        websocket_thread.start()

    def send_command(self, command: Dict[str, Any]):
        """将命令广播到WebSocket"""
        # 这个工具是独立的，所以它需要自己的事件循环来发送消息
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        if loop.is_running():
            asyncio.run_coroutine_threadsafe(self.broadcast_to_websockets(command), loop)
        else:
            loop.run_until_complete(self.broadcast_to_websockets(command))

    def generate_dummy_grab_area_data(self) -> Dict[str, Any]:
        """
        --- 核心修改：使用助手实例的计算结果来生成模拟数据 ---
        """
        logger.info("生成抓取区域模拟数据...")

        if not self.web_display_helper or self.web_display_helper.perspective_transform_matrix is None:
            logger.error("变换矩阵未初始化，无法生成抓取区域数据。请检查配置。")
            return {
                'action': 'update_detection_display',
                'data': { 'objects': [], 'roi_boundary_points_transformed': [], 'config': {} }
            }

        # 从助手实例获取权威的变换矩阵和变换后的ROI点
        matrix = self.web_display_helper.perspective_transform_matrix
        roi_points_transformed = self.web_display_helper.roi_points_transformed_for_js

        # 模拟几个位于源归一化坐标系中的物体
        source_objects = [
            {'id': 1, 'class_id': 0, 'box_xyxyn': [0.2, 0.2, 0.35, 0.35]},
            {'id': 2, 'class_id': 1, 'box_xyxyn': [0.7, 0.3, 0.9, 0.5]},
            {'id': 3, 'class_id': 2, 'box_xyxyn': [0.5, 0.6, 0.68, 0.78]},
        ]

        transformed_objects = []
        for obj in source_objects:
            x1n, y1n, x2n, y2n = obj['box_xyxyn']
            src_bbox_pts = np.array([[[x1n, y1n]], [[x2n, y1n]], [[x2n, y2n]], [[x1n, y2n]]], dtype=np.float32)

            # 使用从助手实例获取的权威矩阵进行变换
            dst_bbox_pts = cv2.perspectiveTransform(src_bbox_pts, matrix)

            if dst_bbox_pts is not None:
                min_x = float(np.min(dst_bbox_pts[:, :, 0]))
                max_x = float(np.max(dst_bbox_pts[:, :, 0]))
                min_y = float(np.min(dst_bbox_pts[:, :, 1]))
                max_y = float(np.max(dst_bbox_pts[:, :, 1]))

                transformed_obj = obj.copy()
                transformed_obj['bbox_transformed_axis_aligned'] = [min_x, min_y, max_x, max_y]
                transformed_objects.append(transformed_obj)

        return {
            'action': 'update_detection_display',
            'data': {
                'objects': transformed_objects,
                'roi_boundary_points_transformed': roi_points_transformed,
                'config': self.web_display_helper.grab_area_config
            }
        }

    def generate_dummy_queue_data(self) -> Dict[str, Any]:
        # (此部分逻辑不变)
        # ...
        names = ["🎮游戏达人", "🌟闪亮之星", "🦄独角兽", "🎯神射手", "🎪马戏团长",
                "🚀火箭手", "🎨艺术家", "🎵音乐家", "🏆冠军", "🌈彩虹"]
        items = []
        for i in range(10):
            items.append({
                "index": i + 1,
                "name": random.choice(names),
                "comments_count": random.randint(1, 25),
                "time_min": random.randint(1, 59),
                "order_id": None
            })
        num_paid = random.randint(1, 2)
        paid_indices = random.sample(range(len(items)), num_paid)
        for idx in paid_indices:
            items[idx]['order_id'] = f"ORDER_{random.randint(1000, 9999)}"
        queue_headers_config = self.config.get('web_display', {}).get('queue_display', {}).get('headers', {}) if self.config else {}
        return {
            "items": items,
            "show_headers": queue_headers_config.get('show', True),
            "header_text": queue_headers_config.get('text_before', "排队朋友"),
            "footer_text": queue_headers_config.get('text_after', "加关注、多评论、停留长的朋友排队优先！<br>重复评论不算；玩过后评论重新计数"),
            "headers": queue_headers_config.get('columns', {})
        }

    def print_instructions(self):
        # (此部分逻辑不变)
        # ...
        print("\n" + "="*60)
        print("🎬 高保真 OBS WebDisplay 预览工具")
        print("="*60)
        print(f"🌐 HTTP端口: {self.http_port} (OBS浏览器源)")
        print(f"🔗 WebSocket端口: {self.websocket_port}")
        print(f"🎯 OBS浏览器源URL: http://127.0.0.1:{self.http_port}/webdisplay.html")
        print("\n📋 可用命令:")
        print("  1 <玩家名>        - 显示正在游戏状态")
        print("  2 <玩家名> [物品] - 显示抓中状态")
        print("  3 <玩家名>        - 显示未抓中状态")
        print("  msg <文本>        - 显示自定义消息")
        print("  queue             - 刷新排队列表")
        print("  grab              - 刷新抓取区域可视化")
        print("  clear             - 清除显示内容")
        print("  demo              - 运行演示序列")
        print("  reload            - 重新加载配置文件并刷新页面")
        print("  help, h           - 显示此帮助")
        print("  quit, q, exit     - 退出程序")
        print("="*60)

    # 删除这个方法，因为它不是核心功能

    def run_demo_sequence(self):
        # (此部分逻辑不变)
        # ...
        print("\n🎭 开始演示序列...")
        demo_steps = [
            ("欢迎消息", {'action': 'show_message', 'text': '🎉 高保真WebDisplay演示！', 'animated': True, 'duration': 3}),
            ("更新队列", {'action': 'update_queue', 'data': self.generate_dummy_queue_data()}),
            ("玩家开始游戏", {'action': 'show_status_message', 'status_type': 'playing', 'player_name': '演示玩家'}),
            ("显示抓取区域", self.generate_dummy_grab_area_data()),
            ("玩家抓中奖品", {'action': 'show_status_message', 'status_type': 'caught_item', 'player_name': '演示玩家', 'item_name': '神秘大奖', 'success_count': 1}),
            ("结束消息", {'action': 'show_message', 'text': '✨ 演示完成！', 'animated': True, 'duration': 5})
        ]
        for step_name, command in demo_steps:
            print(f"  📍 {step_name}...")
            self.send_command(command)
            time.sleep(2)
        print("🎭 演示序列完成！")

    def process_command(self, command_str: str):
        # (此部分逻辑不变)
        # ...
        parts = command_str.split()
        if not parts: return
        cmd = parts[0].lower()
        if cmd == 'demo': self.run_demo_sequence()
        elif cmd == 'clear': self.send_command({'action': 'clear_text'})
        elif cmd == 'queue': self.send_command({'action': 'update_queue', 'data': self.generate_dummy_queue_data()})
        elif cmd == 'grab': self.send_command(self.generate_dummy_grab_area_data())
        elif cmd == 'reload':
            if self.check_and_reload_config(): print("✅ 配置文件重载并刷新成功")
            else: print("❌ 配置文件重载失败")
        elif cmd == 'msg':
            text = ' '.join(parts[1:])
            self.send_command({'action': 'show_message', 'text': text, 'animated': True, 'duration': 5})
        elif cmd in ['1', '2', '3']:
            player_name = parts[1] if len(parts) > 1 else "测试玩家"
            if cmd == '1': self.send_command({'action': 'show_status_message', 'status_type': 'playing', 'player_name': player_name})
            elif cmd == '2':
                item_name = parts[2] if len(parts) > 2 else "神秘奖品"
                self.send_command({'action': 'show_status_message', 'status_type': 'caught_item', 'player_name': player_name, 'item_name': item_name, 'success_count': 1})
            elif cmd == '3': self.send_command({'action': 'show_status_message', 'status_type': 'caught_nothing', 'player_name': player_name})
        else: print(f"❌ 未知命令: {cmd}")

    def run_interactive_mode(self):
        # (此部分逻辑不变)
        # ...
        self.print_instructions()
        self.send_command({'action': 'update_queue', 'data': self.generate_dummy_queue_data()})
        while True:
            try:
                command_str = input("\n🎮 请输入命令 > ").strip()
                if command_str.lower() in ['q', 'quit', 'exit']: break
                if command_str.lower() in ['h', 'help']:
                    self.print_instructions()
                    continue
                self.process_command(command_str)
            except (EOFError, KeyboardInterrupt):
                break

def main():
    """主函数"""
    tool = OBSPreviewTool()
    if not tool.load_config():
        return

    tool.start_http_server()
    tool.start_websocket_in_thread()

    time.sleep(1) # 等待服务器启动

    try:
        tool.run_interactive_mode()
    finally:
        print("\n👋 程序退出。")

if __name__ == "__main__":
    main()
