import sys
import yaml
import random
import time
import json
from pathlib import Path

# 确保可以从当前目录导入 Play_webdisplay
# 将脚本所在的目录添加到 Python 路径中
sys.path.append(str(Path(__file__).parent.absolute()))

try:
    from Play_webdisplay import WebTextDisplay, get_logger
except ImportError as e:
    print(f"无法导入 Play_webdisplay: {e}")
    print("请确保 tool_webview_preview.py 与 Play_webdisplay.py 在同一个目录下。")
    sys.exit(1)

logger = get_logger('WebViewPreviewTool')

def load_config_and_enable_grab_area(config_path='config_play.yaml'):
    """
    加载配置文件，并启用抓取区域可视化用于测试。
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 启用抓取区域可视化用于测试
        if 'web_display' in config and 'grab_area' in config['web_display']:
            config['web_display']['grab_area']['enabled'] = True
            logger.info("在预览模式下，已启用抓取区域可视化用于测试。")

        # 修改端口以避免冲突
        if 'web_display' in config:
            config['web_display']['websocket_port'] = 5559  # 使用不同的端口
            logger.info("预览模式使用端口 5559")

        return config
    except FileNotFoundError:
        logger.error(f"错误：找不到配置文件 '{config_path}'。")
        return None
    except Exception as e:
        logger.error(f"加载或解析配置文件时出错: {e}")
        return None

def load_livecam_config(config_path='Config_livecam.yaml'):
    """
    加载摄像头配置文件以获取ROI点信息。
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        logger.warning(f"找不到摄像头配置文件 '{config_path}'，将使用默认ROI点。")
        return None
    except Exception as e:
        logger.error(f"加载摄像头配置文件时出错: {e}")
        return None

def generate_dummy_queue_data(config):
    """
    生成用于测试的虚拟排队列表数据。
    """
    queue_config = config.get('web_display', {}).get('queue_display', {})
    if not queue_config.get('enabled', False):
        return None

    names = ["飞翔的企鹅", "快乐的小熊", "沉思的猫头鹰", "跳舞的狐狸", "唱歌的鲸鱼", "奔跑的兔子", "害羞的松鼠"]
    items = []
    # 使用配置中的max_items或默认5个
    for i in range(queue_config.get('max_items', 5)):
        items.append({
            "index": i + 1,
            "name": random.choice(names),
            "comments_count": random.randint(1, 25),
            "time_min": f"{random.randint(1, 59)}m",
            "order_id": None # 默认为免费玩家
        })
    
    # 随机让一个玩家成为付费玩家以测试样式
    if items:
        paid_player_index = random.randint(0, len(items) - 1)
        items[paid_player_index]['order_id'] = 'dummy_order_123'
        # 移除付费文字提示，付费样式由配置文件控制

    headers_config = queue_config.get('headers', {})
    
    # 构造JS期望的完整数据结构
    queue_data = {
        "items": items,
        "header_text": headers_config.get('text_before', '排队网友'),
        "footer_text": headers_config.get('text_after', '').replace('\n', '<br>'),
        "show_headers": headers_config.get('show', True),
        "headers": headers_config.get('columns', {})
    }
    return queue_data

def generate_dummy_grab_area_data(config, livecam_config):
    """
    生成用于测试的虚拟抓取区域数据，包括随机物体包围框和标签。
    """
    grab_area_config = config.get('web_display', {}).get('grab_area', {})
    if not grab_area_config.get('enabled', False):
        return None

    # 获取ROI点，如果没有livecam配置则使用默认值
    if livecam_config and 'roi_points' in livecam_config:
        roi_points = livecam_config['roi_points']
    else:
        # 默认ROI点（模拟一个矩形区域）
        roi_points = [[100, 100], [500, 100], [500, 400], [100, 400]]

    # 生成随机物体
    objects = []
    num_objects = random.randint(2, 5)

    for i in range(num_objects):
        # 在ROI区域内生成随机包围框
        min_x = min(point[0] for point in roi_points)
        max_x = max(point[0] for point in roi_points)
        min_y = min(point[1] for point in roi_points)
        max_y = max(point[1] for point in roi_points)

        # 生成包围框坐标
        x1 = random.uniform(min_x, max_x - 50)
        y1 = random.uniform(min_y, max_y - 50)
        x2 = x1 + random.uniform(30, 100)
        y2 = y1 + random.uniform(30, 100)

        # 确保包围框在ROI范围内
        x2 = min(x2, max_x)
        y2 = min(y2, max_y)

        objects.append({
            'id': f'obj_{i+1}',
            'class_id': random.randint(0, 2),
            'bbox_transformed_axis_aligned': [x1, y1, x2, y2]
        })

    grab_area_data = {
        'objects': objects,
        'roi_boundary_points_transformed': roi_points,
        'config': grab_area_config
    }

    return grab_area_data

def print_instructions():
    """打印操作说明"""
    print("\n--- WebView 预览工具 ---")
    print("输入指令来测试不同的显示效果。")
    print("格式: <状态码> <玩家名> [物品名]")
    print("状态码:")
    print("  1: 正在玩 (例如: 1 玩家张三)")
    print("  2: 抓中了 (例如: 2 玩家李四 螃蟹)")
    print("  3: 未抓中 (例如: 3 玩家王五)")
    print("  g: 生成新的抓取区域测试数据")
    print("其他命令:")
    print("  q, quit, exit: 退出程序")
    print("  h, help: 显示此帮助信息")
    print("------------------------")

def main():
    """主函数"""
    print("开始加载配置...")
    config = load_config_and_enable_grab_area()
    if not config:
        print("配置加载失败")
        return

    # 加载摄像头配置
    livecam_config = load_livecam_config()

    # 确保 web_display 是启用的
    if not config.get('web_display', {}).get('enabled', False):
        print("配置中 web_display 未启用，无法启动预览工具。")
        return

    print("创建WebTextDisplay实例...")
    web_display = WebTextDisplay(config)

    print("正在启动Web显示进程...")
    if not web_display.start():
        print("Web显示进程启动失败。")
        return

    print("Web显示已启动。")
    time.sleep(2) # 等待窗口和JS完全加载

    try:
        # 初始显示排队列表
        dummy_queue = generate_dummy_queue_data(config)
        if dummy_queue:
            web_display.update_queue(dummy_queue)
            print("已发送初始虚拟排队列表。")

        # 初始显示抓取区域数据
        dummy_grab_area = generate_dummy_grab_area_data(config, livecam_config)
        if dummy_grab_area:
            # 模拟发送抓取区域数据
            web_display.update_detection_display([])  # 先发送空数据初始化
            print("已发送初始虚拟抓取区域数据。")

        print_instructions()

        while True:
            try:
                command_str = input("请输入指令 > ").strip()
                if not command_str:
                    continue

                if command_str.lower() in ['q', 'quit', 'exit']:
                    break

                if command_str.lower() in ['h', 'help']:
                    print_instructions()
                    continue

                if command_str.lower() == 'g':
                    # 生成新的抓取区域测试数据
                    dummy_grab_area = generate_dummy_grab_area_data(config, livecam_config)
                    if dummy_grab_area:
                        # 通过发送自定义命令来更新抓取区域
                        web_display._send_command({
                            'action': 'custom_js',
                            'js_code': f'updateGrabAreaDisplay({json.dumps(dummy_grab_area, ensure_ascii=False)});'
                        })
                        print("已生成新的抓取区域测试数据。")
                    else:
                        print("抓取区域功能未启用。")
                    continue

                parts = command_str.split()
                if len(parts) < 2:
                    print("指令格式错误。至少需要 <状态码> 和 <玩家名>。")
                    print_instructions()
                    continue

                status_code = parts[0]
                player_name = parts[1]
                item_name = parts[2] if len(parts) > 2 else "神秘礼物"

                if status_code == '1':
                    print(f"显示 'playing' 状态: {player_name}")
                    web_display.show_status_message('playing', player_name)
                elif status_code == '2':
                    print(f"显示 'caught_item' 状态: {player_name}, {item_name}")
                    web_display.show_status_message('caught_item', player_name, item_name)
                elif status_code == '3':
                    print(f"显示 'caught_nothing' 状态: {player_name}")
                    web_display.show_status_message('caught_nothing', player_name)
                else:
                    print(f"未知状态码: {status_code}")
                    print_instructions()

                # 每次操作后都刷新一下排队列表，模拟动态变化
                dummy_queue = generate_dummy_queue_data(config)
                if dummy_queue:
                    web_display.update_queue(dummy_queue)

                # 每次操作后也刷新抓取区域数据
                dummy_grab_area = generate_dummy_grab_area_data(config, livecam_config)
                if dummy_grab_area:
                    web_display._send_command({
                        'action': 'custom_js',
                        'js_code': f'updateGrabAreaDisplay({json.dumps(dummy_grab_area, ensure_ascii=False)});'
                    })

            except (EOFError, KeyboardInterrupt):
                print("\n检测到退出信号...")
                break
            except Exception as e:
                print(f"处理指令时发生错误: {e}")

    except KeyboardInterrupt:
        print("\n检测到退出信号...")
    finally:
        print("正在关闭Web显示...")
        web_display.destroy()
        print("程序已退出。")

if __name__ == "__main__":
    main()
