#!/usr/bin/env python3
"""
演示修改后的 Tool_web2OBSpreview.py 的功能
"""

import sys
import time
from pathlib import Path

# 确保可以从当前目录导入模块
sys.path.append(str(Path(__file__).parent.absolute()))

from Tool_web2OBSpreview import OBSPreviewTool

def main():
    print("🚀 启动高保真 OBS WebDisplay 预览工具演示...")
    
    # 创建工具实例
    tool = OBSPreviewTool()
    
    # 加载配置
    if not tool.load_config():
        print("❌ 配置加载失败，演示退出")
        return
    
    print("✅ 配置加载成功")
    
    # 启动服务器
    print("🌐 启动 HTTP 服务器...")
    tool.start_http_server()
    
    print("🔗 启动 WebSocket 服务器...")
    tool.start_websocket_in_thread()
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    print(f"\n🎯 OBS 浏览器源 URL: http://127.0.0.1:{tool.http_port}/webdisplay.html")
    print("💡 请在 OBS 中添加浏览器源，使用上述 URL")
    
    # 运行演示序列
    print("\n🎭 开始演示序列...")
    
    # 1. 显示欢迎消息
    print("  📍 显示欢迎消息...")
    tool.send_command({
        'action': 'show_message',
        'text': '🎉 高保真 WebDisplay 演示开始！',
        'animated': True,
        'duration': 3
    })
    time.sleep(3)
    
    # 2. 更新队列
    print("  📍 更新队列显示...")
    queue_data = tool.generate_dummy_queue_data()
    tool.send_command({'action': 'update_queue', 'data': queue_data})
    time.sleep(2)
    
    # 3. 显示抓取区域（使用真实的透视变换）
    print("  📍 显示抓取区域（使用真实透视变换）...")
    grab_data = tool.generate_dummy_grab_area_data()
    tool.send_command(grab_data)
    time.sleep(3)
    
    # 4. 模拟玩家游戏
    print("  📍 模拟玩家开始游戏...")
    tool.send_command({
        'action': 'show_status_message',
        'status_type': 'playing',
        'player_name': '演示玩家'
    })
    time.sleep(2)
    
    # 5. 模拟抓中物品
    print("  📍 模拟玩家抓中物品...")
    tool.send_command({
        'action': 'show_status_message',
        'status_type': 'caught_item',
        'player_name': '演示玩家',
        'item_name': '神秘大奖',
        'success_count': 1
    })
    time.sleep(3)
    
    # 6. 显示结束消息
    print("  📍 显示结束消息...")
    tool.send_command({
        'action': 'show_message',
        'text': '✨ 演示完成！工具正常运行中...',
        'animated': True,
        'duration': 5
    })
    
    print("\n🎭 演示序列完成！")
    print("\n📋 现在您可以:")
    print("  - 在 OBS 中观察显示效果")
    print("  - 修改 config_play.yaml 配置文件")
    print("  - 观察配置更改如何实时反映在显示中")
    print("  - 按 Ctrl+C 退出演示")
    
    try:
        # 保持运行状态
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 演示结束，程序退出")

if __name__ == "__main__":
    main()
