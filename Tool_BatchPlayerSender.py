#!/usr/bin/env python3
"""
批量玩家消息发送器
用于测试虚拟玩家功能，可以批量发送真实玩家请求，并自动在其中交错随机评论。
"""

import time
import logging
import random
import threading
import json
from typing import Optional, List, Dict, Any
from http.server import HTTPServer, BaseHTTPRequestHandler
from collections import deque

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchPlayerHTTPHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""

    def do_GET(self):
        """处理GET请求"""
        if self.path == "/game-da302d82":
            # 从消息队列中获取消息
            response_messages = []

            # 获取所有准备好的消息
            while BatchPlayerSender.message_queue:
                message = BatchPlayerSender.message_queue.popleft()
                response_messages.append(message)

            # 构造响应
            response_data = {
                "code": 0,
                "message": response_messages
            }

            # 发送响应
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))

            if response_messages:
                logger.info(f"✅ 已响应 {len(response_messages)} 条消息")
        else:
            self.send_error(404, "Not Found")

    def log_message(self, format, *args):
        """重写日志方法，减少输出"""
        # 忽略HTTP服务器的日志输出
        pass

class BatchPlayerSender:
    """批量玩家消息发送器 - 独立HTTP服务器模式"""

    message_queue = deque()  # 类变量，存储待发送的消息

    def __init__(self, port: int = 9999):
        self.port = port
        self.sent_players = []
        self.server = None
        self.server_thread = None

        # 玩家名生成配置
        self.name_prefixes = [
            "🌈", "☞", "₅₂₀💞", "🎀", "🌺", "🍵", "💞", "♂", "🌟", "💫",
            "🎯", "🎪", "🎨", "🎭", "🌸", "🌼", "🌻", "🌷", "🌹", "💎"
        ]

        self.name_cores = [
            "老魏", "一田静好", "刘", "礻守天着", "小明", "小红", "小李", "小王", "小张", "小陈",
            "阿强", "阿华", "阿伟", "阿杰", "阿峰", "阿斌", "阿军", "阿勇", "阿超", "阿辉",
            "晓雯", "晓敏", "晓丽", "晓燕", "晓霞", "晓娟", "晓芳", "晓玲", "晓慧", "晓琳",
            "志强", "志华", "志伟", "志杰", "志峰", "志斌", "志军", "志勇", "志超", "志辉",
            "美丽", "美华", "美玲", "美娟", "美芳", "美霞", "美燕", "美琳", "美慧", "美敏"
        ]

        self.name_suffixes = [
            "࿐", "꧔ꦿ᭄", "³", "*", "l", "🌺", "🍵", "💞", "♂", "🌟",
            "💫", "🎯", "🎪", "🎨", "🎭", "🌸", "🌼", "🌻", "🌷", "🌹"
        ]

        self.platforms = ["淘宝", "抖音", "快手", "微信", "QQ"]
        
        # 新增：用于生成随机评论的字符池
        self.comment_chars = list(
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
            "大家好欢迎来到直播间这个娃娃好可爱啊主播加油"
            "希望能抓到一个送给女朋友今天运气怎么样啊"
            "排队好久了什么时候到我啊冲冲冲祝我好运"
            "😂👍🔥🎉💖🎁😊🤔🤔🤔哇哦1234567890"
        )

    def generate_random_comment(self) -> str:
        """生成随机评论内容 (1-40个字符)"""
        length = random.randint(1, 40)
        return "".join(random.choices(self.comment_chars, k=length))

    def generate_player_name(self):
        """生成随机玩家名"""
        prefix = random.choice(self.name_prefixes)
        core = random.choice(self.name_cores)
        suffix = random.choice(self.name_suffixes)
        return f"{prefix}{core}{suffix}"

    def generate_test_players(self, count):
        """动态生成测试玩家数据"""
        players = []
        for i in range(count):
            players.append({
                "name": self.generate_player_name(),
                "id": f"auto_player_{i+1:03d}_{int(time.time())}"
            })
        return players
    
    def start_server(self):
        """启动HTTP服务器"""
        try:
            self.server = HTTPServer(('127.0.0.1', self.port), BatchPlayerHTTPHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            logger.info(f"✅ HTTP服务器已启动: http://127.0.0.1:{self.port}/game-da302d82")
            return True
        except Exception as e:
            logger.error(f"❌ 启动HTTP服务器失败: {e}")
            return False

    def stop_server(self):
        """停止HTTP服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            logger.info("✅ HTTP服务器已停止")

    def _prepare_and_interleave_messages(self, players: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        核心方法：为一批玩家生成消息包（游戏请求+评论），并将其交错成一个时间线。
        """
        if not players:
            return []

        # 1. 为每个玩家创建消息包
        all_player_message_packs = []
        for player in players:
            player_pack = []
            
            # a. 创建游戏请求 (1-2位数字)
            target_id = str(random.randint(1, 5))
            game_request_msg = {
                "type": "ChatMessage", "name": player["name"], "player": player["id"],
                "head_img": "img1", "content": target_id, "url": "url1", "plat": "test"
            }
            player_pack.append(game_request_msg)
            
            # b. 创建随机数量的评论 (0-10条)
            num_comments = random.randint(0, 10)
            for _ in range(num_comments):
                comment_content = self.generate_random_comment()
                # 确保评论不是1-2位纯数字，避免被误认为游戏请求
                if comment_content.isdigit() and len(comment_content) <= 2:
                    comment_content += "..."
                
                comment_msg = {
                    "type": "ChatMessage", "name": player["name"], "player": player["id"],
                    "head_img": "img1", "content": comment_content, "url": "url1", "plat": "test"
                }
                player_pack.append(comment_msg)
            
            # c. 随机打乱单个玩家内部的消息顺序
            random.shuffle(player_pack)
            all_player_message_packs.append(player_pack)

        # 2. 交错所有玩家的消息包，形成最终的时间线
        timeline = []
        while any(all_player_message_packs): # 只要还有任何一个包里有消息
            # 随机选择一个非空的消息包
            non_empty_packs = [pack for pack in all_player_message_packs if pack]
            chosen_pack = random.choice(non_empty_packs)
            
            # 从中取出一个消息，加入到时间线
            timeline.append(chosen_pack.pop(0))
            
        logger.info(f"为 {len(players)} 个玩家生成了 {len(timeline)} 条交错的消息。")
        return timeline

    def _add_messages_to_queue_with_delay(self, messages: List[Dict[str, Any]]):
        """将消息列表以微小随机延迟的方式添加到主队列"""
        if not messages:
            return
            
        for msg in messages:
            BatchPlayerSender.message_queue.append(msg)
            # 模拟真实发送的微小间隔
            time.sleep(random.uniform(0.05, 0.2))
        
        logger.info(f"已将 {len(messages)} 条消息添加到发送队列。")

    def send_single_player(self, player_name: str, player_id: str, target_id: Optional[str] = None) -> bool:
        """发送单个玩家（及其随机评论）"""
        try:
            player = [{"name": player_name, "id": player_id}]
            interleaved_messages = self._prepare_and_interleave_messages(player)
            self._add_messages_to_queue_with_delay(interleaved_messages)
            self.sent_players.append({"name": player_name, "id": player_id, "target": "N/A"})
            return True
        except Exception as e:
            logger.error(f"❌ 发送单个玩家时异常: {e}")
            return False
    
    def send_batch_players(self, count: int, interval: float = 2.0) -> int:
        """
        批量发送玩家请求。
        注意：这里的interval现在是批次间的间隔，而不是玩家间的间隔。
        """
        test_players = self.generate_test_players(count)
        interleaved_messages = self._prepare_and_interleave_messages(test_players)
        self._add_messages_to_queue_with_delay(interleaved_messages)
        
        logger.info(f"批量发送完成: 处理了 {count} 个玩家。")
        return count
    
    def send_gradual_players(self, max_count: int, batch_size: int = 2, batch_interval: float = 30.0):
        """逐步发送玩家，模拟真实场景"""
        logger.info(f"开始逐步发送玩家: 最多{max_count}个，每批{batch_size}个，间隔{batch_interval}秒")

        all_players = self.generate_test_players(max_count)
        sent_count = 0
        batch_num = 1

        while sent_count < max_count:
            current_batch_size = min(batch_size, max_count - sent_count)
            player_batch = all_players[sent_count : sent_count + current_batch_size]

            logger.info(f"发送第{batch_num}批玩家 ({len(player_batch)}个)...")
            
            # 为这一批玩家生成交错消息并发送
            interleaved_messages = self._prepare_and_interleave_messages(player_batch)
            self._add_messages_to_queue_with_delay(interleaved_messages)

            sent_count += len(player_batch)
            batch_num += 1

            if sent_count < max_count:
                logger.info(f"等待{batch_interval}秒后发送下一批...")
                time.sleep(batch_interval)
        
        logger.info(f"逐步发送完成，总计处理{sent_count}个玩家")
    
    def test_fill_mode(self):
        """测试填充模式 (真实玩家 < 2)"""
        print("\n" + "=" * 60)
        print("测试填充模式")
        print("=" * 60)
        print("发送1个真实玩家，观察虚拟玩家是否自动填充...")
        
        test_players = self.generate_test_players(1)
        # 直接调用核心逻辑
        interleaved_messages = self._prepare_and_interleave_messages(test_players)
        self._add_messages_to_queue_with_delay(interleaved_messages)
        
        #print("等待20秒观察虚拟玩家管理器响应...")
        #time.sleep(20)
        print("填充模式测试完成")
    
    def test_balance_mode(self):
        """测试平衡模式 (2 <= 真实玩家 <= 8)"""
        print("\n" + "=" * 60)
        print("测试平衡模式")
        print("=" * 60)
        print("发送8个真实玩家，观察虚拟玩家添加策略...")
        print("考虑到游戏循环5秒，发送间隔1秒确保队列中有足够玩家")

        test_players = self.generate_test_players(8)
        interleaved_messages = self._prepare_and_interleave_messages(test_players)
        self._add_messages_to_queue_with_delay(interleaved_messages)

        #print("等待40秒观察虚拟玩家平衡策略...")
        #time.sleep(40)
        print("平衡模式测试完成")
    
    def test_sparse_mode(self):
        """测试稀疏模式 (真实玩家 > 8)"""
        print("\n" + "=" * 60)
        print("测试稀疏模式")
        print("=" * 60)
        print("发送14个真实玩家，确保远超8人阈值，观察虚拟玩家退役...")
        print("间隔1秒发送，确保队列中有足够玩家触发稀疏模式")

        test_players = self.generate_test_players(14)
        interleaved_messages = self._prepare_and_interleave_messages(test_players)
        self._add_messages_to_queue_with_delay(interleaved_messages)

        #print("等待60秒观察稀疏模式响应（虚拟玩家退役）...")
        #time.sleep(60)
        print("稀疏模式测试完成")
    
    def test_heavy_load(self):
        """测试重负载场景 - 突发大量玩家请求"""
        print("\n" + "=" * 60)
        print("测试重负载场景")
        print("=" * 60)
        print("一次性发送60个真实玩家，测试系统突发负载处理能力...")
        print("虚拟池默认5个，间隔8个真实玩家插入1个虚拟玩家")
        print("60个真实玩家应该能耗尽虚拟池，启用备用名列表")
        print("无发送间隔，测试主程序面对突发重负载的处理能力")

        test_players = self.generate_test_players(60)
        print("开始一次性为60个玩家生成交错消息...")
        interleaved_messages = self._prepare_and_interleave_messages(test_players)
        print("开始将所有消息添加到队列...")
        self._add_messages_to_queue_with_delay(interleaved_messages)

        print("所有60个玩家的消息已全部进入队列！")
        #print("等待120秒观察重负载响应（虚拟玩家池扩充和系统稳定性）...")
        #time.sleep(120)
        print("重负载测试完成")
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        print("=" * 80)
        print("虚拟玩家功能全面测试 - 批量玩家发送器")
        print("=" * 80)
        
        try:
            # 测试各种场景
            self.test_fill_mode()
            self.test_balance_mode()
            self.test_sparse_mode()
            self.test_heavy_load()
            
            print("\n" + "=" * 80)
            print("全面测试完成!")
            print(f"总计发送了 {len(self.sent_players)} 个真实玩家请求")
            print("请检查主程序日志和数据库以验证虚拟玩家行为")
            print("=" * 80)
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            logger.error(f"测试过程中出错: {e}")

    def send_custom_message(self, name: str, player_id: str, content: str):
        """手动发送自定义消息"""
        msg = {
            "type": "ChatMessage",
            "name": name,
            "player": player_id,
            "head_img": "img1",
            "content": content,
            "url": "url1",
            "plat": "test"
        }
        BatchPlayerSender.message_queue.append(msg)
        logger.info(f"已手动发送自定义消息: {msg}")

def main():
    """主函数"""
    print("批量玩家消息发送器 - 独立HTTP服务器模式")
    print("用于测试虚拟玩家功能的各种场景")
    print()

    sender = BatchPlayerSender()

    # 启动HTTP服务器
    if not sender.start_server():
        print("❌ 无法启动HTTP服务器，程序退出")
        return

    print("✅ HTTP服务器已启动，现在可以接收play_main.py的轮询请求")
    print("📝 注意：请确保play_main.py配置的消息服务器端口为9999")

    try:
        while True:
            print("\n请选择测试模式:")
            print("1. 填充模式测试 (发送1个玩家)")
            print("2. 平衡模式测试 (发送8个玩家)")
            print("3. 稀疏模式测试 (发送14个玩家)")
            print("4. 重负载测试 (一次性发送60个玩家)")
            print("5. 全面测试 (依次执行所有测试)")
            print("6. 自定义批量发送")
            print("7. 逐步发送模式")
            print("8. 手动编辑并发送消息")  # 新增菜单项
            print("0. 退出")

            choice = input("\n请输入选择 (0-8): ").strip()

            if choice == "0":
                break
            elif choice == "1":
                sender.test_fill_mode()
            elif choice == "2":
                sender.test_balance_mode()
            elif choice == "3":
                sender.test_sparse_mode()
            elif choice == "4":
                sender.test_heavy_load()
            elif choice == "5":
                sender.run_comprehensive_test()
            elif choice == "6":
                try:
                    count = int(input("请输入要发送的玩家数量: "))
                    sender.send_batch_players(count, 0)
                except ValueError:
                    print("输入格式错误")
            elif choice == "7":
                try:
                    max_count = int(input("请输入最大玩家数量: "))
                    batch_size = int(input("请输入每批数量: "))
                    batch_interval = float(input("请输入批次间隔(秒): "))
                    sender.send_gradual_players(max_count, batch_size, batch_interval)
                except ValueError:
                    print("输入格式错误")
            elif choice == "8":
                # 支持一行输入三项，用空格分隔
                line = input("请输入 用户名 玩家ID 消息内容（用空格分隔）: ").strip()
                parts = line.split(" ", 2)  # 最多分割成3部分，内容允许有空格
                if len(parts) == 3:
                    name, player_id, content = parts
                    sender.send_custom_message(name, player_id, content)
                    print("自定义消息已发送。")
                else:
                    print("格式错误，请用空格分隔用户名、ID、内容")
            else:
                print("无效选择")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    finally:
        sender.stop_server()
        print("程序退出")

if __name__ == "__main__":
    main()
